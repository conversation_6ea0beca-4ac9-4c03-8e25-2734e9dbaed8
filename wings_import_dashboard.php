<?php
/**
 * Wings Import Dashboard - Easy Access Launcher
 * Simple launcher for the Wings API import with real-time progress
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    // If running standalone, include WordPress
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Include the main import script
require_once('wings_api_import_realtime.php');
?>
