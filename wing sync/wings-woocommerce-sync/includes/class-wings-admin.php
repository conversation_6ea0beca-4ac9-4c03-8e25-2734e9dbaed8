<?php
/**
 * Wings Admin Class
 * 
 * Handles admin interface and settings
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_Admin {

    /**
     * Admin instance
     */
    private static $instance = null;

    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_wings_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_wings_manual_sync', array($this, 'ajax_manual_sync'));
        add_action('wp_ajax_wings_clear_log', array($this, 'ajax_clear_log'));
        add_action('wp_ajax_wings_test_customer_sync', array($this, 'ajax_test_customer_sync'));

        // New AJAX actions for reorganized commands
        add_action('wp_ajax_wings_set_test_api_preset', array($this, 'ajax_set_test_api_preset'));
        add_action('wp_ajax_wings_test_product_connection', array($this, 'ajax_test_product_connection'));
        add_action('wp_ajax_wings_test_customer_connection', array($this, 'ajax_test_customer_connection'));
        add_action('wp_ajax_wings_sync_stock_only', array($this, 'ajax_sync_stock_only'));
        add_action('wp_ajax_wings_sync_prices_only', array($this, 'ajax_sync_prices_only'));
        add_action('wp_ajax_wings_retrieve_customers', array($this, 'ajax_retrieve_customers'));
        add_action('wp_ajax_wings_sync_customers', array($this, 'ajax_sync_customers'));
        add_action('wp_ajax_wings_send_unsynced_to_wings', array($this, 'ajax_send_unsynced_to_wings'));
        add_action('wp_ajax_wings_create_wp_customers', array($this, 'ajax_create_wp_customers'));
        add_action('wp_ajax_wings_generate_progress_report', array($this, 'ajax_generate_progress_report'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Wings Sync', 'wings-sync'),
            __('Wings Sync', 'wings-sync'),
            'manage_woocommerce',
            'wings-sync',
            array($this, 'admin_page')
        );
    }

    /**
     * Admin init
     */
    public function admin_init() {
        register_setting('wings_sync_settings', 'wings_sync_settings', array($this, 'sanitize_settings'));
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'woocommerce_page_wings-sync') {
            return;
        }

        wp_enqueue_script(
            'wings-admin-js',
            WINGS_SYNC_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            WINGS_SYNC_VERSION,
            true
        );

        wp_enqueue_style(
            'wings-admin-css',
            WINGS_SYNC_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            WINGS_SYNC_VERSION
        );

        wp_localize_script('wings-admin-js', 'wings_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wings_sync_nonce'),
            'strings' => array(
                'testing' => __('Testiranje...', 'wings-sync'),
                'syncing' => __('Sinhronizacija u toku...', 'wings-sync'),
                'success' => __('Uspešno!', 'wings-sync'),
                'error' => __('Greška!', 'wings-sync'),
                'confirm_clear_log' => __('Da li ste sigurni da želite da obrišete log?', 'wings-sync')
            )
        ));
    }

    /**
     * Admin page
     */
    public function admin_page() {
        $settings = Wings_WooCommerce_Sync::get_settings();
        $sync_stats = Wings_Sync::get_instance()->get_sync_stats();
        $log_entries = Wings_WooCommerce_Sync::get_log();
        
        include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/admin-display.php';
    }

    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();

        $sanitized['api_url'] = esc_url_raw($input['api_url'] ?? 'https://portal.wings.rs/api/v1/');
        $sanitized['api_alias'] = sanitize_text_field($input['api_alias'] ?? '');
        $sanitized['api_username'] = sanitize_text_field($input['api_username'] ?? '');
        $sanitized['api_password'] = sanitize_text_field($input['api_password'] ?? '');
        $sanitized['auto_sync'] = isset($input['auto_sync']) ? true : false;
        $sanitized['sync_interval'] = intval($input['sync_interval'] ?? 15);
        $sanitized['batch_size'] = intval($input['batch_size'] ?? 50);
        $sanitized['timeout'] = intval($input['timeout'] ?? 60);
        $sanitized['sync_enabled'] = isset($input['sync_enabled']) ? true : false;

        // Customer sync settings
        $sanitized['enable_customer_sync'] = isset($input['enable_customer_sync']) ? true : false;
        $sanitized['customer_sync_interval'] = sanitize_text_field($input['customer_sync_interval'] ?? 'hourly');
        $sanitized['customer_batch_size'] = intval($input['customer_batch_size'] ?? 50);

        // Keep existing last_sync value
        $existing_settings = Wings_WooCommerce_Sync::get_settings();
        $sanitized['last_sync'] = $existing_settings['last_sync'] ?? 0;

        // Update cron schedule if interval changed
        if ($sanitized['sync_interval'] !== ($existing_settings['sync_interval'] ?? 15)) {
            wp_clear_scheduled_hook('wings_sync_cron_hook');
            if ($sanitized['auto_sync']) {
                wp_schedule_event(time(), 'wings_sync_interval', 'wings_sync_cron_hook');
            }
        }

        // Schedule or unschedule cron based on auto_sync setting
        if ($sanitized['auto_sync'] && !$existing_settings['auto_sync']) {
            wp_schedule_event(time(), 'wings_sync_interval', 'wings_sync_cron_hook');
        } elseif (!$sanitized['auto_sync'] && $existing_settings['auto_sync']) {
            wp_clear_scheduled_hook('wings_sync_cron_hook');
        }

        return $sanitized;
    }

    /**
     * AJAX test connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        $api = Wings_API::get_instance();
        $result = $api->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => $result['message']
            ));
        }
    }

    /**
     * AJAX manual sync
     */
    public function ajax_manual_sync() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        // Increase time limit for sync
        set_time_limit(300); // 5 minutes

        $sync = Wings_Sync::get_instance();
        $result = $sync->sync_all_products();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Sinhronizacija uspešno završena!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX clear log
     */
    public function ajax_clear_log() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        Wings_WooCommerce_Sync::clear_log();

        wp_send_json_success(array(
            'message' => __('Log je uspešno obrisan.', 'wings-sync')
        ));
    }

    /**
     * AJAX test customer sync
     */
    public function ajax_test_customer_sync() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        // Include test file
        include_once WINGS_SYNC_PLUGIN_DIR . 'test-customer-sync.php';

        // Capture test output
        ob_start();
        run_wings_customer_sync_tests();
        $test_output = ob_get_clean();

        wp_send_json_success(array(
            'message' => __('Customer sync test completed.', 'wings-sync'),
            'output' => $test_output
        ));
    }

    /**
     * Add admin notices
     */
    public function admin_notices() {
        $screen = get_current_screen();
        
        if ($screen->id !== 'woocommerce_page_wings-sync') {
            return;
        }

        $settings = Wings_WooCommerce_Sync::get_settings();

        // Check if API is configured
        if (empty($settings['api_alias']) || empty($settings['api_username']) || empty($settings['api_password'])) {
            ?>
            <div class="notice notice-warning">
                <p><?php _e('Wings API nije potpuno konfigurisan. Molimo unesite sve potrebne podatke.', 'wings-sync'); ?></p>
            </div>
            <?php
        }

        // Check last sync
        $last_sync = $settings['last_sync'] ?? 0;
        if ($last_sync === 0) {
            ?>
            <div class="notice notice-info">
                <p><?php _e('Sinhronizacija još uvek nije pokretana. Kliknite na "Sinhronizuj sada" da pokrenete prvu sinhronizaciju.', 'wings-sync'); ?></p>
            </div>
            <?php
        } elseif ((time() - $last_sync) > 24 * 60 * 60) { // More than 24 hours
            ?>
            <div class="notice notice-warning">
                <p><?php _e('Poslednja sinhronizacija je bila pre više od 24 sata. Preporučujemo da pokrenete novu sinhronizaciju.', 'wings-sync'); ?></p>
            </div>
            <?php
        }
    }

    /**
     * Display sync status widget
     */
    public function display_sync_status() {
        $stats = Wings_Sync::get_instance()->get_sync_stats();
        $settings = Wings_WooCommerce_Sync::get_settings();

        ?>
        <div class="wings-sync-status">
            <h3><?php _e('Status sinhronizacije', 'wings-sync'); ?></h3>
            <table class="widefat">
                <tr>
                    <td><?php _e('Ukupno proizvoda:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($stats['total_products']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Sinhronizovani proizvodi:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($stats['synced_products']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Poslednja sinhronizacija:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($stats['last_sync_formatted']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Automatska sinhronizacija:', 'wings-sync'); ?></td>
                    <td><strong><?php echo $settings['auto_sync'] ? __('Uključena', 'wings-sync') : __('Isključena', 'wings-sync'); ?></strong></td>
                </tr>
                <?php if ($settings['auto_sync']): ?>
                <tr>
                    <td><?php _e('Interval sinhronizacije:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($settings['sync_interval']); ?> <?php _e('minuta', 'wings-sync'); ?></strong></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        <?php
    }

    /**
     * AJAX set test API preset
     */
    public function ajax_set_test_api_preset() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        $preset_type = sanitize_text_field($_POST['preset_type'] ?? '');

        $settings = Wings_WooCommerce_Sync::get_settings();

        // Set test environment values
        $settings['api_url'] = 'https://portal.wings.rs/api/v1/';
        $settings['api_alias'] = 'grosstest';
        $settings['api_username'] = 'aql';
        $settings['api_password'] = 'grossaql';

        if ($preset_type === 'customer') {
            $settings['enable_customer_sync'] = true;
            $settings['customer_sync_interval'] = 'hourly';
            $settings['customer_batch_size'] = 50;
        }

        Wings_WooCommerce_Sync::update_settings($settings);

        wp_send_json_success(array(
            'message' => __('Test API podešavanja su uspešno postavljena.', 'wings-sync'),
            'preset_type' => $preset_type
        ));
    }

    /**
     * AJAX test product connection
     */
    public function ajax_test_product_connection() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        $api = Wings_API::get_instance();

        // Test basic connection
        $connection_result = $api->test_connection();
        if (is_wp_error($connection_result)) {
            wp_send_json_error(array(
                'message' => $connection_result->get_error_message()
            ));
        }

        // Test product retrieval
        $products_result = $api->get_products(0, 5);
        if (is_wp_error($products_result)) {
            wp_send_json_error(array(
                'message' => __('Konekcija uspešna, ali preuzimanje proizvoda neuspešno: ', 'wings-sync') . $products_result->get_error_message()
            ));
        }

        wp_send_json_success(array(
            'message' => __('Test konekcije za proizvode uspešan!', 'wings-sync'),
            'products_count' => count($products_result['products'] ?? [])
        ));
    }

    /**
     * AJAX test customer connection
     */
    public function ajax_test_customer_connection() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        $api = Wings_API::get_instance();

        // Test basic connection
        $connection_result = $api->test_connection();
        if (is_wp_error($connection_result)) {
            wp_send_json_error(array(
                'message' => $connection_result->get_error_message()
            ));
        }

        // Test customer retrieval
        $customers_result = $api->get_customers(0, 5);
        if (is_wp_error($customers_result)) {
            wp_send_json_error(array(
                'message' => __('Konekcija uspešna, ali preuzimanje kupaca neuspešno: ', 'wings-sync') . $customers_result->get_error_message()
            ));
        }

        wp_send_json_success(array(
            'message' => __('Test konekcije za kupce uspešan!', 'wings-sync'),
            'customers_count' => count($customers_result['customers'] ?? [])
        ));
    }

    /**
     * AJAX sync stock only
     */
    public function ajax_sync_stock_only() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        set_time_limit(300);

        $sync = Wings_Sync::get_instance();
        $result = $sync->sync_stock_only();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Sinhronizacija zaliha uspešno završena!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX sync prices only
     */
    public function ajax_sync_prices_only() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        set_time_limit(300);

        $sync = Wings_Sync::get_instance();
        $result = $sync->sync_prices_only();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Sinhronizacija cena uspešno završena!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX retrieve customers
     */
    public function ajax_retrieve_customers() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        set_time_limit(300);

        $batch_size = intval($_POST['batch_size'] ?? 50);
        $date_from = sanitize_text_field($_POST['date_from'] ?? '');
        $date_to = sanitize_text_field($_POST['date_to'] ?? '');
        $retrieve_type = sanitize_text_field($_POST['retrieve_type'] ?? 'all');

        $customer_sync = new Wings_Customer_Sync();
        $result = $customer_sync->retrieve_customers_from_wings($retrieve_type, $batch_size, $date_from, $date_to);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Preuzimanje kupaca uspešno završeno!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX sync customers
     */
    public function ajax_sync_customers() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        set_time_limit(300);

        $sync_type = sanitize_text_field($_POST['sync_type'] ?? 'all');

        $customer_sync = new Wings_Customer_Sync();
        $result = $customer_sync->sync_customers($sync_type);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Sinhronizacija kupaca uspešno završena!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX send unsynced to Wings
     */
    public function ajax_send_unsynced_to_wings() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        set_time_limit(300);

        $include_guest = isset($_POST['include_guest']) && $_POST['include_guest'] === 'true';
        $include_registered = isset($_POST['include_registered']) && $_POST['include_registered'] === 'true';
        $validate_before_send = isset($_POST['validate_before_send']) && $_POST['validate_before_send'] === 'true';

        $customer_sync = new Wings_Customer_Sync();
        $result = $customer_sync->send_unsynced_customers_to_wings($include_guest, $include_registered, $validate_before_send);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Slanje nesinhronizovanih kupaca uspešno završeno!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX create WP customers
     */
    public function ajax_create_wp_customers() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        set_time_limit(300);

        $send_welcome_email = isset($_POST['send_welcome_email']) && $_POST['send_welcome_email'] === 'true';
        $generate_random_password = isset($_POST['generate_random_password']) && $_POST['generate_random_password'] === 'true';
        $set_customer_role = isset($_POST['set_customer_role']) && $_POST['set_customer_role'] === 'true';

        $customer_sync = new Wings_Customer_Sync();
        $result = $customer_sync->create_wp_customers_from_wings($send_welcome_email, $generate_random_password, $set_customer_role);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Kreiranje WP kupaca uspešno završeno!', 'wings-sync'),
                'stats' => $result['stats'],
                'execution_time' => $result['execution_time']
            ));
        }
    }

    /**
     * AJAX generate progress report
     */
    public function ajax_generate_progress_report() {
        check_ajax_referer('wings_sync_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-sync'));
        }

        $report_type = sanitize_text_field($_POST['report_type'] ?? 'sync');

        $report_data = array();

        switch ($report_type) {
            case 'sync':
                $sync_stats = Wings_Sync::get_instance()->get_sync_stats();
                $report_data = array(
                    'type' => 'sync_report',
                    'generated_at' => current_time('mysql'),
                    'stats' => $sync_stats,
                    'recent_activity' => array_slice(Wings_WooCommerce_Sync::get_log(), -20)
                );
                break;

            case 'performance':
                $report_data = array(
                    'type' => 'performance_report',
                    'generated_at' => current_time('mysql'),
                    'avg_sync_time' => get_option('wings_avg_sync_time', 0),
                    'api_calls_today' => get_option('wings_api_calls_today', 0),
                    'success_rate' => get_option('wings_api_success_rate', 0),
                    'memory_usage' => memory_get_peak_usage(true),
                    'php_version' => PHP_VERSION,
                    'wp_version' => get_bloginfo('version')
                );
                break;

            case 'errors':
                $error_logs = array_filter(Wings_WooCommerce_Sync::get_log(), function($entry) {
                    return $entry['type'] === 'error';
                });
                $report_data = array(
                    'type' => 'error_report',
                    'generated_at' => current_time('mysql'),
                    'total_errors' => count($error_logs),
                    'recent_errors' => array_slice($error_logs, -50),
                    'error_summary' => $this->generate_error_summary($error_logs)
                );
                break;
        }

        wp_send_json_success(array(
            'message' => __('Izveštaj uspešno generisan!', 'wings-sync'),
            'report' => $report_data,
            'download_url' => $this->generate_report_download_url($report_data)
        ));
    }

    /**
     * Generate error summary
     */
    private function generate_error_summary($error_logs) {
        $summary = array();
        foreach ($error_logs as $error) {
            $key = substr($error['message'], 0, 50);
            if (!isset($summary[$key])) {
                $summary[$key] = 0;
            }
            $summary[$key]++;
        }
        arsort($summary);
        return array_slice($summary, 0, 10, true);
    }

    /**
     * Generate report download URL
     */
    private function generate_report_download_url($report_data) {
        $filename = 'wings-sync-report-' . date('Y-m-d-H-i-s') . '.json';
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['path'] . '/' . $filename;

        file_put_contents($file_path, json_encode($report_data, JSON_PRETTY_PRINT));

        return $upload_dir['url'] . '/' . $filename;
    }
}
