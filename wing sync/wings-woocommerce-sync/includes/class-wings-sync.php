<?php
/**
 * Wings Sync Class
 * 
 * Handles synchronization between Wings and WooCommerce
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_Sync {

    /**
     * Sync instance
     */
    private static $instance = null;

    /**
     * Wings API instance
     */
    private $api;

    /**
     * Sync settings
     */
    private $settings;

    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->api = Wings_API::get_instance();
        $this->settings = Wings_WooCommerce_Sync::get_settings();
    }

    /**
     * Perform full synchronization
     */
    public function sync_all_products($warehouse_id = null) {
        $start_time = microtime(true);
        $sync_stats = array(
            'total_processed' => 0,
            'updated' => 0,
            'created' => 0,
            'errors' => 0,
            'skipped' => 0
        );

        Wings_WooCommerce_Sync::log('Pokretanje potpune sinhronizacije...', 'info');

        try {
            // Get all articles from Wings
            $articles = $this->api->get_all_articles($warehouse_id, $this->settings['batch_size'] ?? 50);
            
            if (is_wp_error($articles)) {
                Wings_WooCommerce_Sync::log('Greška pri preuzimanju artikala: ' . $articles->get_error_message(), 'error');
                return $articles;
            }

            $total_articles = count($articles);
            Wings_WooCommerce_Sync::log('Preuzeto ' . $total_articles . ' artikala za sinhronizaciju', 'info');

            // Process each article
            foreach ($articles as $index => $article) {
                $sync_stats['total_processed']++;
                
                $result = $this->sync_single_product($article);
                
                if (is_wp_error($result)) {
                    $sync_stats['errors']++;
                    Wings_WooCommerce_Sync::log('Greška pri sinhronizaciji artikla ID ' . ($article['id'] ?? 'N/A') . ': ' . $result->get_error_message(), 'error');
                } elseif ($result['action'] === 'updated') {
                    $sync_stats['updated']++;
                } elseif ($result['action'] === 'created') {
                    $sync_stats['created']++;
                } else {
                    $sync_stats['skipped']++;
                }

                // Progress logging every 10 items
                if (($index + 1) % 10 === 0) {
                    Wings_WooCommerce_Sync::log('Obrađeno ' . ($index + 1) . '/' . $total_articles . ' artikala', 'info');
                }
            }

            $execution_time = round(microtime(true) - $start_time, 2);
            
            // Update last sync timestamp
            $this->settings['last_sync'] = time();
            Wings_WooCommerce_Sync::update_settings($this->settings);

            // Log final results
            $message = sprintf(
                'Sinhronizacija završena. Obrađeno: %d, Ažurirano: %d, Kreirano: %d, Preskočeno: %d, Greške: %d. Vreme izvršavanja: %s sekundi.',
                $sync_stats['total_processed'],
                $sync_stats['updated'],
                $sync_stats['created'],
                $sync_stats['skipped'],
                $sync_stats['errors'],
                $execution_time
            );
            
            Wings_WooCommerce_Sync::log($message, 'info');

            return array(
                'success' => true,
                'stats' => $sync_stats,
                'execution_time' => $execution_time
            );

        } catch (Exception $e) {
            Wings_WooCommerce_Sync::log('Kritična greška tokom sinhronizacije: ' . $e->getMessage(), 'error');
            return new WP_Error('sync_exception', $e->getMessage());
        }
    }

    /**
     * Sync single product
     */
    public function sync_single_product($article) {
        if (!isset($article['attributes'])) {
            return new WP_Error('invalid_article', 'Artikal nema attributes podatke');
        }

        $attrs = $article['attributes'];
        $wings_id = $article['id'] ?? '';
        $sku = $attrs['sifra'] ?? '';

        if (empty($wings_id) || empty($sku)) {
            return new WP_Error('missing_data', 'Nedostaju osnovni podaci (ID ili šifra)');
        }

        // Find existing product
        $product = $this->find_product_by_wings_data($wings_id, $sku);
        $action = 'updated';

        if (!$product) {
            // Create new product
            $product = new WC_Product_Simple();
            $action = 'created';
        }

        // Map Wings data to WooCommerce product
        $this->map_product_data($product, $article);

        // Save product
        $product_id = $product->save();

        if (!$product_id) {
            return new WP_Error('save_failed', 'Neuspešno čuvanje proizvoda');
        }

        // Update meta data
        update_post_meta($product_id, '_wings_id', $wings_id);
        update_post_meta($product_id, '_wings_sku', $sku);
        update_post_meta($product_id, '_wings_last_sync', time());

        return array(
            'action' => $action,
            'product_id' => $product_id,
            'wings_id' => $wings_id,
            'sku' => $sku
        );
    }

    /**
     * Find product by Wings data
     */
    private function find_product_by_wings_data($wings_id, $sku) {
        // First try to find by Wings ID
        $products = get_posts(array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_wings_id',
                    'value' => $wings_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        ));

        if (!empty($products)) {
            return wc_get_product($products[0]->ID);
        }

        // Try to find by SKU
        $product_id = wc_get_product_id_by_sku($sku);
        if ($product_id) {
            return wc_get_product($product_id);
        }

        return null;
    }

    /**
     * Map Wings article data to WooCommerce product
     */
    private function map_product_data($product, $article) {
        $attrs = $article['attributes'];

        // Basic product data
        $product->set_name($attrs['naziv'] ?? '');
        $product->set_sku($attrs['sifra'] ?? '');
        $product->set_description($attrs['napomena'] ?? '');

        // Price mapping based on user role or settings
        $price = $this->get_product_price($attrs);
        if ($price > 0) {
            $product->set_regular_price($price);
            $product->set_price($price);
        }

        // Stock management
        $product->set_manage_stock(true);
        
        $quantity = floatval($attrs['kolicina'] ?? 0);
        $reserved = floatval($attrs['rezervacija'] ?? 0);
        $available_stock = max(0, $quantity - $reserved);
        
        $product->set_stock_quantity($available_stock);
        $product->set_stock_status($available_stock > 0 ? 'instock' : 'outofstock');

        // Additional attributes
        if (!empty($attrs['barkod'])) {
            update_post_meta($product->get_id(), '_wings_barcode', $attrs['barkod']);
        }

        if (!empty($attrs['proizvodjac'])) {
            update_post_meta($product->get_id(), '_wings_manufacturer', $attrs['proizvodjac']);
        }

        if (!empty($attrs['vrsta'])) {
            update_post_meta($product->get_id(), '_wings_category', $attrs['vrsta']);
        }

        if (!empty($attrs['jm'])) {
            update_post_meta($product->get_id(), '_wings_unit', $attrs['jm']);
        }

        // Set product status
        $product->set_status('publish');
        $product->set_catalog_visibility('visible');

        // Tax settings
        if (isset($attrs['porez']) && $attrs['porez'] > 0) {
            $product->set_tax_status('taxable');
            $product->set_tax_class('');
        }
    }

    /**
     * Get product price based on settings
     */
    private function get_product_price($attrs) {
        // Default to regular price
        $price = floatval($attrs['cena'] ?? 0);

        // Check for retail price if available
        if (isset($attrs['cenamp']) && $attrs['cenamp'] > 0) {
            $price = floatval($attrs['cenamp']);
        }

        // Apply discount if available
        if (isset($attrs['rabat']) && $attrs['rabat'] > 0) {
            $discount = floatval($attrs['rabat']);
            $price = $price * (1 - $discount / 100);
        }

        return $price;
    }

    /**
     * Sync specific products by SKU
     */
    public function sync_products_by_sku($skus) {
        if (!is_array($skus)) {
            $skus = array($skus);
        }

        $results = array();

        foreach ($skus as $sku) {
            // Find article in Wings by SKU
            $articles = $this->api->get_articles(0, 1);
            
            if (is_wp_error($articles)) {
                $results[$sku] = $articles;
                continue;
            }

            // This is a simplified approach - in real implementation,
            // you might need to search through all articles or use a specific API endpoint
            $found = false;
            foreach ($articles['articles'] as $article) {
                if (($article['attributes']['sifra'] ?? '') === $sku) {
                    $results[$sku] = $this->sync_single_product($article);
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $results[$sku] = new WP_Error('not_found', 'Artikal sa SKU ' . $sku . ' nije pronađen u Wings sistemu');
            }
        }

        return $results;
    }

    /**
     * Get sync statistics
     */
    public function get_sync_stats() {
        $total_products = wp_count_posts('product')->publish;
        
        $synced_products = get_posts(array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_wings_id',
                    'compare' => 'EXISTS'
                )
            ),
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));

        $last_sync = $this->settings['last_sync'] ?? 0;

        return array(
            'total_products' => $total_products,
            'synced_products' => count($synced_products),
            'last_sync' => $last_sync,
            'last_sync_formatted' => $last_sync ? date('Y-m-d H:i:s', $last_sync) : 'Nikad'
        );
    }
}
