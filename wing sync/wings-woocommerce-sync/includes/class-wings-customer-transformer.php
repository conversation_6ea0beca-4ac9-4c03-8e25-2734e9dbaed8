<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Pure functions for transforming customer data between Wings Portal and WooCommerce formats
 */
class Wings_Customer_Transformer {

    /**
     * Transform Wings customer data to WooCommerce customer format
     * Pure function - no side effects
     *
     * @param array $wings_customer Wings customer data
     * @return array WooCommerce customer data
     */
    public static function wings_to_woocommerce($wings_customer) {
        if (empty($wings_customer) || !is_array($wings_customer)) {
            return array();
        }

        $wc_customer = array();

        // Extract attributes if they exist (Wings API structure)
        $attributes = $wings_customer['attributes'] ?? $wings_customer;

        // Get basic info from attributes
        $naziv = sanitize_text_field($attributes['naziv'] ?? '');
        $email = sanitize_email($attributes['email'] ?? '');
        $kontakt = sanitize_text_field($attributes['kontakt'] ?? '');
        $customer_id = sanitize_text_field($wings_customer['id'] ?? $attributes['sifra'] ?? '');

        // Handle missing email
        $email = sanitize_email($attributes['email'] ?? '');
        if (empty($email)) {
            $email = self::generate_customer_email($attributes);
        }
        
        // Extract name parts from 'naziv'
        $naziv = sanitize_text_field($attributes['naziv'] ?? '');
        $name_parts = self::parse_company_name($naziv);
        
        $wc_customer['user_email'] = $email;
        $wc_customer['display_name'] = $naziv;
        $wc_customer['first_name'] = $name_parts['first_name'];
        $wc_customer['last_name'] = $name_parts['last_name'];
        
        // B2B specific fields
        $wc_customer['billing'] = array(
            'company' => $naziv,
            'address_1' => sanitize_text_field($attributes['adresa'] ?? ''),
            'city' => sanitize_text_field($attributes['mesto'] ?? ''),
            'phone' => sanitize_text_field($attributes['telefon'] ?? $attributes['mobilni'] ?? ''),
            'email' => $email,
            'country' => 'RS',
        );
        
        return $wc_customer;
    }

    /**
     * Transform WooCommerce customer data to Wings customer format
     * Pure function - no side effects
     * 
     * @param array $wc_customer WooCommerce customer data
     * @return array Wings customer data
     */
    public static function woocommerce_to_wings($wc_customer) {
        if (empty($wc_customer) || !is_array($wc_customer)) {
            return array();
        }

        $wings_customer = array();

        // Extract billing info
        $billing = $wc_customer['billing'] ?? array();
        
        // Basic customer info
        $wings_customer['naziv'] = sanitize_text_field(
            $billing['company'] ?? 
            trim(($billing['first_name'] ?? '') . ' ' . ($billing['last_name'] ?? '')) ??
            $wc_customer['display_name'] ?? ''
        );
        
        $wings_customer['email'] = sanitize_email($wc_customer['user_email'] ?? $billing['email'] ?? '');
        $wings_customer['adresa'] = sanitize_text_field($billing['address_1'] ?? '');
        $wings_customer['mesto'] = sanitize_text_field($billing['city'] ?? '');
        $wings_customer['telefon'] = sanitize_text_field($billing['phone'] ?? '');

        // Extract Wings-specific metadata
        $meta_data = $wc_customer['meta_data'] ?? array();
        if (is_array($meta_data)) {
            foreach ($meta_data as $meta) {
                if (is_array($meta) && isset($meta['key'], $meta['value'])) {
                    switch ($meta['key']) {
                        case 'wings_customer_code':
                            $wings_customer['sifra'] = sanitize_text_field($meta['value']);
                            break;
                        case 'wings_pib':
                            $wings_customer['pib'] = sanitize_text_field($meta['value']);
                            break;
                        case 'wings_status':
                            $wings_customer['status'] = sanitize_text_field($meta['value']);
                            break;
                        case 'wings_sales_rep':
                            $wings_customer['komercijalista'] = sanitize_text_field($meta['value']);
                            break;
                        case 'wings_customer_class':
                            $wings_customer['klasa'] = sanitize_text_field($meta['value']);
                            break;
                    }
                }
            }
        }

        // Set defaults for required Wings fields
        if (empty($wings_customer['status'])) {
            $wings_customer['status'] = 'aktivan';
        }

        return $wings_customer;
    }

    /**
     * Validate Wings customer data
     * Pure function - no side effects
     * 
     * @param array $wings_customer Wings customer data
     * @return array Validation result ['valid' => bool, 'errors' => array]
     */
    public static function validate_wings_customer($wings_customer) {
        $errors = array();

        // Required fields
        if (empty($wings_customer['naziv'])) {
            $errors[] = 'Naziv kupca je obavezan';
        }

        if (empty($wings_customer['email']) || !is_email($wings_customer['email'])) {
            $errors[] = 'Validna email adresa je obavezna';
        }

        // Optional but recommended fields
        if (empty($wings_customer['adresa'])) {
            $errors[] = 'Adresa je preporučena';
        }

        if (empty($wings_customer['telefon'])) {
            $errors[] = 'Telefon je preporučen';
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Validate WooCommerce customer data
     * Pure function - no side effects
     * 
     * @param array $wc_customer WooCommerce customer data
     * @return array Validation result ['valid' => bool, 'errors' => array]
     */
    public static function validate_woocommerce_customer($wc_customer) {
        $errors = array();

        // Required fields
        if (empty($wc_customer['user_email']) || !is_email($wc_customer['user_email'])) {
            $errors[] = 'Valid email address is required';
        }

        if (empty($wc_customer['display_name']) && 
            empty($wc_customer['first_name']) && 
            empty($wc_customer['last_name'])) {
            $errors[] = 'Customer name is required';
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Extract first name from full name
     * Pure function - no side effects
     * 
     * @param string $full_name Full name
     * @return string First name
     */
    private static function extract_first_name($full_name) {
        if (empty($full_name)) {
            return '';
        }

        $parts = explode(' ', trim($full_name));
        return sanitize_text_field($parts[0] ?? '');
    }

    /**
     * Extract last name from full name
     * Pure function - no side effects
     * 
     * @param string $full_name Full name
     * @return string Last name
     */
    private static function extract_last_name($full_name) {
        if (empty($full_name)) {
            return '';
        }

        $parts = explode(' ', trim($full_name));
        if (count($parts) > 1) {
            array_shift($parts); // Remove first name
            return sanitize_text_field(implode(' ', $parts));
        }

        return '';
    }

    /**
     * Compare two customer records to determine if sync is needed
     * Pure function - no side effects
     * 
     * @param array $wings_customer Wings customer data
     * @param array $wc_customer WooCommerce customer data
     * @return array Comparison result ['needs_sync' => bool, 'differences' => array]
     */
    public static function compare_customers($wings_customer, $wc_customer) {
        $differences = array();

        // Convert Wings to WC format for comparison
        $wings_as_wc = self::wings_to_woocommerce($wings_customer);

        // Compare key fields
        $fields_to_compare = array(
            'user_email' => 'Email',
            'display_name' => 'Name',
        );

        foreach ($fields_to_compare as $field => $label) {
            $wings_value = $wings_as_wc[$field] ?? '';
            $wc_value = $wc_customer[$field] ?? '';
            
            if ($wings_value !== $wc_value) {
                $differences[$field] = array(
                    'label' => $label,
                    'wings' => $wings_value,
                    'woocommerce' => $wc_value
                );
            }
        }

        // Compare billing fields
        $billing_fields = array(
            'address_1' => 'Address',
            'city' => 'City',
            'phone' => 'Phone',
        );

        $wings_billing = $wings_as_wc['billing'] ?? array();
        $wc_billing = $wc_customer['billing'] ?? array();

        foreach ($billing_fields as $field => $label) {
            $wings_value = $wings_billing[$field] ?? '';
            $wc_value = $wc_billing[$field] ?? '';
            
            if ($wings_value !== $wc_value) {
                $differences["billing_{$field}"] = array(
                    'label' => "Billing {$label}",
                    'wings' => $wings_value,
                    'woocommerce' => $wc_value
                );
            }
        }

        return array(
            'needs_sync' => !empty($differences),
            'differences' => $differences
        );
    }

    /**
     * Generate email for customers without email
     */
    private static function generate_customer_email($customer_data) {
        $sifra = $customer_data['sifra'] ?? '';
        $naziv = $customer_data['naziv'] ?? '';
        
        // Option 1: Use customer code
        if (!empty($sifra)) {
            return strtolower($sifra) . '@wings-import.local';
        }
        
        // Option 2: Use sanitized company name
        if (!empty($naziv)) {
            $clean_name = sanitize_title($naziv);
            return $clean_name . '@wings-import.local';
        }
        
        // Fallback
        return 'customer-' . uniqid() . '@wings-import.local';
    }

    private static function parse_company_name($naziv) {
        // Extract contact person if exists
        if (preg_match('/^(.+?)\s*-\s*(.+)$/', $naziv, $matches)) {
            return [
                'first_name' => trim($matches[2]),
                'last_name' => '',
                'company' => trim($matches[1])
            ];
        }
        
        return [
            'first_name' => $naziv,
            'last_name' => '',
            'company' => $naziv
        ];
    }
}








