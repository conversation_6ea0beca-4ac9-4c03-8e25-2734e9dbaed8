<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Wings Customer Sync - Main synchronization class
 */
class Wings_Customer_Sync {

    /**
     * @var Wings_API
     */
    private $api;

    /**
     * @var array
     */
    private $settings;

    /**
     * Constructor
     */
    public function __construct() {
        $this->api = Wings_API::get_instance();
        $this->settings = Wings_WooCommerce_Sync::get_settings();
        
        $this->init_hooks();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // WooCommerce customer hooks
        add_action('woocommerce_created_customer', array($this, 'on_customer_created'), 10, 3);
        add_action('woocommerce_customer_save_address', array($this, 'on_customer_address_updated'), 10, 2);
        add_action('woocommerce_save_account_details', array($this, 'on_customer_account_updated'), 10, 1);
        
        // Admin hooks
        add_action('wings_sync_customers_cron', array($this, 'sync_customers_from_wings'));
        
        // Schedule customer sync if enabled
        if ($this->is_customer_sync_enabled()) {
            $this->schedule_customer_sync();
        }
    }

    /**
     * Check if customer sync is enabled
     * 
     * @return bool
     */
    private function is_customer_sync_enabled() {
        return !empty($this->settings['enable_customer_sync']);
    }

    /**
     * Schedule customer sync cron job
     */
    private function schedule_customer_sync() {
        if (!wp_next_scheduled('wings_sync_customers_cron')) {
            $interval = $this->settings['customer_sync_interval'] ?? 'hourly';
            wp_schedule_event(time(), $interval, 'wings_sync_customers_cron');
        }
    }

    /**
     * Handle new customer creation in WooCommerce
     * 
     * @param int $customer_id WooCommerce customer ID
     * @param array $new_customer_data Customer data
     * @param string $password_generated Whether password was generated
     */
    public function on_customer_created($customer_id, $new_customer_data, $password_generated) {
        if (!$this->is_customer_sync_enabled()) {
            return;
        }

        Wings_WooCommerce_Sync::log("Novi kupac kreiran u WooCommerce: {$customer_id}", 'info');

        // Get full customer data
        $wc_customer = $this->get_woocommerce_customer_data($customer_id);
        if (empty($wc_customer)) {
            Wings_WooCommerce_Sync::log("Nije moguće preuzeti podatke za kupca: {$customer_id}", 'error');
            return;
        }

        // Transform to Wings format
        $wings_customer = Wings_Customer_Transformer::woocommerce_to_wings($wc_customer);
        
        // Validate data
        $validation = Wings_Customer_Transformer::validate_wings_customer($wings_customer);
        if (!$validation['valid']) {
            Wings_WooCommerce_Sync::log("Validacija neuspešna za kupca {$customer_id}: " . implode(', ', $validation['errors']), 'error');
            return;
        }

        // Create customer in Wings
        $result = $this->create_customer_in_wings($wings_customer);
        if (!is_wp_error($result)) {
            // Store Wings customer ID in WooCommerce
            update_user_meta($customer_id, 'wings_customer_id', $result);
            update_user_meta($customer_id, 'wings_last_sync', current_time('mysql'));
            
            Wings_WooCommerce_Sync::log("Kupac uspešno kreiran u Wings sa ID: {$result}", 'info');
        } else {
            Wings_WooCommerce_Sync::log("Greška pri kreiranju kupca u Wings: " . $result->get_error_message(), 'error');
        }
    }

    /**
     * Handle customer address update
     * 
     * @param int $user_id Customer ID
     * @param string $load_address Address type (billing/shipping)
     */
    public function on_customer_address_updated($user_id, $load_address) {
        if (!$this->is_customer_sync_enabled() || $load_address !== 'billing') {
            return;
        }

        Wings_WooCommerce_Sync::log("Adresa kupca ažurirana: {$user_id}", 'info');
        $this->sync_customer_to_wings($user_id);
    }

    /**
     * Handle customer account details update
     * 
     * @param int $user_id Customer ID
     */
    public function on_customer_account_updated($user_id) {
        if (!$this->is_customer_sync_enabled()) {
            return;
        }

        Wings_WooCommerce_Sync::log("Podaci kupca ažurirani: {$user_id}", 'info');
        $this->sync_customer_to_wings($user_id);
    }

    /**
     * Sync customer from WooCommerce to Wings
     * 
     * @param int $customer_id WooCommerce customer ID
     * @return bool|WP_Error
     */
    private function sync_customer_to_wings($customer_id) {
        $wc_customer = $this->get_woocommerce_customer_data($customer_id);
        if (empty($wc_customer)) {
            return new WP_Error('no_customer_data', 'Customer data not found');
        }

        $wings_customer_id = get_user_meta($customer_id, 'wings_customer_id', true);
        
        if (empty($wings_customer_id)) {
            // Customer doesn't exist in Wings, create new
            return $this->on_customer_created($customer_id, $wc_customer, false);
        }

        // TODO: Implement customer update in Wings
        // For now, just log that update is needed
        Wings_WooCommerce_Sync::log("Potrebno je ažurirati kupca u Wings: {$wings_customer_id}", 'info');
        
        return true;
    }

    /**
     * Sync customers from Wings to WooCommerce (scheduled task)
     */
    public function sync_customers_from_wings() {
        if (!$this->is_customer_sync_enabled()) {
            return;
        }

        Wings_WooCommerce_Sync::log('Pokretanje sinhronizacije kupaca iz Wings-a', 'info');

        $batch_size = $this->settings['customer_batch_size'] ?? 50;
        $customers = $this->api->get_all_customers(array(), $batch_size);

        if (is_wp_error($customers)) {
            Wings_WooCommerce_Sync::log('Greška pri preuzimanju kupaca iz Wings-a: ' . $customers->get_error_message(), 'error');
            return;
        }

        $synced_count = 0;
        $created_count = 0;
        $updated_count = 0;

        foreach ($customers as $wings_customer) {
            $result = $this->sync_wings_customer_to_woocommerce($wings_customer);
            
            if (!is_wp_error($result)) {
                $synced_count++;
                if ($result['action'] === 'created') {
                    $created_count++;
                } elseif ($result['action'] === 'updated') {
                    $updated_count++;
                }
            }
        }

        Wings_WooCommerce_Sync::log("Sinhronizacija završena. Ukupno: {$synced_count}, Kreirano: {$created_count}, Ažurirano: {$updated_count}", 'info');
    }

    /**
     * Sync single Wings customer to WooCommerce
     * 
     * @param array $wings_customer Wings customer data
     * @return array|WP_Error Result with action taken
     */
    private function sync_wings_customer_to_woocommerce($wings_customer) {
        // Transform Wings customer to WooCommerce format
        $wc_customer_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);
        
        // Validate data
        $validation = Wings_Customer_Transformer::validate_woocommerce_customer($wc_customer_data);
        if (!$validation['valid']) {
            return new WP_Error('validation_failed', 'Customer validation failed: ' . implode(', ', $validation['errors']));
        }

        // Check if customer already exists in WooCommerce
        $existing_customer = $this->find_woocommerce_customer_by_email($wc_customer_data['user_email']);
        
        if ($existing_customer) {
            // Update existing customer
            $result = $this->update_woocommerce_customer($existing_customer->ID, $wc_customer_data);
            return array('action' => 'updated', 'customer_id' => $existing_customer->ID, 'result' => $result);
        } else {
            // Create new customer with professionals role
            $customer_id = $this->create_woocommerce_customer_with_role($wc_customer_data, 'professionals');
            if (!is_wp_error($customer_id)) {
                return array('action' => 'created', 'customer_id' => $customer_id);
            }
            return $customer_id;
        }
    }

    /**
     * Get WooCommerce customer data
     * 
     * @param int $customer_id Customer ID
     * @return array|null Customer data
     */
    private function get_woocommerce_customer_data($customer_id) {
        $customer = new WC_Customer($customer_id);
        
        if (!$customer->get_id()) {
            return null;
        }

        return array(
            'user_email' => $customer->get_email(),
            'display_name' => $customer->get_display_name(),
            'first_name' => $customer->get_first_name(),
            'last_name' => $customer->get_last_name(),
            'billing' => array(
                'first_name' => $customer->get_billing_first_name(),
                'last_name' => $customer->get_billing_last_name(),
                'company' => $customer->get_billing_company(),
                'address_1' => $customer->get_billing_address_1(),
                'city' => $customer->get_billing_city(),
                'phone' => $customer->get_billing_phone(),
                'email' => $customer->get_billing_email(),
                'country' => $customer->get_billing_country(),
            ),
            'meta_data' => $this->get_customer_meta_data($customer_id),
        );
    }

    /**
     * Get customer meta data
     * 
     * @param int $customer_id Customer ID
     * @return array Meta data
     */
    private function get_customer_meta_data($customer_id) {
        $meta_keys = array(
            'wings_customer_id',
            'wings_customer_code',
            'wings_pib',
            'wings_status',
            'wings_sales_rep',
            'wings_customer_class',
            'wings_last_sync',
        );

        $meta_data = array();
        foreach ($meta_keys as $key) {
            $value = get_user_meta($customer_id, $key, true);
            if (!empty($value)) {
                $meta_data[] = array('key' => $key, 'value' => $value);
            }
        }

        return $meta_data;
    }

    /**
     * Create customer in Wings
     * 
     * @param array $customer_data Wings customer data
     * @return int|WP_Error Wings customer ID or error
     */
    private function create_customer_in_wings($customer_data) {
        return $this->api->create_customer($customer_data);
    }

    /**
     * Find WooCommerce customer by email
     * 
     * @param string $email Customer email
     * @return WP_User|null Customer object or null
     */
    private function find_woocommerce_customer_by_email($email) {
        $user = get_user_by('email', $email);
        
        if ($user && in_array('customer', $user->roles)) {
            return $user;
        }
        
        return null;
    }

    /**
     * Create WooCommerce customer
     *
     * @param array $customer_data Customer data
     * @return int|WP_Error Customer ID or error
     */
    private function create_woocommerce_customer($customer_data) {
        try {
            // Create WordPress user first
            $user_data = array(
                'user_login' => $customer_data['user_email'],
                'user_email' => $customer_data['user_email'],
                'user_pass' => wp_generate_password(),
                'display_name' => $customer_data['display_name'],
                'first_name' => $customer_data['first_name'] ?? '',
                'last_name' => $customer_data['last_name'] ?? '',
                'role' => 'professionals'  // Set role to professionals as requested
            );

            $user_id = wp_insert_user($user_data);

            if (is_wp_error($user_id)) {
                Wings_WooCommerce_Sync::log('Failed to create WordPress user: ' . $user_id->get_error_message(), 'error');
                return $user_id;
            }

            // Create WooCommerce customer
            $customer = new WC_Customer($user_id);

            // Set basic customer data
            if (!empty($customer_data['first_name'])) {
                $customer->set_first_name($customer_data['first_name']);
            }
            if (!empty($customer_data['last_name'])) {
                $customer->set_last_name($customer_data['last_name']);
            }
            if (!empty($customer_data['display_name'])) {
                $customer->set_display_name($customer_data['display_name']);
            }

            // Set billing information
            $billing = $customer_data['billing'] ?? array();
            if (!empty($billing)) {
                $customer->set_billing_first_name($billing['first_name'] ?? '');
                $customer->set_billing_last_name($billing['last_name'] ?? '');
                $customer->set_billing_company($billing['company'] ?? '');
                $customer->set_billing_address_1($billing['address_1'] ?? '');
                $customer->set_billing_city($billing['city'] ?? '');
                $customer->set_billing_phone($billing['phone'] ?? '');
                $customer->set_billing_email($billing['email'] ?? $customer_data['user_email']);
                $customer->set_billing_country($billing['country'] ?? 'RS');
            }

            // Save customer
            $customer->save();

            // Add Wings-specific metadata
            $meta_data = $customer_data['meta_data'] ?? array();
            foreach ($meta_data as $key => $value) {
                if (strpos($key, 'wings_') === 0) {
                    update_user_meta($user_id, $key, $value);
                }
            }

            Wings_WooCommerce_Sync::log("WooCommerce customer created successfully with ID: {$user_id}", 'info');

            return $user_id;

        } catch (Exception $e) {
            Wings_WooCommerce_Sync::log('Exception creating WooCommerce customer: ' . $e->getMessage(), 'error');
            return new WP_Error('creation_failed', $e->getMessage());
        }
    }

    /**
     * Update WooCommerce customer
     *
     * @param int $customer_id Customer ID
     * @param array $customer_data Customer data
     * @return bool|WP_Error Success or error
     */
    private function update_woocommerce_customer($customer_id, $customer_data) {
        try {
            // Load existing customer
            $customer = new WC_Customer($customer_id);

            if (!$customer->get_id()) {
                return new WP_Error('customer_not_found', 'Customer not found');
            }

            // Update basic customer data
            if (!empty($customer_data['first_name'])) {
                $customer->set_first_name($customer_data['first_name']);
            }
            if (!empty($customer_data['last_name'])) {
                $customer->set_last_name($customer_data['last_name']);
            }
            if (!empty($customer_data['display_name'])) {
                $customer->set_display_name($customer_data['display_name']);
            }

            // Update billing information
            $billing = $customer_data['billing'] ?? array();
            if (!empty($billing)) {
                if (!empty($billing['first_name'])) {
                    $customer->set_billing_first_name($billing['first_name']);
                }
                if (!empty($billing['last_name'])) {
                    $customer->set_billing_last_name($billing['last_name']);
                }
                if (!empty($billing['company'])) {
                    $customer->set_billing_company($billing['company']);
                }
                if (!empty($billing['address_1'])) {
                    $customer->set_billing_address_1($billing['address_1']);
                }
                if (!empty($billing['city'])) {
                    $customer->set_billing_city($billing['city']);
                }
                if (!empty($billing['phone'])) {
                    $customer->set_billing_phone($billing['phone']);
                }
                if (!empty($billing['country'])) {
                    $customer->set_billing_country($billing['country']);
                }
            }

            // Save customer
            $customer->save();

            // Update Wings-specific metadata
            $meta_data = $customer_data['meta_data'] ?? array();
            foreach ($meta_data as $key => $value) {
                if (strpos($key, 'wings_') === 0) {
                    update_user_meta($customer_id, $key, $value);
                }
            }

            // Update last sync timestamp
            update_user_meta($customer_id, 'wings_last_sync', current_time('mysql'));

            Wings_WooCommerce_Sync::log("WooCommerce customer updated successfully: {$customer_id}", 'info');

            return true;

        } catch (Exception $e) {
            Wings_WooCommerce_Sync::log('Exception updating WooCommerce customer: ' . $e->getMessage(), 'error');
            return new WP_Error('update_failed', $e->getMessage());
        }
    }

    /**
     * Retrieve customers from Wings (for AJAX calls)
     *
     * @param string $retrieve_type Type of retrieval (all, new, updated)
     * @param int $batch_size Number of customers to retrieve
     * @param string $date_from Start date filter
     * @param string $date_to End date filter
     * @return array|WP_Error
     */
    public function retrieve_customers_from_wings($retrieve_type = 'all', $batch_size = 50, $date_from = '', $date_to = '') {
        Wings_WooCommerce_Sync::log("Pokretanje preuzimanja kupaca iz Wings-a (tip: {$retrieve_type})", 'info');

        $customers = $this->api->get_all_customers(array(), $batch_size);

        if (is_wp_error($customers)) {
            Wings_WooCommerce_Sync::log('Greška pri preuzimanju kupaca iz Wings-a: ' . $customers->get_error_message(), 'error');
            return $customers;
        }

        Wings_WooCommerce_Sync::log("Ukupno preuzeto {$batch_size} kupaca", 'info');

        $stats = array(
            'total_retrieved' => count($customers),
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped' => 0
        );

        $start_time = microtime(true);

        foreach ($customers as $wings_customer) {
            $stats['processed']++;

            try {
                $result = $this->sync_wings_customer_to_woocommerce($wings_customer);

                if (!is_wp_error($result)) {
                    if ($result['action'] === 'created') {
                        $stats['created']++;
                        Wings_WooCommerce_Sync::log("Kreiran novi kupac sa ID: {$result['customer_id']} i ulogom 'professionals'", 'info');
                    } elseif ($result['action'] === 'updated') {
                        $stats['updated']++;
                        Wings_WooCommerce_Sync::log("Ažuriran postojeći kupac: {$result['customer_id']}", 'info');
                    }
                } else {
                    $stats['errors']++;
                    Wings_WooCommerce_Sync::log("Greška pri obradi kupca: " . $result->get_error_message(), 'error');
                }

            } catch (Exception $e) {
                $stats['errors']++;
                Wings_WooCommerce_Sync::log('Exception pri obradi kupca: ' . $e->getMessage(), 'error');
            }
        }

        $execution_time = round(microtime(true) - $start_time, 2);

        Wings_WooCommerce_Sync::log("Preuzimanje kupaca završeno. Kreirano: {$stats['created']}, Ažurirano: {$stats['updated']}, Greške: {$stats['errors']}", 'info');

        return array(
            'stats' => $stats,
            'execution_time' => $execution_time
        );
    }

    /**
     * Sync customers (for AJAX calls)
     *
     * @param string $sync_type Type of sync (all, data, addresses)
     * @return array|WP_Error
     */
    public function sync_customers($sync_type = 'all') {
        Wings_WooCommerce_Sync::log("Pokretanje sinhronizacije kupaca (tip: {$sync_type})", 'info');

        // For now, this just calls the main sync method
        // In the future, we can add different sync types
        $this->sync_customers_from_wings();

        // Return basic stats
        return array(
            'stats' => array(
                'sync_type' => $sync_type,
                'message' => 'Sinhronizacija pokrenuta'
            ),
            'execution_time' => 0
        );
    }

    /**
     * Create WP customers from Wings data (for AJAX calls)
     *
     * @param bool $send_welcome_email
     * @param bool $generate_random_password
     * @param bool $set_customer_role
     * @return array|WP_Error
     */
    public function create_wp_customers_from_wings($send_welcome_email = false, $generate_random_password = true, $set_customer_role = true) {
        Wings_WooCommerce_Sync::log('Pokretanje kreiranja WP kupaca iz Wings podataka', 'info');

        $batch_size = $this->settings['customer_batch_size'] ?? 50;
        $customers = $this->api->get_all_customers(array(), $batch_size);

        if (is_wp_error($customers)) {
            Wings_WooCommerce_Sync::log('Greška pri preuzimanju kupaca iz Wings-a: ' . $customers->get_error_message(), 'error');
            return $customers;
        }

        $stats = array(
            'total_processed' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped' => 0
        );

        $start_time = microtime(true);

        foreach ($customers as $wings_customer) {
            $stats['total_processed']++;

            try {
                // Transform Wings customer to WooCommerce format
                $wc_customer_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);

                // Validate data
                $validation = Wings_Customer_Transformer::validate_woocommerce_customer($wc_customer_data);
                if (!$validation['valid']) {
                    Wings_WooCommerce_Sync::log('Validacija neuspešna za kupca: ' . implode(', ', $validation['errors']), 'warning');
                    $stats['errors']++;
                    continue;
                }

                // Check if customer already exists
                $existing_customer = $this->find_woocommerce_customer_by_email($wc_customer_data['user_email']);

                if ($existing_customer) {
                    // Update existing customer
                    $result = $this->update_woocommerce_customer($existing_customer->ID, $wc_customer_data);
                    if (!is_wp_error($result)) {
                        $stats['updated']++;
                        Wings_WooCommerce_Sync::log("Ažuriran postojeći kupac: {$existing_customer->ID}", 'info');
                    } else {
                        $stats['errors']++;
                        Wings_WooCommerce_Sync::log("Greška pri ažuriranju kupca: " . $result->get_error_message(), 'error');
                    }
                } else {
                    // Create new customer with professionals role
                    $customer_id = $this->create_woocommerce_customer_with_role($wc_customer_data, 'professionals');

                    if (!is_wp_error($customer_id)) {
                        $stats['created']++;
                        Wings_WooCommerce_Sync::log("Kreiran novi kupac sa ID: {$customer_id} i ulogom 'professionals'", 'info');

                        // Send welcome email if requested
                        if ($send_welcome_email) {
                            wp_new_user_notification($customer_id, null, 'user');
                        }
                    } else {
                        $stats['errors']++;
                        Wings_WooCommerce_Sync::log("Greška pri kreiranju kupca: " . $customer_id->get_error_message(), 'error');
                    }
                }

            } catch (Exception $e) {
                $stats['errors']++;
                Wings_WooCommerce_Sync::log('Exception pri obradi kupca: ' . $e->getMessage(), 'error');
            }
        }

        $execution_time = round(microtime(true) - $start_time, 2);

        Wings_WooCommerce_Sync::log("Kreiranje WP kupaca završeno. Kreirano: {$stats['created']}, Ažurirano: {$stats['updated']}, Greške: {$stats['errors']}", 'info');

        return array(
            'stats' => $stats,
            'execution_time' => $execution_time
        );
    }

    /**
     * Create WooCommerce customer with specific role
     *
     * @param array $customer_data Customer data
     * @param string $role User role (default: professionals)
     * @return int|WP_Error Customer ID or error
     */
    private function create_woocommerce_customer_with_role($customer_data, $role = 'professionals') {
        try {
            // Validate required data
            if (empty($customer_data['user_email'])) {
                return new WP_Error('missing_email', 'Email address is required');
            }

            if (empty($customer_data['display_name'])) {
                return new WP_Error('missing_name', 'Display name is required');
            }

            // Generate unique username from email
            $username = $this->generate_unique_username($customer_data['user_email']);

            // Create WordPress user first
            $user_data = array(
                'user_login' => $username,
                'user_email' => $customer_data['user_email'],
                'user_pass' => wp_generate_password(),
                'display_name' => $customer_data['display_name'],
                'first_name' => $customer_data['first_name'] ?? '',
                'last_name' => $customer_data['last_name'] ?? '',
                'role' => $role
            );

            $user_id = wp_insert_user($user_data);

            if (is_wp_error($user_id)) {
                Wings_WooCommerce_Sync::log('Failed to create WordPress user: ' . $user_id->get_error_message(), 'error');
                return $user_id;
            }

            // Set role specifically (in case wp_insert_user didn't set it properly)
            $user = new WP_User($user_id);
            $user->set_role($role);

            // If Ultimate Member is active, set UM-specific data
            if (class_exists('UM')) {
                $this->setup_ultimate_member_user($user_id, $customer_data, $role);
            }

            // Create WooCommerce customer
            $customer = new WC_Customer($user_id);

            // Set billing information
            if (isset($customer_data['billing'])) {
                $billing = $customer_data['billing'];
                $customer->set_billing_first_name($billing['first_name'] ?? '');
                $customer->set_billing_last_name($billing['last_name'] ?? '');
                $customer->set_billing_company($billing['company'] ?? '');
                $customer->set_billing_address_1($billing['address_1'] ?? '');
                $customer->set_billing_address_2($billing['address_2'] ?? '');
                $customer->set_billing_city($billing['city'] ?? '');
                $customer->set_billing_state($billing['state'] ?? '');
                $customer->set_billing_postcode($billing['postcode'] ?? '');
                $customer->set_billing_country($billing['country'] ?? 'RS');
                $customer->set_billing_email($billing['email'] ?? $customer_data['user_email']);
                $customer->set_billing_phone($billing['phone'] ?? '');
            }

            // Set shipping information (copy from billing if not provided)
            if (isset($customer_data['shipping'])) {
                $shipping = $customer_data['shipping'];
                $customer->set_shipping_first_name($shipping['first_name'] ?? $customer->get_billing_first_name());
                $customer->set_shipping_last_name($shipping['last_name'] ?? $customer->get_billing_last_name());
                $customer->set_shipping_company($shipping['company'] ?? $customer->get_billing_company());
                $customer->set_shipping_address_1($shipping['address_1'] ?? $customer->get_billing_address_1());
                $customer->set_shipping_address_2($shipping['address_2'] ?? $customer->get_billing_address_2());
                $customer->set_shipping_city($shipping['city'] ?? $customer->get_billing_city());
                $customer->set_shipping_state($shipping['state'] ?? $customer->get_billing_state());
                $customer->set_shipping_postcode($shipping['postcode'] ?? $customer->get_billing_postcode());
                $customer->set_shipping_country($shipping['country'] ?? $customer->get_billing_country());
            }

            // Save customer
            $customer->save();

            // Add Wings-specific metadata
            $meta_data = $customer_data['meta_data'] ?? array();
            foreach ($meta_data as $key => $value) {
                if (strpos($key, 'wings_') === 0) {
                    update_user_meta($user_id, $key, $value);
                }
            }

            Wings_WooCommerce_Sync::log("WooCommerce customer created successfully with ID: {$user_id} and role: {$role}", 'info');

            return $user_id;

        } catch (Exception $e) {
            Wings_WooCommerce_Sync::log('Exception creating WooCommerce customer: ' . $e->getMessage(), 'error');
            return new WP_Error('creation_failed', $e->getMessage());
        }
    }

    /**
     * Generate unique username from email
     *
     * @param string $email Email address
     * @return string Unique username
     */
    private function generate_unique_username($email) {
        // Start with email prefix
        $base_username = sanitize_user(substr($email, 0, strpos($email, '@')));

        // If empty, use wings prefix
        if (empty($base_username)) {
            $base_username = 'wings_customer';
        }

        $username = $base_username;
        $counter = 1;

        // Make sure username is unique
        while (username_exists($username)) {
            $username = $base_username . '_' . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Setup Ultimate Member specific user data
     *
     * @param int $user_id User ID
     * @param array $customer_data Customer data
     * @param string $role User role
     */
    private function setup_ultimate_member_user($user_id, $customer_data, $role) {
        // Set UM role
        update_user_meta($user_id, 'role', $role);

        // Set UM account status
        update_user_meta($user_id, 'account_status', 'approved');

        // Set UM profile completeness
        update_user_meta($user_id, 'profile_completed', 1);

        // Set additional UM fields if available
        if (!empty($customer_data['billing']['company'])) {
            update_user_meta($user_id, 'company', $customer_data['billing']['company']);
        }

        if (!empty($customer_data['billing']['phone'])) {
            update_user_meta($user_id, 'mobile_number', $customer_data['billing']['phone']);
        }

        // Set Wings-specific UM fields
        if (!empty($customer_data['meta_data']['wings_customer_id'])) {
            update_user_meta($user_id, 'wings_customer_id', $customer_data['meta_data']['wings_customer_id']);
        }

        Wings_WooCommerce_Sync::log("Ultimate Member data set for user {$user_id} with role {$role}", 'info');
    }
}
