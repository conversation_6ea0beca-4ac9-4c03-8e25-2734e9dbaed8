<?php
/**
 * Wings API Class
 * 
 * Handles communication with Wings Portal API
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_API {

    /**
     * API instance
     */
    private static $instance = null;

    /**
     * API settings
     */
    private $settings;

    /**
     * Session token
     */
    private $session_token = null;

    /**
     * API base URL
     */
    private $api_url;

    /**
     * API alias
     */
    private $api_alias;

    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->settings = Wings_WooCommerce_Sync::get_settings();
        $this->api_url = trailingslashit($this->settings['api_url'] ?? 'https://portal.wings.rs/api/v1/');
        $this->api_alias = $this->settings['api_alias'] ?? '';
    }

    /**
     * Test API connection
     */
    public function test_connection() {
        if (empty($this->api_alias)) {
            return new WP_Error('missing_alias', __('API alias nije podešen.', 'wings-sync'));
        }

        $login_result = $this->login();
        
        if (is_wp_error($login_result)) {
            return $login_result;
        }

        return array(
            'success' => true,
            'message' => __('Konekcija uspešna!', 'wings-sync'),
            'token' => $this->session_token
        );
    }

    /**
     * Login to Wings API
     */
    public function login() {
        $username = $this->settings['api_username'] ?? '';
        $password = $this->settings['api_password'] ?? '';

        if (empty($username) || empty($password)) {
            return new WP_Error('missing_credentials', __('API kredencijali nisu podešeni.', 'wings-sync'));
        }

        $login_url = $this->api_url . $this->api_alias . '/system.user.log';
        
        $body = array(
            'aUn' => $username,
            'aUp' => $password
        );

        $response = wp_remote_post($login_url, array(
            'timeout' => $this->settings['timeout'] ?? 60,
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => wp_json_encode($body)
        ));

        if (is_wp_error($response)) {
            Wings_WooCommerce_Sync::log('Login greška: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            Wings_WooCommerce_Sync::log('Login neuspešan: HTTP ' . $response_code, 'error');
            return new WP_Error('login_failed', sprintf(__('Login neuspešan. HTTP kod: %d', 'wings-sync'), $response_code));
        }

        $data = json_decode($response_body, true);

        if (!$data || !isset($data['data'][0]['attributes']['token'])) {
            Wings_WooCommerce_Sync::log('Neispravna struktura odgovora pri login-u', 'error');
            return new WP_Error('invalid_response', __('Neispravna struktura odgovora.', 'wings-sync'));
        }

        $this->session_token = $data['data'][0]['attributes']['token'];
        
        Wings_WooCommerce_Sync::log('Uspešan login. Token: ' . substr($this->session_token, 0, 10) . '...', 'info');
        
        return true;
    }

    /**
     * Make API request
     */
    private function make_request($endpoint, $params = array(), $method = 'GET') {
        // Ensure we're logged in
        if (!$this->session_token) {
            $login_result = $this->login();
            if (is_wp_error($login_result)) {
                return $login_result;
            }
        }

        $url = $this->api_url . $this->api_alias . '/' . $endpoint;

        $args = array(
            'timeout' => $this->settings['timeout'] ?? 60,
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $this->session_token
            )
        );

        if ($method === 'GET' && !empty($params)) {
            $url = add_query_arg($params, $url);
        } elseif ($method === 'POST') {
            $args['method'] = 'POST';
            $args['headers']['Content-Type'] = 'application/json';
            $args['body'] = wp_json_encode($params);
        }

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            Wings_WooCommerce_Sync::log('API zahtev greška: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            Wings_WooCommerce_Sync::log('API zahtev neuspešan: HTTP ' . $response_code . ' za endpoint: ' . $endpoint, 'error');
            return new WP_Error('api_error', sprintf(__('API greška. HTTP kod: %d', 'wings-sync'), $response_code));
        }

        $data = json_decode($response_body, true);

        if (!$data) {
            Wings_WooCommerce_Sync::log('Neispravna JSON struktura u odgovoru za endpoint: ' . $endpoint, 'error');
            return new WP_Error('invalid_json', __('Neispravna JSON struktura.', 'wings-sync'));
        }

        return $data;
    }

    /**
     * Get all warehouses
     */
    public function get_warehouses() {
        $cache_key = 'wings_warehouses';
        $cached = get_transient($cache_key);
        
        if ($cached !== false) {
            return $cached;
        }

        $response = $this->make_request('lager.magacin.svi');
        
        if (is_wp_error($response)) {
            return $response;
        }

        $warehouses = $response['data'] ?? array();
        
        // Cache for 5 minutes
        set_transient($cache_key, $warehouses, 5 * MINUTE_IN_SECONDS);
        
        Wings_WooCommerce_Sync::log('Preuzeto ' . count($warehouses) . ' magacina', 'info');
        
        return $warehouses;
    }

    /**
     * Get articles with pagination
     */
    public function get_articles($start = 0, $length = 50, $warehouse_id = null) {
        $params = array(
            'dStart' => $start,
            'dLength' => $length,
            'output' => 'jsonapi'
        );

        if ($warehouse_id) {
            $params['magacin'] = $warehouse_id;
        }

        $response = $this->make_request('local.cache.artikal', $params);
        
        if (is_wp_error($response)) {
            return $response;
        }

        $articles = $response['data'] ?? array();
        $meta = $response['meta'] ?? array();
        
        Wings_WooCommerce_Sync::log('Preuzeto ' . count($articles) . ' artikala (start: ' . $start . ', length: ' . $length . ')', 'info');
        
        return array(
            'articles' => $articles,
            'meta' => $meta
        );
    }

    /**
     * Get all articles (with pagination handling)
     */
    public function get_all_articles($warehouse_id = null, $batch_size = 50) {
        $all_articles = array();
        $start = 0;
        $max_iterations = 100; // Safety limit
        $iteration = 0;

        do {
            $result = $this->get_articles($start, $batch_size, $warehouse_id);
            
            if (is_wp_error($result)) {
                return $result;
            }

            $articles = $result['articles'];
            $meta = $result['meta'];
            
            if (empty($articles)) {
                break;
            }

            $all_articles = array_merge($all_articles, $articles);
            
            $start += $batch_size;
            $iteration++;
            
            // Check if we have more data
            $total = $meta['total'] ?? 0;
            if ($start >= $total) {
                break;
            }

            // Safety break
            if ($iteration >= $max_iterations) {
                Wings_WooCommerce_Sync::log('Dostignut maksimalni broj iteracija pri preuzimanju artikala', 'warning');
                break;
            }

            // Small delay to avoid overwhelming the API
            usleep(500000); // 0.5 seconds

        } while (true);

        Wings_WooCommerce_Sync::log('Ukupno preuzeto ' . count($all_articles) . ' artikala', 'info');

        return $all_articles;
    }

    /**
     * Get customers with pagination
     */
    public function get_customers($start = 0, $length = 50, $filters = array()) {
        $params = array(
            'dStart' => $start,
            'dLength' => $length,
            'output' => 'jsonapi'
        );

        // Add optional filters
        if (!empty($filters['komercijalista'])) {
            $params['komercijalista'] = $filters['komercijalista'];
        }
        if (!empty($filters['status'])) {
            $params['status'] = $filters['status'];
        }
        if (!empty($filters['search'])) {
            $params['search'] = $filters['search'];
        }

        $response = $this->make_request('local.kupac.svi', $params);

        if (is_wp_error($response)) {
            return $response;
        }

        $customers = $response['data'] ?? array();
        $meta = $response['meta'] ?? array();

        Wings_WooCommerce_Sync::log('Preuzeto ' . count($customers) . ' kupaca (start: ' . $start . ', length: ' . $length . ')', 'info');

        return array(
            'customers' => $customers,
            'meta' => $meta
        );
    }

    /**
     * Get all customers with pagination
     */
    public function get_all_customers($filters = array(), $batch_size = 50) {
        $all_customers = array();
        $start = 0;
        $max_iterations = 100; // Safety limit
        $iteration = 0;

        do {
            $result = $this->get_customers($start, $batch_size, $filters);

            if (is_wp_error($result)) {
                return $result;
            }

            $customers = $result['customers'];
            $meta = $result['meta'];

            if (empty($customers)) {
                break;
            }

            $all_customers = array_merge($all_customers, $customers);

            $start += $batch_size;
            $iteration++;

            // Check if we have more data
            $total = $meta['total'] ?? 0;
            if ($start >= $total) {
                break;
            }

            // Safety break
            if ($iteration >= $max_iterations) {
                Wings_WooCommerce_Sync::log('Dostignut maksimalni broj iteracija pri preuzimanju kupaca', 'warning');
                break;
            }

            // Small delay to avoid overwhelming the API
            usleep(500000); // 0.5 seconds

        } while (true);

        Wings_WooCommerce_Sync::log('Ukupno preuzeto ' . count($all_customers) . ' kupaca', 'info');

        return $all_customers;
    }

    /**
     * Get specific customer info
     */
    public function get_customer_info($customer_id) {
        $params = array(
            'id' => $customer_id
        );

        $response = $this->make_request('local.kupac.info', $params);

        if (is_wp_error($response)) {
            return $response;
        }

        $customer_data = $response['data'] ?? array();

        Wings_WooCommerce_Sync::log('Preuzeti podaci za kupca ID: ' . $customer_id, 'info');

        return $customer_data;
    }

    /**
     * Create new customer in Wings
     */
    public function create_customer($customer_data) {
        $response = $this->make_request('local.kupac.nov', $customer_data, 'POST');

        if (is_wp_error($response)) {
            Wings_WooCommerce_Sync::log('Greška pri kreiranju kupca: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $new_customer_id = $response;
        Wings_WooCommerce_Sync::log('Kreiran novi kupac sa ID: ' . $new_customer_id, 'info');

        return $new_customer_id;
    }

    /**
     * Get article warehouse stock
     */
    public function get_article_warehouse_stock($article_id) {
        $cache_key = 'wings_stock_' . $article_id;
        $cached = get_transient($cache_key);
        
        if ($cached !== false) {
            return $cached;
        }

        $params = array('artikal' => $article_id);
        $response = $this->make_request('lager.artikal.magacin', $params);
        
        if (is_wp_error($response)) {
            return $response;
        }

        $stock_data = $response['data'] ?? array();
        
        // Cache for 2 minutes
        set_transient($cache_key, $stock_data, 2 * MINUTE_IN_SECONDS);
        
        return $stock_data;
    }

    /**
     * Clear API cache
     */
    public function clear_cache() {
        global $wpdb;
        
        // Delete all Wings-related transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wings_%' OR option_name LIKE '_transient_timeout_wings_%'");
        
        Wings_WooCommerce_Sync::log('API cache obrisan', 'info');
    }
}
