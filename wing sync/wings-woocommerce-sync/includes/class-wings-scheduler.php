<?php
/**
 * Wings Scheduler Class
 * 
 * Handles automatic synchronization scheduling
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_Scheduler {

    /**
     * Scheduler instance
     */
    private static $instance = null;

    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('wings_sync_cron_hook', array($this, 'run_scheduled_sync'));
        add_action('init', array($this, 'init_scheduler'));
        add_filter('cron_schedules', array($this, 'add_cron_intervals'));
    }

    /**
     * Initialize scheduler
     */
    public function init_scheduler() {
        $settings = Wings_WooCommerce_Sync::get_settings();
        
        // Schedule cron if auto sync is enabled and not already scheduled
        if (($settings['auto_sync'] ?? false) && !wp_next_scheduled('wings_sync_cron_hook')) {
            wp_schedule_event(time(), 'wings_sync_interval', 'wings_sync_cron_hook');
            Wings_WooCommerce_Sync::log('Automatska sinhronizacija je zakazana', 'info');
        }
        
        // Unschedule if auto sync is disabled
        if (!($settings['auto_sync'] ?? false) && wp_next_scheduled('wings_sync_cron_hook')) {
            wp_clear_scheduled_hook('wings_sync_cron_hook');
            Wings_WooCommerce_Sync::log('Automatska sinhronizacija je otkazana', 'info');
        }
    }

    /**
     * Add custom cron intervals
     */
    public function add_cron_intervals($schedules) {
        $settings = Wings_WooCommerce_Sync::get_settings();
        $interval = intval($settings['sync_interval'] ?? 15);
        
        $schedules['wings_sync_interval'] = array(
            'interval' => $interval * 60, // Convert minutes to seconds
            'display' => sprintf(__('Svakih %d minuta', 'wings-sync'), $interval)
        );
        
        return $schedules;
    }

    /**
     * Run scheduled synchronization
     */
    public function run_scheduled_sync() {
        $settings = Wings_WooCommerce_Sync::get_settings();
        
        // Check if sync is enabled
        if (!($settings['sync_enabled'] ?? true)) {
            Wings_WooCommerce_Sync::log('Sinhronizacija je onemogućena u podešavanjima', 'info');
            return;
        }

        // Check if auto sync is enabled
        if (!($settings['auto_sync'] ?? false)) {
            Wings_WooCommerce_Sync::log('Automatska sinhronizacija je onemogućena', 'info');
            return;
        }

        Wings_WooCommerce_Sync::log('Pokretanje automatske sinhronizacije...', 'info');

        try {
            // Set time limit for background process
            set_time_limit(300); // 5 minutes
            
            // Increase memory limit if possible
            if (function_exists('ini_set')) {
                ini_set('memory_limit', '512M');
            }

            $sync = Wings_Sync::get_instance();
            $result = $sync->sync_all_products();

            if (is_wp_error($result)) {
                Wings_WooCommerce_Sync::log('Automatska sinhronizacija neuspešna: ' . $result->get_error_message(), 'error');
                $this->send_error_notification($result->get_error_message());
            } else {
                $stats = $result['stats'];
                $message = sprintf(
                    'Automatska sinhronizacija završena. Obrađeno: %d, Ažurirano: %d, Kreirano: %d, Greške: %d',
                    $stats['total_processed'],
                    $stats['updated'],
                    $stats['created'],
                    $stats['errors']
                );
                
                Wings_WooCommerce_Sync::log($message, 'info');

                // Send notification if there were errors
                if ($stats['errors'] > 0) {
                    $this->send_error_notification('Automatska sinhronizacija završena sa greškama: ' . $stats['errors']);
                }
            }

        } catch (Exception $e) {
            $error_message = 'Kritična greška tokom automatske sinhronizacije: ' . $e->getMessage();
            Wings_WooCommerce_Sync::log($error_message, 'error');
            $this->send_error_notification($error_message);
        }
    }

    /**
     * Send error notification email
     */
    private function send_error_notification($message) {
        $settings = Wings_WooCommerce_Sync::get_settings();
        
        // Check if notifications are enabled (you can add this setting)
        if (!($settings['email_notifications'] ?? true)) {
            return;
        }

        $admin_email = get_option('admin_email');
        $site_name = get_bloginfo('name');
        
        $subject = sprintf('[%s] Wings Sync - Greška u sinhronizaciji', $site_name);
        
        $email_message = sprintf(
            "Poštovani,\n\nDošlo je do greške tokom automatske sinhronizacije Wings Portal-a sa WooCommerce-om.\n\nGreška: %s\n\nVreme: %s\n\nMolimo proverite podešavanja i log fajlove.\n\nAdmin panel: %s\n\nSrdačan pozdrav,\nWings Sync Plugin",
            $message,
            current_time('Y-m-d H:i:s'),
            admin_url('admin.php?page=wings-sync')
        );

        wp_mail($admin_email, $subject, $email_message);
    }

    /**
     * Get next scheduled sync time
     */
    public function get_next_sync_time() {
        $timestamp = wp_next_scheduled('wings_sync_cron_hook');
        
        if (!$timestamp) {
            return null;
        }
        
        return array(
            'timestamp' => $timestamp,
            'formatted' => date('Y-m-d H:i:s', $timestamp),
            'human' => human_time_diff($timestamp, time())
        );
    }

    /**
     * Manually reschedule sync
     */
    public function reschedule_sync() {
        // Clear existing schedule
        wp_clear_scheduled_hook('wings_sync_cron_hook');
        
        $settings = Wings_WooCommerce_Sync::get_settings();
        
        // Reschedule if auto sync is enabled
        if ($settings['auto_sync'] ?? false) {
            wp_schedule_event(time(), 'wings_sync_interval', 'wings_sync_cron_hook');
            Wings_WooCommerce_Sync::log('Sinhronizacija je ponovo zakazana', 'info');
            return true;
        }
        
        return false;
    }

    /**
     * Check if sync is currently running
     */
    public function is_sync_running() {
        return get_transient('wings_sync_running') !== false;
    }

    /**
     * Set sync running status
     */
    public function set_sync_running($running = true) {
        if ($running) {
            set_transient('wings_sync_running', time(), 10 * MINUTE_IN_SECONDS);
        } else {
            delete_transient('wings_sync_running');
        }
    }

    /**
     * Get sync lock status
     */
    public function get_sync_lock_info() {
        $lock_time = get_transient('wings_sync_running');
        
        if (!$lock_time) {
            return null;
        }
        
        return array(
            'locked_since' => $lock_time,
            'locked_for' => time() - $lock_time,
            'formatted' => date('Y-m-d H:i:s', $lock_time)
        );
    }

    /**
     * Force unlock sync (emergency use)
     */
    public function force_unlock_sync() {
        delete_transient('wings_sync_running');
        Wings_WooCommerce_Sync::log('Sync lock je prisilno uklonjen', 'warning');
    }

    /**
     * Get cron status information
     */
    public function get_cron_status() {
        $next_sync = $this->get_next_sync_time();
        $settings = Wings_WooCommerce_Sync::get_settings();
        $lock_info = $this->get_sync_lock_info();
        
        return array(
            'auto_sync_enabled' => $settings['auto_sync'] ?? false,
            'sync_interval' => $settings['sync_interval'] ?? 15,
            'next_sync' => $next_sync,
            'is_locked' => $lock_info !== null,
            'lock_info' => $lock_info,
            'wp_cron_disabled' => defined('DISABLE_WP_CRON') && DISABLE_WP_CRON
        );
    }

    /**
     * Test cron functionality
     */
    public function test_cron() {
        // Schedule a test event
        wp_schedule_single_event(time() + 60, 'wings_sync_test_cron');
        
        // Check if it was scheduled
        $scheduled = wp_next_scheduled('wings_sync_test_cron');
        
        if ($scheduled) {
            // Clean up
            wp_clear_scheduled_hook('wings_sync_test_cron');
            return true;
        }
        
        return false;
    }

    /**
     * Get detailed scheduler information for debugging
     */
    public function get_debug_info() {
        $cron_array = _get_cron_array();
        $wings_crons = array();
        
        foreach ($cron_array as $timestamp => $cron) {
            foreach ($cron as $hook => $events) {
                if (strpos($hook, 'wings_') === 0) {
                    $wings_crons[] = array(
                        'hook' => $hook,
                        'timestamp' => $timestamp,
                        'formatted' => date('Y-m-d H:i:s', $timestamp),
                        'events' => $events
                    );
                }
            }
        }
        
        return array(
            'wp_cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON,
            'wings_crons' => $wings_crons,
            'current_time' => time(),
            'current_time_formatted' => current_time('Y-m-d H:i:s'),
            'timezone' => wp_timezone_string()
        );
    }
}
