# Wings WooCommerce Sync

Sinhronizuje artikle iz Wings Portal baze sa WooCommerce proizvodima.

## Opis

Wings WooCommerce Sync je WordPress plugin koji omogućava automatsku sinhronizaciju proizvoda između Wings Portal sistema i WooCommerce online prodavnice. Plugin podržava sinhronizaciju cena, zaliha, opisa proizvoda i drugih važnih informacija.

## Karakteristike

- **Reorganizovani interfejs**: Novi tab-based interfejs sa jasno podeljenim funkcionalnostima
- **Automatska sinhronizacija**: Redovno ažuriranje proizvoda prema podešenom rasporedu
- **Manualna sinhronizacija**: Mogućnost pokretanja sinhronizacije na zahtev
- **Batch obrada**: Efikasno procesiranje velikog broja proizvoda
- **Error handling**: Napredna kontrola grešaka sa detaljnim logovima
- **HPOS kompatibilnost**: Podržava WooCommerce High-Performance Order Storage
- **Sinhronizacija kupaca**: Dvosmerna sinhronizacija kupaca između Wings Portal-a i WooCommerce-a
- **Vizuelni progress tracking**: Real-time praćenje napretka sa graficima i metrikama
- **Kompletno testiranje**: Ugrađeni test alati za validaciju funkcionalnosti

## Zahtevi

- WordPress 5.5 ili noviji
- WooCommerce 5.0 atau noviji
- PHP 7.4 ili noviji
- Pristup Wings Portal API-ju

## Instalacija

1. Preuzmite plugin fajlove
2. Otpakujte ih u `/wp-content/plugins/wings-woocommerce-sync/` direktorijum
3. Aktivirajte plugin kroz 'Plugins' meni u WordPress admin panelu
4. Idite na WooCommerce > Wings Sync za konfiguraciju

## Novi Interfejs - Tab Organizacija

Plugin je reorganizovan u 5 glavnih sekcija:

### 1. API Podešavanja
- Konfiguracija API konekcije
- Test okruženje presets
- Osnovne opcije sinhronizacije
- Podešavanja za kupce

### 2. Test Komande
- Test API konekcije za proizvode
- Test API konekcije za kupce
- API dijagnostika
- Performance testovi

### 3. Sinhronizacija Proizvoda
- Manualna sinhronizacija svih proizvoda
- Sinhronizacija samo zaliha
- Sinhronizacija samo cena
- Selektivna sinhronizacija (po kategoriji, SKU, datumu)
- Upravljanje zalihama

### 4. Upravljanje Kupcima
- Preuzimanje kupaca iz Wings Portal-a
- Sinhronizacija postojećih kupaca
- Slanje nesinhronizovanih kupaca u Wings
- Kreiranje WP korisnika iz Wings podataka

### 5. Izveštaj o Napretku
- Vizuelni dashboard sa progress indikatorima
- Real-time activity feed
- Performance metrije
- Export funkcionalnosti
- System health monitoring

## Konfiguracija

### API Podešavanja

1. Idite na tab "API Podešavanja"
2. Unesite Wings Portal API URL (obično `https://portal.wings.rs/api/v1/`)
3. Unesite vaš klijentski alias
4. Unesite korisničko ime i lozinku
5. Testirajte konekciju klikom na "Testiraj konekciju"

**Brza konfiguracija**: Koristite "Test okruženje - Brza konfiguracija" dugmad za automatsko podešavanje test API-ja.

### Opcije Sinhronizacije

- **Automatska sinhronizacija**: Omogućite za redovnu sinhronizaciju
- **Interval sinhronizacije**: Izaberite koliko često da se pokreće (5, 15, 30 minuta ili 1 sat)
- **Veličina batch-a**: Broj proizvoda koji se obrađuje odjednom (10-200)
- **Timeout**: Maksimalno vreme čekanja za API pozive (30-300 sekundi)

## Korišćenje

### Testiranje Konekcije

1. Idite na tab "Test Komande"
2. Pokrenite "Test konekcije za proizvode" ili "Test konekcije za kupce"
3. Pregledajte rezultate u real-time
4. Koristite dodatne testove za dijagnostiku

### Sinhronizacija Proizvoda

1. Idite na tab "Sinhronizacija Proizvoda"
2. Izaberite tip sinhronizacije:
   - **Sve proizvode**: Kompletna sinhronizacija
   - **Samo zalihe**: Brža sinhronizacija zaliha
   - **Samo cene**: Sinhronizacija cena
3. Ili koristite selektivnu sinhronizaciju po kategoriji/SKU
4. Pratite napredak kroz vizuelni progress bar

### Upravljanje Kupcima

1. Idite na tab "Upravljanje Kupcima"
2. **Preuzimanje**: Preuzmite kupce iz Wings Portal-a
3. **Sinhronizacija**: Sinhronizujte postojeće kupce
4. **Slanje**: Pošaljite nesinhronizovane WP kupce u Wings
5. **Kreiranje**: Kreirajte WP korisnike iz Wings podataka

### Praćenje Napretka

1. Idite na tab "Izveštaj o Napretku"
2. Pregledajte vizuelni dashboard
3. Pratite real-time aktivnost
4. Analizirajte performance metrije
5. Generirajte i izvezite izveštaje

## Testiranje

Plugin uključuje kompletnu test suite dostupnu kroz "Test Komande" tab:

### Automatski testovi
- Test API konekcije
- Test preuzimanja podataka
- Test transformacije podataka
- Performance testovi

### Manualni testovi
```bash
# Pokretanje testova preko WP-CLI
wp eval-file wp-content/plugins/wings-woocommerce-sync/test-customer-sync.php
```

Ili pristupite test stranici direktno:
```
/wp-admin/admin.php?page=wings-sync&tab=test-commands&run_wings_customer_tests=1
```

Za detaljne instrukcije o testiranju, pogledajte `TESTING-GUIDE.md`.

## Logovi i Monitoring

Plugin vodi detaljne logove svih aktivnosti dostupne kroz "Izveštaj o Napretku":

- **Real-time activity feed**: Trenutna aktivnost
- **Performance metrije**: Brzina, uspešnost, korišćenje resursa
- **System health**: Status API-ja, cron zadataka, memorije
- **Export opcije**: Izvoz logova i izveštaja

## Troubleshooting

### Česti Problemi

**Problem**: "Connection failed: Invalid credentials"
**Rešenje**: 
1. Idite na "API Podešavanja" tab
2. Proverite API kredencijale
3. Koristite "Test konekcije" dugme

**Problem**: "Sync timeout"
**Rešenje**: 
1. Idite na "API Podešavanja"
2. Povećajte timeout vrednost
3. Smanjite batch size

**Problem**: "Memory limit exceeded"
**Rešenje**: 
1. Smanjite batch size u podešavanjima
2. Povećajte PHP memory limit
3. Koristite selektivnu sinhronizaciju

### Debug Mode

Za detaljno debug-ovanje:

1. Omogućite WordPress debug mode u `wp-config.php`
2. Koristite "Test Komande" tab za dijagnostiku
3. Pregledajte "Izveštaj o Napretku" za detaljne metrije

## API Dokumentacija

Plugin koristi Wings Portal REST API sa sledećim endpoint-ovima:

- `GET /gross/system.user.log` - Autentifikacija
- `GET /gross/artikal.lista` - Lista proizvoda
- `GET /gross/kupac.lista` - Lista kupaca
- `POST /gross/kupac.dodaj` - Dodavanje kupca

## Sigurnost

- Svi API pozivi koriste HTTPS
- Kredencijali se čuvaju enkriptovano
- CSRF zaštita za sve admin akcije
- Validacija i sanitizacija svih input podataka

## Dokumentacija

Detaljnu dokumentaciju možete naći u:

- `REORGANIZED-COMMANDS-SUMMARY.md` - Pregled nove organizacije
- `TESTING-GUIDE.md` - Instrukcije za testiranje
- `CUSTOMER-SYNC-DETAILED.md` - Detalji o sinhronizaciji kupaca
- `PRODUCT-SYNC-README.md` - Detalji o sinhronizaciji proizvoda

## Podrška

Za podršku i dodatne informacije:

1. Proverite dokumentaciju u `docs/` direktorijumu
2. Koristite ugrađene test alate u "Test Komande" tab-u
3. Pregledajte "Izveštaj o Napretku" za dijagnostiku
4. Kontaktirajte Wings Portal support tim

## Changelog

### 1.1.0
- **MAJOR UPDATE**: Kompletno reorganizovan interfejs
- Novi tab-based dizajn sa 5 glavnih sekcija
- Vizuelni progress tracking sa circular progress indikatorima
- Real-time activity feed
- Kompletni test alati ugrađeni u interfejs
- Performance metrije i system health monitoring
- Export funkcionalnosti za izveštaje
- Poboljšana sinhronizacija kupaca
- Selektivna sinhronizacija po kategoriji, SKU, datumu

### 1.0.0
- Početna verzija
- Osnovna sinhronizacija proizvoda
- Automatska sinhronizacija
- Admin interface

## Licenca

GPL v2 or later

## Autor

Vaše ime - vaš@email.com
