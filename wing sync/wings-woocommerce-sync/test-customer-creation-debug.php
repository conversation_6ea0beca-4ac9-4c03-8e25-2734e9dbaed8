<?php
/**
 * Debug test for customer creation with professionals role
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Include WordPress if running standalone
    require_once('../../../wp-config.php');
}

function test_customer_creation_debug() {
    echo "<h2>Wings Customer Creation Debug Test</h2>\n";
    
    // Check if professionals role exists
    echo "<h3>1. Checking if 'professionals' role exists</h3>\n";
    $professionals_role = get_role('professionals');
    if ($professionals_role) {
        echo "<p style='color: green;'>✓ 'professionals' role exists</p>\n";
        echo "<pre>Capabilities: " . print_r($professionals_role->capabilities, true) . "</pre>\n";
    } else {
        echo "<p style='color: red;'>✗ 'professionals' role does NOT exist</p>\n";
        
        // Try to create it
        echo "<p>Attempting to create 'professionals' role...</p>\n";
        $customer_role = get_role('customer');
        $capabilities = $customer_role ? $customer_role->capabilities : array('read' => true);
        
        add_role('professionals', 'Professionals', $capabilities);
        
        $professionals_role = get_role('professionals');
        if ($professionals_role) {
            echo "<p style='color: green;'>✓ 'professionals' role created successfully</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to create 'professionals' role</p>\n";
        }
    }
    
    // Check customer sync settings
    echo "<h3>2. Checking customer sync settings</h3>\n";
    $settings = Wings_WooCommerce_Sync::get_settings();
    echo "<pre>Settings: " . print_r($settings, true) . "</pre>\n";
    
    $enable_customer_sync = $settings['enable_customer_sync'] ?? false;
    if ($enable_customer_sync) {
        echo "<p style='color: green;'>✓ Customer sync is enabled</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Customer sync is NOT enabled</p>\n";
        
        // Enable it
        $settings['enable_customer_sync'] = true;
        $settings['customer_sync_interval'] = 'hourly';
        $settings['customer_batch_size'] = 50;
        Wings_WooCommerce_Sync::update_settings($settings);
        echo "<p>Customer sync has been enabled.</p>\n";
    }
    
    // Test API connection
    echo "<h3>3. Testing API connection</h3>\n";
    try {
        $api = Wings_API::get_instance();
        $connection_test = $api->test_connection();
        
        if (is_wp_error($connection_test)) {
            echo "<p style='color: red;'>✗ API connection failed: " . $connection_test->get_error_message() . "</p>\n";
        } else {
            echo "<p style='color: green;'>✓ API connection successful</p>\n";
            
            // Try to get customers
            echo "<h4>3.1 Testing customer retrieval</h4>\n";
            $customers = $api->get_all_customers(array(), 5);
            
            if (is_wp_error($customers)) {
                echo "<p style='color: red;'>✗ Customer retrieval failed: " . $customers->get_error_message() . "</p>\n";
            } else {
                echo "<p style='color: green;'>✓ Retrieved " . count($customers) . " customers</p>\n";
                
                if (!empty($customers)) {
                    echo "<h4>3.2 Sample customer data</h4>\n";
                    echo "<pre>" . print_r($customers[0], true) . "</pre>\n";
                    
                    // Test transformation
                    echo "<h4>3.3 Testing data transformation</h4>\n";
                    $wc_customer_data = Wings_Customer_Transformer::wings_to_woocommerce($customers[0]);
                    echo "<pre>Transformed data: " . print_r($wc_customer_data, true) . "</pre>\n";

                    // Test validation
                    $validation = Wings_Customer_Transformer::validate_woocommerce_customer($wc_customer_data);
                    if ($validation['valid']) {
                        echo "<p style='color: green;'>✓ Customer data validation passed</p>\n";
                    } else {
                        echo "<p style='color: red;'>✗ Customer data validation failed: " . implode(', ', $validation['errors']) . "</p>\n";

                        // Show what was missing and try to fix it
                        echo "<p><strong>Attempting to fix validation issues...</strong></p>\n";

                        // Fix email if missing
                        if (empty($wc_customer_data['user_email'])) {
                            $customer_id = $customers[0]['id'] ?? 'unknown';
                            $wc_customer_data['user_email'] = "customer{$customer_id}@wings-generated.local";
                            echo "<p>Generated email: {$wc_customer_data['user_email']}</p>\n";
                        }

                        // Fix display name if missing
                        if (empty($wc_customer_data['display_name'])) {
                            $customer_id = $customers[0]['id'] ?? 'unknown';
                            $wc_customer_data['display_name'] = "Wings Customer {$customer_id}";
                            echo "<p>Generated display name: {$wc_customer_data['display_name']}</p>\n";
                        }

                        // Re-test validation
                        $validation = Wings_Customer_Transformer::validate_woocommerce_customer($wc_customer_data);
                        if ($validation['valid']) {
                            echo "<p style='color: green;'>✓ Customer data validation passed after fixes</p>\n";
                        } else {
                            echo "<p style='color: red;'>✗ Customer data validation still failed: " . implode(', ', $validation['errors']) . "</p>\n";
                        }
                    }
                    
                    // Test customer creation
                    echo "<h4>3.4 Testing customer creation</h4>\n";
                    $customer_sync = new Wings_Customer_Sync();
                    
                    // Check if customer already exists
                    $existing_customer = get_user_by('email', $wc_customer_data['user_email']);
                    if ($existing_customer) {
                        echo "<p style='color: orange;'>⚠ Customer with email {$wc_customer_data['user_email']} already exists (ID: {$existing_customer->ID})</p>\n";
                        echo "<p>User roles: " . implode(', ', $existing_customer->roles) . "</p>\n";
                    } else {
                        echo "<p>Customer with email {$wc_customer_data['user_email']} does not exist. Attempting to create...</p>\n";
                        
                        // Use reflection to access private method
                        $reflection = new ReflectionClass($customer_sync);
                        $method = $reflection->getMethod('create_woocommerce_customer_with_role');
                        $method->setAccessible(true);
                        
                        $result = $method->invoke($customer_sync, $wc_customer_data, 'professionals');
                        
                        if (is_wp_error($result)) {
                            echo "<p style='color: red;'>✗ Customer creation failed: " . $result->get_error_message() . "</p>\n";
                        } else {
                            echo "<p style='color: green;'>✓ Customer created successfully with ID: {$result}</p>\n";
                            
                            // Check the created user
                            $created_user = get_user_by('ID', $result);
                            if ($created_user) {
                                echo "<p>Created user roles: " . implode(', ', $created_user->roles) . "</p>\n";
                                echo "<p>User email: {$created_user->user_email}</p>\n";
                                echo "<p>Display name: {$created_user->display_name}</p>\n";
                            }
                        }
                    }
                }
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Exception: " . $e->getMessage() . "</p>\n";
    }
    
    // Test sync method
    echo "<h3>4. Testing sync method</h3>\n";
    try {
        $customer_sync = new Wings_Customer_Sync();
        
        // Use reflection to check if customer sync is enabled
        $reflection = new ReflectionClass($customer_sync);
        $method = $reflection->getMethod('is_customer_sync_enabled');
        $method->setAccessible(true);
        
        $is_enabled = $method->invoke($customer_sync);
        if ($is_enabled) {
            echo "<p style='color: green;'>✓ Customer sync is enabled in class</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Customer sync is NOT enabled in class</p>\n";
        }
        
        // Test the sync method
        echo "<p>Testing sync_customers_from_wings method...</p>\n";
        ob_start();
        $customer_sync->sync_customers_from_wings();
        $output = ob_get_clean();
        
        echo "<p>Sync method executed. Check logs for results.</p>\n";
        if ($output) {
            echo "<pre>Output: {$output}</pre>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Sync test exception: " . $e->getMessage() . "</p>\n";
    }
    
    // Show recent log entries
    echo "<h3>5. Recent log entries</h3>\n";
    $log_entries = Wings_WooCommerce_Sync::get_log();
    $recent_entries = array_slice($log_entries, -10);
    
    if (empty($recent_entries)) {
        echo "<p>No log entries found.</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>Time</th><th>Type</th><th>Message</th></tr>\n";
        foreach (array_reverse($recent_entries) as $entry) {
            $color = $entry['type'] === 'error' ? 'red' : ($entry['type'] === 'warning' ? 'orange' : 'black');
            echo "<tr><td>{$entry['timestamp']}</td><td style='color: {$color};'>{$entry['type']}</td><td>{$entry['message']}</td></tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>Test completed!</h3>\n";
}

// Run the test if accessed directly
if (!defined('WINGS_SYNC_TESTING')) {
    test_customer_creation_debug();
}
?>
