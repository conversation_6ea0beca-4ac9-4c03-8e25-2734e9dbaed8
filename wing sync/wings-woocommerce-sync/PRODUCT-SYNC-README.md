# 📦 Wings Product Sync Documentation

## Overview

The Wings Product Sync module synchronizes product inventory, stock levels, and prices between Wings Portal ERP system and WooCommerce. This ensures your online store always reflects accurate product information from your ERP system.

## Features

### ✅ Core Functionality
- **Real-time Stock Synchronization** - Automatic inventory updates
- **Price Synchronization** - Keep prices current with Wings Portal
- **Product Information Sync** - Names, descriptions, categories
- **Multi-warehouse Support** - Handle complex inventory setups
- **Batch Processing** - Efficient handling of large catalogs
- **SKU Mapping** - Reliable product identification

### ✅ Advanced Features
- **Scheduled Sync** - Configurable intervals (5-60 minutes)
- **Manual Sync** - On-demand synchronization
- **Performance Optimization** - Memory and timeout management
- **Comprehensive Logging** - Detailed operation tracking
- **Error Handling** - Graceful failure recovery

## Wings Portal API Endpoints

### Product Data Retrieval

**Primary Endpoint**: `local.artikal.svi`
- **Purpose**: Fetch all products with pagination
- **Parameters**:
  - `dStart`: Starting record number
  - `dLength`: Number of records to fetch
  - `magacin`: Warehouse filter (optional)
  - `output`: Response format (jsonapi)

**Stock Information**: `lager.artikal.magacin`
- **Purpose**: Get warehouse stock levels
- **Parameters**:
  - `artikal`: Product ID
- **Returns**: Stock quantities by warehouse

### Authentication
- **Endpoint**: `system.user.log`
- **Method**: POST
- **Parameters**: `aUn` (username), `aUp` (password)
- **Returns**: Session token for subsequent requests

## Data Mapping

### Wings → WooCommerce Product Fields

| Wings Field | WooCommerce Field | Type | Description |
|-------------|-------------------|------|-------------|
| `id` | `meta: wings_product_id` | string | Wings product identifier |
| `sifra` | `sku` | string | Product SKU/code |
| `naziv` | `name` | string | Product name |
| `opis` | `description` | text | Product description |
| `cena` | `regular_price` | decimal | Product price |
| `kategorija` | `product_cat` | taxonomy | Product category |
| `jedinica` | `meta: wings_unit` | string | Unit of measure |
| `status` | `status` | string | Product status |

### Stock Data Mapping

| Wings Field | WooCommerce Field | Description |
|-------------|-------------------|-------------|
| `stanje` | `stock_quantity` | Available stock |
| `rezervisano` | `meta: wings_reserved` | Reserved quantity |
| `magacin` | `meta: wings_warehouse` | Warehouse location |

## Synchronization Process

### 1. Authentication
```php
$api = Wings_API::get_instance();
$connection = $api->test_connection();
```

### 2. Product Retrieval
```php
// Get products in batches
$products = $api->get_articles($start, $batch_size, $warehouse_id);

// Get all products with pagination
$all_products = $api->get_all_articles($warehouse_id, $batch_size);
```

### 3. Data Transformation
```php
// Transform Wings product to WooCommerce format
$wc_product_data = transform_wings_to_wc_product($wings_product);

// Validate product data
$validation = validate_product_data($wc_product_data);
```

### 4. Product Update/Creation
```php
// Find existing product by SKU
$existing_product = wc_get_product_id_by_sku($sku);

if ($existing_product) {
    // Update existing product
    update_woocommerce_product($existing_product, $product_data);
} else {
    // Create new product
    create_woocommerce_product($product_data);
}
```

### 5. Stock Synchronization
```php
// Update stock levels
$product = wc_get_product($product_id);
$product->set_stock_quantity($stock_level);
$product->save();
```

## Configuration Options

### Basic Settings

**API Configuration**
- `api_url`: Wings Portal API URL
- `api_alias`: Client alias for Wings Portal
- `api_username`: Wings Portal username
- `api_password`: Wings Portal password

**Sync Settings**
- `auto_sync`: Enable/disable automatic sync
- `sync_interval`: Sync frequency (5, 15, 30, 60 minutes)
- `batch_size`: Products per batch (10-200)
- `timeout`: API request timeout (30-300 seconds)

### Advanced Settings

**Warehouse Configuration**
- `default_warehouse`: Default warehouse for stock
- `warehouse_mapping`: Map Wings warehouses to WC locations
- `multi_warehouse`: Enable multi-warehouse support

**Product Rules**
- `create_missing`: Auto-create missing products
- `update_prices`: Enable price synchronization
- `update_stock`: Enable stock synchronization
- `update_descriptions`: Sync product descriptions

## Monitoring and Logging

### Sync Statistics
- **Total Products**: Number of products in Wings Portal
- **Synced Products**: Successfully synchronized products
- **Last Sync**: Timestamp of last synchronization
- **Sync Duration**: Time taken for last sync
- **Error Count**: Number of sync errors

### Log Levels
- **Info**: Normal operations and successful syncs
- **Warning**: Non-critical issues (missing data, etc.)
- **Error**: Failed operations requiring attention
- **Debug**: Detailed technical information

### Log Entries
```
[2024-01-15 10:30:15] INFO: Starting product sync
[2024-01-15 10:30:16] INFO: Retrieved 150 products from Wings Portal
[2024-01-15 10:30:18] INFO: Updated product SKU-001 stock: 25 units
[2024-01-15 10:30:20] WARNING: Product SKU-002 missing description
[2024-01-15 10:30:22] INFO: Sync completed. 148/150 products updated
```

## Performance Optimization

### Batch Processing
- **Small Stores** (< 1000 products): Batch size 50-100
- **Medium Stores** (1000-5000 products): Batch size 25-50
- **Large Stores** (> 5000 products): Batch size 10-25

### Memory Management
- **PHP Memory Limit**: Minimum 256MB recommended
- **Execution Time**: Set to 300+ seconds for large syncs
- **Database Optimization**: Regular cleanup of logs and cache

### Caching Strategy
- **API Response Caching**: 2-minute cache for stock data
- **Product Data Caching**: Cache transformed product data
- **Clear Cache**: Automatic cache clearing after sync

## Error Handling

### Common Errors

**API Connection Errors**
- **Cause**: Network issues, wrong credentials
- **Solution**: Check API settings, test connection
- **Prevention**: Use connection testing before sync

**Product Creation Errors**
- **Cause**: Missing required fields, invalid data
- **Solution**: Validate product data, check WooCommerce requirements
- **Prevention**: Implement data validation

**Stock Update Errors**
- **Cause**: Product not found, invalid stock values
- **Solution**: Verify product exists, check stock data format
- **Prevention**: Use SKU-based product lookup

### Error Recovery
- **Retry Logic**: Automatic retry for temporary failures
- **Partial Sync**: Continue sync even if some products fail
- **Error Reporting**: Detailed error logs for troubleshooting

## Testing

### Manual Testing
1. **Connection Test**: Verify API connectivity
2. **Single Product Sync**: Test with one product
3. **Batch Sync**: Test with small batch (5-10 products)
4. **Full Sync**: Complete synchronization test

### Automated Testing
```php
// Test API connection
$test_result = $api->test_connection();

// Test product retrieval
$products = $api->get_articles(0, 5);

// Test data transformation
$wc_data = transform_wings_to_wc_product($wings_product);
```

### Test Environment
- **Wings Portal Test URL**: `https://portal.wings.rs/grosstest`
- **Test Credentials**: Username: `aql`, Password: `grossaql`
- **Safe Testing**: No real data affected

## Troubleshooting

### Sync Not Running
1. Check if auto-sync is enabled
2. Verify cron jobs are working
3. Check API credentials
4. Review error logs

### Products Not Updating
1. Verify SKU mapping is correct
2. Check product permissions
3. Review data validation errors
4. Test with single product

### Performance Issues
1. Reduce batch size
2. Increase timeout values
3. Check server resources
4. Optimize database

### Stock Discrepancies
1. Verify warehouse mapping
2. Check stock calculation logic
3. Review Wings Portal stock data
4. Test stock update process

## Best Practices

### Setup
- Always test in staging environment first
- Use Wings Portal test environment for initial setup
- Configure appropriate batch sizes for your server
- Set up monitoring and alerting

### Maintenance
- Regular log review and cleanup
- Monitor sync performance metrics
- Keep plugin updated
- Backup before major changes

### Security
- Use strong API credentials
- Limit API access permissions
- Regular credential rotation
- Monitor for unauthorized access

## Support

### Documentation
- Complete API documentation in plugin files
- Detailed error code reference
- Configuration examples and templates

### Debugging
- Enable WordPress debug mode
- Review Wings Portal API logs
- Use plugin's built-in testing tools
- Check WooCommerce system status

### Getting Help
- Review plugin logs first
- Test with Wings Portal test environment
- Provide specific error messages when seeking support
- Include relevant configuration details
