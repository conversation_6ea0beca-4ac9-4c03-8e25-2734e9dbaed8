# Wings Portal Customer Sync Documentation

## Overview

This document explains the customer synchronization functionality between Wings Portal and WooCommerce, implemented using pure, self-contained functions for clean and maintainable code.

## Architecture

### Core Components

1. **Wings_API** - Extended with customer-specific API methods
2. **Wings_Customer_Transformer** - Pure functions for data transformation
3. **Wings_Customer_Sync** - Main synchronization orchestrator
4. **Test Suite** - Comprehensive testing framework

### Design Principles

- **Pure Functions**: No side effects, predictable outputs
- **Self-Contained**: Each function handles its own validation and error handling
- **Clean Code**: Single responsibility, clear naming, comprehensive documentation
- **Stable Environment**: Built on existing Wings WooCommerce Sync foundation

## Wings Portal API Endpoints

### Customer Reading Operations

```php
// Get customers with pagination and filters
$api->get_customers($start, $length, $filters);

// Get all customers (handles pagination automatically)
$api->get_all_customers($filters, $batch_size);

// Get specific customer details
$api->get_customer_info($customer_id);
```

**API Endpoint**: `local.kupac.svi`
**Parameters**:
- `dStart`: Starting record (pagination)
- `dLength`: Number of records to fetch
- `komercijalista`: Filter by sales representative
- `status`: Filter by customer status
- `search`: Search term

### Customer Writing Operations

```php
// Create new customer in Wings
$api->create_customer($customer_data);
```

**API Endpoint**: `local.kupac.nov`
**Required Fields**:
- `naziv`: Customer name/company
- `email`: Email address
- `adresa`: Address
- `mesto`: City
- `telefon`: Phone number
- `status`: Customer status (default: 'aktivan')

## Data Transformation

### Wings to WooCommerce Mapping

| Wings Field | WooCommerce Field | Notes |
|-------------|-------------------|-------|
| `naziv` | `display_name`, `billing_company` | Company name or full name |
| `email` | `user_email`, `billing_email` | Primary email |
| `adresa` | `billing_address_1` | Street address |
| `mesto` | `billing_city` | City |
| `telefon` | `billing_phone` | Phone number |
| `sifra` | `meta: wings_customer_code` | Customer code |
| `pib` | `meta: wings_pib` | Tax ID |
| `status` | `meta: wings_status` | Customer status |
| `komercijalista` | `meta: wings_sales_rep` | Sales representative |
| `klasa` | `meta: wings_customer_class` | Customer class |

### Pure Transformation Functions

```php
// Transform Wings customer to WooCommerce format
$wc_customer = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);

// Transform WooCommerce customer to Wings format
$wings_customer = Wings_Customer_Transformer::woocommerce_to_wings($wc_customer);

// Validate customer data
$validation = Wings_Customer_Transformer::validate_wings_customer($customer_data);
$validation = Wings_Customer_Transformer::validate_woocommerce_customer($customer_data);

// Compare customers for sync decisions
$comparison = Wings_Customer_Transformer::compare_customers($wings_customer, $wc_customer);
```

## Synchronization Workflow

### WooCommerce → Wings (Real-time)

1. **Customer Registration**: Hook into `woocommerce_created_customer`
2. **Address Update**: Hook into `woocommerce_customer_save_address`
3. **Account Update**: Hook into `woocommerce_save_account_details`

```php
// Automatic sync on customer events
add_action('woocommerce_created_customer', array($sync, 'on_customer_created'), 10, 3);
add_action('woocommerce_customer_save_address', array($sync, 'on_customer_address_updated'), 10, 2);
add_action('woocommerce_save_account_details', array($sync, 'on_customer_account_updated'), 10, 1);
```

### Wings → WooCommerce (Scheduled)

1. **Scheduled Sync**: Cron job `wings_sync_customers_cron`
2. **Batch Processing**: Handle large customer lists with pagination
3. **Conflict Resolution**: Compare timestamps and data differences

```php
// Scheduled sync from Wings to WooCommerce
add_action('wings_sync_customers_cron', array($sync, 'sync_customers_from_wings'));
```

## Configuration

### Settings

Add to Wings sync settings:

```php
$settings = array(
    'enable_customer_sync' => true,           // Enable/disable customer sync
    'customer_sync_interval' => 'hourly',     // Sync frequency
    'customer_batch_size' => 50,              // Batch size for sync
);
```

### Environment Setup

The customer sync uses the existing stable environment:

- **API Authentication**: Reuses existing Wings API session management
- **Error Handling**: Leverages existing logging and error reporting
- **Settings Management**: Extends current settings framework
- **HPOS Compatibility**: Maintains WooCommerce HPOS support

## Testing

### Test Suite Usage

```php
// Run comprehensive tests
include 'test-customer-sync.php';
run_wings_customer_sync_tests();

// Individual test functions
test_wings_customer_api();          // Test API connectivity and data retrieval
test_customer_transformation();     // Test data transformation functions
test_customer_creation();          // Test customer creation workflow
test_sync_workflow();              // Test complete sync process
```

### Test Environment

- **Wings Portal Test URL**: `https://portal.wings.rs/grosstest`
- **Test Credentials**: Username: `aql`, Password: `grossaql`
- **Safe Testing**: Test functions include validation without actual data creation

## Where Customers Are Created

### WordPress/WooCommerce Database Structure

When customers are synchronized from Wings Portal to WooCommerce, they are stored in the standard WordPress database tables:

#### 1. **wp_users** Table
- **Purpose**: Main user account record
- **Key Fields**:
  - `user_login`: Customer email (used as username)
  - `user_email`: Customer email address
  - `display_name`: Customer/company name
  - `user_registered`: Registration timestamp
  - `user_status`: Account status

#### 2. **wp_usermeta** Table
- **Purpose**: Customer details and metadata
- **Standard WooCommerce Fields**:
  - `first_name`, `last_name`: Customer name
  - `billing_first_name`, `billing_last_name`: Billing name
  - `billing_company`: Company name
  - `billing_address_1`: Street address
  - `billing_city`: City
  - `billing_phone`: Phone number
  - `billing_country`: Country code

- **Wings-Specific Fields**:
  - `wings_customer_id`: Wings Portal customer ID
  - `wings_customer_code`: Wings customer code (sifra)
  - `wings_pib`: Tax identification number
  - `wings_status`: Customer status in Wings
  - `wings_sales_rep`: Sales representative
  - `wings_customer_class`: Customer classification
  - `wings_last_sync`: Last synchronization timestamp

### Customer Creation Process

```php
// 1. Create WordPress user
$user_id = wp_insert_user(array(
    'user_login' => $email,
    'user_email' => $email,
    'user_pass' => wp_generate_password(),
    'role' => 'customer'
));

// 2. Create WooCommerce customer
$customer = new WC_Customer($user_id);
$customer->set_billing_address_1($address);
$customer->save();

// 3. Add Wings metadata
update_user_meta($user_id, 'wings_customer_id', $wings_id);
```

### Viewing Created Customers

Customers can be viewed in:
1. **WordPress Admin**: Users → All Users (filter by Customer role)
2. **WooCommerce**: WooCommerce → Customers
3. **Database**: Direct access to `wp_users` and `wp_usermeta` tables

## Implementation Status

### ✅ Completed

- [x] Wings API customer methods (`get_customers`, `get_all_customers`, `get_customer_info`, `create_customer`)
- [x] Pure transformation functions (bidirectional Wings ↔ WooCommerce)
- [x] Data validation functions
- [x] Customer comparison logic
- [x] WooCommerce hook integration
- [x] Scheduled sync framework
- [x] Comprehensive test suite
- [x] Settings integration
- [x] Error handling and logging
- [x] WooCommerce customer creation/update implementation

### 🚧 In Progress

- [x] WooCommerce customer creation/update implementation
- [ ] Wings customer update API method
- [ ] Background processing for large datasets
- [ ] Admin interface for sync monitoring
- [ ] Conflict resolution interface

### 📋 Next Steps

1. **Complete WooCommerce Integration**
   ```php
   private function create_woocommerce_customer($customer_data) {
       $customer = new WC_Customer();
       // Set customer properties
       $customer->save();
       return $customer->get_id();
   }
   ```

2. **Implement Wings Customer Update**
   ```php
   public function update_customer($customer_id, $customer_data) {
       return $this->make_request('local.kupac.update', $customer_data, 'POST');
   }
   ```

3. **Add Background Processing**
   ```php
   class Wings_Customer_Background_Processor extends WP_Background_Process {
       protected $action = 'wings_customer_sync';
       // Implement background processing
   }
   ```

## Usage Examples

### Manual Customer Sync

```php
// Initialize customer sync
$customer_sync = new Wings_Customer_Sync();

// Sync specific customer from WooCommerce to Wings
$result = $customer_sync->sync_customer_to_wings($wc_customer_id);

// Sync all customers from Wings to WooCommerce
$customer_sync->sync_customers_from_wings();
```

### Custom Integration

```php
// Get Wings customer data
$api = Wings_API::get_instance();
$wings_customers = $api->get_all_customers();

// Transform and validate
foreach ($wings_customers as $wings_customer) {
    $wc_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);
    $validation = Wings_Customer_Transformer::validate_woocommerce_customer($wc_data);
    
    if ($validation['valid']) {
        // Process valid customer data
    }
}
```

## Security Considerations

- **Input Validation**: All customer data is sanitized using WordPress functions
- **API Security**: Reuses existing secure API authentication
- **Data Privacy**: Customer data is handled according to WordPress privacy standards
- **Audit Trail**: All sync operations are logged for compliance

## Performance Optimization

- **Pagination**: Large customer lists are processed in batches
- **Caching**: API responses can be cached to reduce load
- **Background Processing**: Heavy operations run in background
- **Rate Limiting**: API calls respect Wings Portal rate limits

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check Wings Portal credentials
   - Verify API URL and alias
   - Test with Wings test environment

2. **Customer Data Validation Failed**
   - Check required fields (naziv, email)
   - Validate email format
   - Ensure customer name is not empty

3. **Sync Not Running**
   - Verify customer sync is enabled in settings
   - Check cron job scheduling
   - Review error logs for issues

### Debug Mode

Enable debug logging:
```php
Wings_WooCommerce_Sync::log('Debug message', 'debug');
```

View logs in Wings Sync admin panel or WordPress debug log.
