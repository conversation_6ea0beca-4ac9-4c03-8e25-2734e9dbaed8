<?php
/**
 * Plugin Name: Wings WooCommerce Sync
 * Plugin URI: https://yourwebsite.com/wings-woocommerce-sync
 * Description: Sinhronizuje artikle iz Wings Portal baze sa WooCommerce proizvodima.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wings-sync
 * Domain Path: /languages
 * Requires at least: 5.5
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.5
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WINGS_SYNC_VERSION', '1.0.0');
define('WINGS_SYNC_PLUGIN_FILE', __FILE__);
define('WINGS_SYNC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WINGS_SYNC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WINGS_SYNC_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Wings WooCommerce Sync Class
 */
class Wings_WooCommerce_Sync {

    /**
     * Plugin instance
     */
    private static $instance = null;

    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Load plugin text domain
        load_plugin_textdomain('wings-sync', false, dirname(WINGS_SYNC_PLUGIN_BASENAME) . '/languages');

        // Include required files
        $this->includes();

        // Initialize hooks
        $this->init_hooks();

        // Initialize classes
        $this->init_classes();
    }

    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('Wings WooCommerce Sync zahteva da WooCommerce bude instaliran i aktiviran.', 'wings-sync'); ?></p>
        </div>
        <?php
    }

    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once WINGS_SYNC_PLUGIN_DIR . 'includes/class-wings-api.php';
        require_once WINGS_SYNC_PLUGIN_DIR . 'includes/class-wings-sync.php';
        require_once WINGS_SYNC_PLUGIN_DIR . 'includes/class-wings-scheduler.php';

        // Customer sync classes
        require_once WINGS_SYNC_PLUGIN_DIR . 'includes/class-wings-customer-transformer.php';
        require_once WINGS_SYNC_PLUGIN_DIR . 'includes/class-wings-customer-sync.php';

        // Admin classes
        if (is_admin()) {
            require_once WINGS_SYNC_PLUGIN_DIR . 'includes/class-wings-admin.php';
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(WINGS_SYNC_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(WINGS_SYNC_PLUGIN_FILE, array($this, 'deactivate'));

        // Plugin action links
        add_filter('plugin_action_links_' . WINGS_SYNC_PLUGIN_BASENAME, array($this, 'plugin_action_links'));

        // HPOS compatibility
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
    }

    /**
     * Initialize classes
     */
    private function init_classes() {
        // Initialize scheduler
        Wings_Scheduler::get_instance();

        // Initialize customer sync
        new Wings_Customer_Sync();

        // Initialize admin if in admin area
        if (is_admin()) {
            Wings_Admin::get_instance();
        }
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create default options
        $default_settings = array(
            'api_url' => 'https://portal.wings.rs/api/v1/',
            'api_alias' => '',
            'api_username' => '',
            'api_password' => '',
            'auto_sync' => false,
            'sync_interval' => 15,
            'last_sync' => 0,
            'sync_enabled' => true,
            'batch_size' => 50,
            'timeout' => 60,
            'enable_customer_sync' => true,
            'customer_sync_interval' => 'hourly',
            'customer_batch_size' => 50
        );

        add_option('wings_sync_settings', $default_settings);
        add_option('wings_sync_log', array());

        // Create 'professionals' user role if it doesn't exist
        $this->create_professionals_role();

        // Schedule cron job if auto sync is enabled
        if (!wp_next_scheduled('wings_sync_cron_hook')) {
            wp_schedule_event(time(), 'wings_sync_interval', 'wings_sync_cron_hook');
        }

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create professionals user role
     */
    private function create_professionals_role() {
        // Check if role already exists
        if (get_role('professionals')) {
            return;
        }

        // Check if Ultimate Member is active
        if (class_exists('UM')) {
            // Create role via Ultimate Member
            $this->create_um_professionals_role();
        } else {
            // Create standard WordPress role
            $this->create_wp_professionals_role();
        }

        Wings_WooCommerce_Sync::log('Created professionals user role', 'info');
    }

    /**
     * Create professionals role via Ultimate Member
     */
    private function create_um_professionals_role() {
        // Get UM roles
        $um_roles = UM()->roles();

        // Create professionals role with Ultimate Member
        $role_data = array(
            'name' => 'Professionals',
            'capabilities' => array(
                'read' => true,
                'um_member_directory' => true,
                'um_user_profile' => true
            ),
            'can_edit_everyone' => false,
            'can_delete_everyone' => false,
            'priority' => 1
        );

        // Add the role
        add_role('professionals', 'Professionals', $role_data['capabilities']);

        // If UM has additional role management, use it
        if (method_exists($um_roles, 'add_role')) {
            $um_roles->add_role('professionals', $role_data);
        }
    }

    /**
     * Create standard WordPress professionals role
     */
    private function create_wp_professionals_role() {
        // Get customer role capabilities as base
        $customer_role = get_role('customer');
        $capabilities = $customer_role ? $customer_role->capabilities : array(
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false
        );

        // Add professionals role with customer capabilities plus some additional ones
        add_role('professionals', __('Professionals', 'wings-sync'), array_merge($capabilities, array(
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
            'view_woocommerce_reports' => false,
            'manage_woocommerce' => false
        )));
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled cron jobs
        wp_clear_scheduled_hook('wings_sync_cron_hook');

        // Clear transients
        delete_transient('wings_api_cache');
    }

    /**
     * Add plugin action links
     */
    public function plugin_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=wings-sync') . '">' . __('Podešavanja', 'wings-sync') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Declare HPOS compatibility
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', WINGS_SYNC_PLUGIN_FILE, true);
        }
    }

    /**
     * Get plugin settings
     */
    public static function get_settings() {
        return get_option('wings_sync_settings', array());
    }

    /**
     * Update plugin settings
     */
    public static function update_settings($settings) {
        return update_option('wings_sync_settings', $settings);
    }

    /**
     * Log sync activity
     */
    public static function log($message, $type = 'info') {
        $log = get_option('wings_sync_log', array());
        
        $log[] = array(
            'timestamp' => current_time('mysql'),
            'type' => $type,
            'message' => $message
        );

        // Keep only last 100 log entries
        if (count($log) > 100) {
            $log = array_slice($log, -100);
        }

        update_option('wings_sync_log', $log);
    }

    /**
     * Get sync log
     */
    public static function get_log() {
        return get_option('wings_sync_log', array());
    }

    /**
     * Clear sync log
     */
    public static function clear_log() {
        return update_option('wings_sync_log', array());
    }
}

/**
 * Add custom cron interval
 */
add_filter('cron_schedules', function($schedules) {
    $settings = Wings_WooCommerce_Sync::get_settings();
    $interval = isset($settings['sync_interval']) ? intval($settings['sync_interval']) : 15;
    
    $schedules['wings_sync_interval'] = array(
        'interval' => $interval * 60, // Convert minutes to seconds
        'display' => sprintf(__('Svakih %d minuta', 'wings-sync'), $interval)
    );
    
    return $schedules;
});

// Initialize plugin
Wings_WooCommerce_Sync::get_instance();
