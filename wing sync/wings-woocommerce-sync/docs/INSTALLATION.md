# Wings WooCommerce Sync - Instalacija i Konfiguracija

## Preduslovi

Prije instalacije, provjerite da li su ispunjeni sljedeći uslovi:

### Sistemski zahtevi
- **WordPress**: 5.5 ili noviji
- **WooCommerce**: 5.0 ili noviji  
- **PHP**: 7.4 ili noviji
- **MySQL**: 5.6 ili noviji
- **cURL**: Omogućen za API pozive

### Wings Portal pristup
- Valjan Wings Portal nalog
- API kredencijali (alias, korisničko ime, lozinka)
- Mrežni pristup do Wings Portal servera

## Korak 1: Instalacija plugin-a

### Opcija A: Upload preko WordPress Admin-a

1. **Pakovanje plugin-a**
   ```bash
   cd wings-woocommerce-sync
   zip -r wings-woocommerce-sync.zip .
   ```

2. **Upload u WordPress**
   - Idite na WordPress Admin → Plugins → Add New
   - Kliknite "Upload Plugin"
   - Izaberite `wings-woocommerce-sync.zip`
   - Kliknite "Install Now"

### Opcija B: FTP upload

1. **Upload fajlova**
   ```
   /wp-content/plugins/wings-woocommerce-sync/
   ```

2. **Postavite dozvole**
   ```bash
   chmod 755 /wp-content/plugins/wings-woocommerce-sync/
   chmod 644 /wp-content/plugins/wings-woocommerce-sync/*.php
   ```

## Korak 2: Aktivacija

1. Idite na WordPress Admin → Plugins
2. Pronađite "Wings WooCommerce Sync"
3. Kliknite "Activate"

**Napomena**: Plugin će automatski proveriti da li je WooCommerce aktivan.

## Korak 3: Osnovna konfiguracija

### 3.1 Pristup podešavanjima

1. Idite na WooCommerce → Wings Sync
2. Otvorit će se admin stranica plugin-a

### 3.2 API konfiguracija

Unesite sledeće podatke:

```
Wings API URL: https://portal.wings.rs/api/v1/
Wings Alias: [vaš-alias]
Korisničko ime: [vaše-korisničko-ime]
Lozinka: [vaša-lozinka]
```

**Za test environment:**
```
Wings API URL: https://portal.wings.rs/api/v1/
Wings Alias: grosstest
Korisničko ime: aql
Lozinka: grossaql
```

### 3.3 Test konekcije

1. Kliknite "Testiraj konekciju"
2. Sačekajte potvrdu uspešne konekcije
3. Ako test ne prođe, proverite:
   - Ispravnost kredencijala
   - Mrežnu konekciju
   - Firewall podešavanja

## Korak 4: Podešavanje sinhronizacije

### 4.1 Osnovne opcije

- **Automatska sinhronizacija**: Omogućite za redovnu sinhronizaciju
- **Interval sinhronizacije**: Preporučeno 15-30 minuta
- **Veličina batch-a**: Počnite sa 50, prilagodite prema performansama
- **Timeout**: 60 sekundi za standardne instalacije

### 4.2 Napredne opcije

Za velike inventare (1000+ proizvoda):
- Smanjite batch veličinu na 25-30
- Povećajte timeout na 120-180 sekundi
- Razmislite o povećanju PHP memory_limit

## Korak 5: Prva sinhronizacija

### 5.1 Manualna sinhronizacija

1. Kliknite "Sinhronizuj sada"
2. Pratite napredak u real-time
3. Pregledajte rezultate i log

### 5.2 Provera rezultata

Nakon sinhronizacije proverite:
- WooCommerce → Products (broj novih proizvoda)
- Wings Sync → Log (greške i upozorenja)
- Status panel (statistike sinhronizacije)

## Korak 6: Automatska sinhronizacija

### 6.1 Omogućavanje

1. Označite "Automatska sinhronizacija"
2. Izaberite interval (preporučeno 15 minuta)
3. Sačuvajte podešavanja

### 6.2 Verifikacija cron job-a

Proverite da li je cron job zakazan:
```php
// Dodajte u functions.php za debug
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        $next_sync = wp_next_scheduled('wings_sync_cron_hook');
        echo '<!-- Next Wings sync: ' . date('Y-m-d H:i:s', $next_sync) . ' -->';
    }
});
```

## Troubleshooting

### Problem: "WooCommerce missing notice"
**Rešenje**: Instalirajte i aktivirajte WooCommerce plugin

### Problem: "Login neuspešan"
**Mogući uzroci**:
- Neispravni kredencijali
- Mrežni problemi
- Firewall blokiranje

**Rešenje**:
1. Proverite kredencijale
2. Testirajte konekciju van WordPress-a
3. Kontaktirajte hosting provajdera

### Problem: "Timeout greška"
**Rešenje**:
1. Povećajte timeout vrednost
2. Smanjite batch veličinu
3. Proverite server performanse

### Problem: "Memory limit exceeded"
**Rešenje**:
```php
// Dodajte u wp-config.php
ini_set('memory_limit', '512M');
```

### Problem: Cron job se ne izvršava
**Mogući uzroci**:
- DISABLE_WP_CRON je true
- Server ne podržava cron job-ove

**Rešenje**:
1. Proverite wp-config.php za DISABLE_WP_CRON
2. Postavite server-level cron job:
   ```bash
   */15 * * * * wget -q -O - http://yoursite.com/wp-cron.php?doing_wp_cron
   ```

## Održavanje

### Redovne provere

1. **Nedeljno**: Proverite log za greške
2. **Mesečno**: Proverite performanse sinhronizacije
3. **Kvartalno**: Ažurirajte plugin ako je dostupna nova verzija

### Backup

Pre ažuriranja plugin-a:
1. Napravite backup baze podataka
2. Napravite backup plugin fajlova
3. Testirajte na staging environment-u

### Monitoring

Postavite monitoring za:
- Greške u sinhronizaciji
- Performanse API poziva
- Broj sinhronizovanih proizvoda

## Podrška

Za dodatnu pomoć:
- **Email**: <EMAIL>
- **Dokumentacija**: [Link to full docs]
- **GitHub Issues**: [Link to repository]

---

**Napomena**: Ovaj plugin je optimizovan za Wings Portal API. Za specifične probleme sa Wings sistemom, kontaktirajte Wings support tim.
