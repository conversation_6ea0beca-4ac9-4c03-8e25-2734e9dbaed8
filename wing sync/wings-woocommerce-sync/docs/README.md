# Wings WooCommerce Sync

WordPress plugin za sinhronizaciju artikala iz Wings Portal baze sa WooCommerce proizvodima.

## Opis

Wings WooCommerce Sync omogućava automatsku i manualnu sinhronizaciju proizvoda između Wings Portal sistema i WooCommerce online prodavnice. Plugin mapira artikle iz Wings baze u WooCommerce proizvode, ažurira cene, količine na stanju i ostale relevantne informacije.

## Funkcionalnosti

### ✅ Osnovne funkcionalnosti
- **API konfiguracija** - Jednostavno podešavanje Wings Portal API kredencijala
- **Test konekcije** - Provera ispravnosti API podešavanja
- **Manualna sinhronizacija** - Pokretanje sinhronizacije na zahtev
- **Automatska sinhronizacija** - Zakazana sinhronizacija u intervalima
- **Detaljno logovanje** - Praćenje svih aktivnosti i grešaka
- **Progress tracking** - Real-time praćenje napretka sinhronizacije

### 🔄 Sinhronizacija podataka
- **Mapiranje artikala** - Wings ID → WooCommerce meta
- **SKU sinhronizacija** - Wings šifra → WooCommerce SKU
- **Cene** - Automatsko ažuriranje cena
- **Stanje** - Sinhronizacija količina na stanju
- **Status dostupnosti** - Automatsko postavljanje stock_status
- **Meta podaci** - Čuvanje dodatnih Wings informacija

### 🛡️ Sigurnost i performanse
- **HPOS kompatibilnost** - Podrška za High-Performance Order Storage
- **Batch processing** - Obrađivanje u manjim grupama
- **Error handling** - Robusno rukovanje greškama
- **Cache sistem** - Optimizovane API pozive
- **Background processing** - Sinhronizacija u pozadini

## Sistemski zahtevi

- **WordPress**: 5.5 ili noviji
- **WooCommerce**: 5.0 ili noviji
- **PHP**: 7.4 ili noviji
- **MySQL**: 5.6 ili noviji

## Instalacija

1. **Upload plugin-a**
   ```
   wp-content/plugins/wings-woocommerce-sync/
   ```

2. **Aktivacija**
   - Idite na WordPress Admin → Plugins
   - Pronađite "Wings WooCommerce Sync"
   - Kliknite "Activate"

3. **Konfiguracija**
   - Idite na WooCommerce → Wings Sync
   - Unesite API kredencijale
   - Testirajte konekciju
   - Pokrenite prvu sinhronizaciju

## Konfiguracija

### API podešavanja

```
Wings API URL: https://portal.wings.rs/api/v1/
Wings Alias: grosstest
Korisničko ime: aql
Lozinka: grossaql
```

### Opcije sinhronizacije

- **Automatska sinhronizacija**: Uključi/isključi
- **Interval**: 5, 15, 30 minuta ili 1 sat
- **Batch veličina**: 10-200 proizvoda po pozivu
- **Timeout**: 30-300 sekundi

## Korišćenje

### Manualna sinhronizacija

1. Idite na WooCommerce → Wings Sync
2. Kliknite "Sinhronizuj sada"
3. Pratite napredak u real-time
4. Pregledajte rezultate i log

### Automatska sinhronizacija

1. Omogućite "Automatska sinhronizacija"
2. Izaberite interval
3. Sačuvajte podešavanja
4. Plugin će automatski sinhronizovati proizvode

### Praćenje aktivnosti

- **Status panel** - Pregled osnovnih statistika
- **Log sinhronizacije** - Detaljno praćenje aktivnosti
- **Email obaveštenja** - Automatska obaveštenja o greškama

## API Endpoints

Plugin koristi sledeće Wings Portal API endpoints:

- `system.user.log` - Autentifikacija
- `lager.magacin.svi` - Lista magacina
- `local.cache.artikal` - Artikli sa stanjem
- `lager.magkol.svi` - Stanje po magacinima
- `lager.magacin.artikli` - Detaljno stanje magacina

## Mapiranje podataka

| Wings polje | WooCommerce polje | Opis |
|-------------|-------------------|------|
| `id` | `_wings_id` (meta) | Jedinstveni ID artikla |
| `sifra` | `sku` | Šifra proizvoda |
| `naziv` | `post_title` | Naziv proizvoda |
| `cena` | `_regular_price` | Osnovna cena |
| `kolicina` | `_stock` | Količina na stanju |
| `barkod` | `_wings_barcode` (meta) | Barkod |
| `proizvodjac` | `_wings_manufacturer` (meta) | Proizvođač |

## Troubleshooting

### Česte greške

**"Login neuspešan"**
- Proverite API kredencijale
- Proverite mrežnu konekciju
- Kontaktirajte Wings Portal support

**"Timeout greška"**
- Povećajte timeout vrednost
- Smanjite batch veličinu
- Proverite server performanse

**"Nedostaju proizvodi"**
- Proverite Wings API odgovor
- Proverite mapiranje podataka
- Pregledajte log za greške

### Debug informacije

Za debug informacije, dodajte u `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Log fajlovi se čuvaju u `/wp-content/debug.log`

## Podrška

- **Email**: <EMAIL>
- **Dokumentacija**: [Link to docs]
- **GitHub**: [Link to repository]

## Changelog

### 1.0.0
- Početna verzija
- Osnovna sinhronizacija funkcionalnost
- Admin interface
- Automatska sinhronizacija
- HPOS kompatibilnost

## Licenca

GPL v2 or later

## Autor

Developed by [Your Company Name]

---

**Napomena**: Ovaj plugin je dizajniran specifično za Wings Portal API. Za podršku Wings Portal sistema, kontaktirajte Wings support tim.
