# Wings Customer Sync - Testing Guide

## 🧪 How to Test the Customer Sync Plugin

This guide explains how to test the Wings customer synchronization functionality step by step.

## Prerequisites

1. **WordPress Installation** with WooCommerce active
2. **Wings WooCommerce Sync Plugin** installed and activated
3. **Test Environment Access** to Wings Portal

## Test Environment Setup

### Wings Portal Test Environment

The plugin is configured to work with Wings Portal test environment:

```
URL: https://portal.wings.rs/grosstest
Username: aql
Password: grossaql
```

### Plugin Configuration

1. Go to **WooCommerce > Wings Sync** in WordPress admin
2. Configure the API settings:
   - **API URL**: `https://portal.wings.rs/api/v1/`
   - **API Alias**: `grosstest`
   - **Username**: `aql`
   - **Password**: `grossaql`
3. Enable customer sync in settings
4. Save settings

## Testing Methods

### Method 1: Admin Panel Test (Recommended)

This is the easiest way to test the customer sync functionality.

**Steps:**

1. Navigate to your Wings Sync admin page:
   ```
   /wp-admin/admin.php?page=wings-sync
   ```

2. Add the test parameter to the URL:
   ```
   /wp-admin/admin.php?page=wings-sync&run_wings_customer_tests=1
   ```

3. Press Enter - the test suite will automatically run and display results

**Expected Output:**
```
Wings Customer Sync Test Suite
==============================

Testing Customer Data Transformation
✓ Wings to WooCommerce transformation working
✓ WooCommerce to Wings transformation working
✓ Data validation passed
✓ Customer comparison logic working

Testing Wings Customer API
✓ API Connection successful!
✓ Retrieved 5 customers from Wings Portal
✓ Customer data structure correct

Testing Customer Creation
✓ Customer data validation passed
⚠ Customer creation test skipped (test mode)

Testing Complete Sync Workflow
✓ All components working correctly
```

### Method 2: WP-CLI Test

If you have WP-CLI installed on your server:

```bash
# Navigate to WordPress root directory
cd /path/to/your/wordpress

# Run the test file
wp eval-file wp-content/plugins/wings-woocommerce-sync/test-customer-sync.php
```

### Method 3: Direct PHP Test

Create a test file in your WordPress root:

**test-wings-customers.php:**
```php
<?php
// Load WordPress
require_once('wp-config.php');

// Include the test file
include_once('wp-content/plugins/wings-woocommerce-sync/test-customer-sync.php');

// Run tests
run_wings_customer_sync_tests();
?>
```

Then access: `https://yoursite.com/test-wings-customers.php`

### Method 4: Functions.php Test

Add to your theme's `functions.php` (temporarily):

```php
// Add this temporarily for testing
add_action('init', function() {
    if (isset($_GET['test_wings_customers']) && current_user_can('manage_options')) {
        include_once(ABSPATH . 'wp-content/plugins/wings-woocommerce-sync/test-customer-sync.php');
        run_wings_customer_sync_tests();
        exit;
    }
});
```

Then visit: `https://yoursite.com/?test_wings_customers=1`

## What the Tests Check

### 1. API Connection Test
- ✅ Connects to Wings Portal API
- ✅ Authenticates with test credentials
- ✅ Retrieves customer list
- ✅ Validates API response structure

### 2. Data Transformation Test
- ✅ Wings → WooCommerce data conversion
- ✅ WooCommerce → Wings data conversion
- ✅ Data validation for both formats
- ✅ Field mapping accuracy
- ✅ Customer comparison logic

### 3. Customer Creation Test
- ✅ Data validation before creation
- ✅ Required field checking
- ✅ Email format validation
- ⚠️ Actual creation (disabled in test mode)

### 4. Complete Workflow Test
- ✅ End-to-end process verification
- ✅ Component integration check
- ✅ Error handling validation

## Sample Test Output

```html
Wings Customer Sync Test Suite
==============================

Testing Customer Data Transformation
-------------------------------------

1. Wings to WooCommerce Transformation
Original Wings Data:
Array
(
    [id] => 12345
    [sifra] => CUST001
    [naziv] => Test Company d.o.o.
    [email] => <EMAIL>
    [adresa] => Test Street 123
    [mesto] => Belgrade
    [telefon] => +381 11 123 4567
    [pib] => *********
    [status] => aktivan
)

Transformed WooCommerce Data:
Array
(
    [user_email] => <EMAIL>
    [display_name] => Test Company d.o.o.
    [first_name] => Test
    [last_name] => Company d.o.o.
    [billing] => Array
    (
        [company] => Test Company d.o.o.
        [address_1] => Test Street 123
        [city] => Belgrade
        [phone] => +381 11 123 4567
        [email] => <EMAIL>
    )
    [meta_data] => Array
    (
        [wings_customer_id] => 12345
        [wings_customer_code] => CUST001
        [wings_pib] => *********
    )
)

✓ Transformation successful!

Testing Wings Customer API
--------------------------

1. Testing API Connection
✓ Connection successful!

2. Testing Customer Retrieval
Retrieved 5 customers
Total customers in Wings: 150

Sample Customer Data:
Array
(
    [id] => 67890
    [naziv] => Real Customer Name
    [email] => <EMAIL>
    [adresa] => Real Address 456
    [mesto] => Novi Sad
)

✓ API retrieval successful!

Test Suite Complete
===================
All customer sync components tested successfully!
```

## Troubleshooting

### Common Issues

**1. "Wings_API class not found"**
- **Solution**: Make sure the Wings WooCommerce Sync plugin is activated
- Check that all plugin files are uploaded correctly

**2. "Connection failed: Invalid credentials"**
- **Solution**: Verify API settings in Wings Sync admin panel
- Make sure you're using the test environment credentials

**3. "Customer validation failed"**
- **Solution**: Check that customer data has required fields (naziv, email)
- Verify email format is valid

**4. "Test page not loading"**
- **Solution**: Make sure you have admin privileges
- Check that the URL parameter is correct: `&run_wings_customer_tests=1`

### Debug Mode

Enable WordPress debug mode for detailed error information:

**wp-config.php:**
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
```

### Manual API Test

Test the API connection manually:

```php
// Test API connection
$api = Wings_API::get_instance();
$test = $api->test_connection();

if (is_wp_error($test)) {
    echo "Error: " . $test->get_error_message();
} else {
    echo "Success: " . print_r($test, true);
}
```

## Production Testing

### Before Going Live

1. **Test with Real Data**: Use your actual Wings Portal credentials
2. **Small Batch Test**: Start with a small number of customers
3. **Backup Database**: Always backup before running sync
4. **Monitor Logs**: Watch the sync logs for any issues

### Production Checklist

- [ ] API credentials configured for production
- [ ] Customer sync enabled in settings
- [ ] Sync interval set appropriately
- [ ] Batch size configured for your server
- [ ] Error notifications set up
- [ ] Backup strategy in place

## Next Steps

After successful testing:

1. **Configure Production Settings**: Update API credentials for live environment
2. **Enable Customer Sync**: Turn on customer synchronization
3. **Monitor Initial Sync**: Watch the first sync carefully
4. **Set Up Monitoring**: Configure alerts for sync failures
5. **Train Users**: Educate staff on the new customer sync functionality

## Support

If you encounter issues during testing:

1. Check the WordPress error log
2. Review the Wings Sync plugin logs
3. Verify API credentials and connectivity
4. Test with the Wings Portal test environment first
5. Contact support with specific error messages and test results
