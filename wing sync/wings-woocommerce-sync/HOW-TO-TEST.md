# 🧪 How to Test Wings Customer Sync Plugin

## Quick Start Testing Guide

Follow these simple steps to test the customer synchronization functionality.

## Step 1: Plugin Setup

### 1.1 Install and Activate
1. Upload the `wings-woocommerce-sync` folder to `/wp-content/plugins/`
2. Go to **Plugins** in WordPress admin
3. Activate **Wings WooCommerce Sync**

### 1.2 Configure API Settings
1. Go to **WooCommerce > Wings Sync**
2. Enter these test settings:
   ```
   API URL: https://portal.wings.rs/api/v1/
   API Alias: grosstest
   Username: aql
   Password: grossaql
   ```
3. Click **Save Settings**

## Step 2: Test API Connection

1. In the Wings Sync admin page, click **Test Connection**
2. You should see: ✅ **"Connection successful!"**
3. If you see an error, double-check the API settings

## Step 3: Run Customer Sync Tests

### Method A: URL Parameter Test (Easiest)

1. Go to your Wings Sync admin page
2. Add `&run_wings_customer_tests=1` to the end of the URL
3. Your URL should look like:
   ```
   /wp-admin/admin.php?page=wings-sync&run_wings_customer_tests=1
   ```
4. Press Enter and the tests will run automatically

### Method B: Create Test Page

1. Create a new file in your WordPress root: `test-wings.php`
2. Add this content:
   ```php
   <?php
   require_once('wp-config.php');
   
   if (!current_user_can('manage_options')) {
       die('Access denied');
   }
   
   include_once('wp-content/plugins/wings-woocommerce-sync/test-customer-sync.php');
   echo '<div style="font-family: monospace; white-space: pre-wrap;">';
   run_wings_customer_sync_tests();
   echo '</div>';
   ?>
   ```
3. Visit: `https://yoursite.com/test-wings.php`

### Method C: Functions.php Test

1. Add this to your theme's `functions.php`:
   ```php
   add_action('wp_footer', function() {
       if (isset($_GET['test_wings']) && current_user_can('manage_options')) {
           echo '<div style="position:fixed;top:0;left:0;width:100%;height:100%;background:white;z-index:9999;padding:20px;overflow:auto;">';
           include_once(ABSPATH . 'wp-content/plugins/wings-woocommerce-sync/test-customer-sync.php');
           run_wings_customer_sync_tests();
           echo '<br><a href="javascript:history.back()">← Back</a></div>';
       }
   });
   ```
2. Visit any page with `?test_wings=1` added to URL

## Step 4: Understanding Test Results

### ✅ Successful Test Output
```
Wings Customer Sync Test Suite
==============================

Testing Customer Data Transformation
✓ Wings to WooCommerce transformation working
✓ WooCommerce to Wings transformation working  
✓ Data validation passed
✓ Customer comparison logic working

Testing Wings Customer API
✓ API Connection successful!
✓ Retrieved 5 customers from Wings Portal
✓ Customer data structure correct

Testing Customer Creation
✓ Customer data validation passed
⚠ Customer creation test skipped (test mode)

Testing Complete Sync Workflow
✓ All components working correctly

Test Suite Complete
===================
All customer sync components tested successfully!
```

### ❌ Common Error Messages

**"Wings_API class not found"**
- Solution: Make sure the plugin is activated

**"Connection failed: Invalid credentials"**
- Solution: Check API settings in Wings Sync admin

**"Customer validation failed"**
- Solution: This is normal for test data validation

## Step 5: Test Real Customer Data

### 5.1 Test Customer Retrieval
```php
// Add this to a test file
$api = Wings_API::get_instance();
$customers = $api->get_customers(0, 5); // Get first 5 customers

if (!is_wp_error($customers)) {
    echo "Retrieved " . count($customers['customers']) . " customers\n";
    print_r($customers['customers'][0]); // Show first customer
} else {
    echo "Error: " . $customers->get_error_message();
}
```

### 5.2 Test Data Transformation
```php
// Test transforming Wings customer to WooCommerce format
$wings_customer = array(
    'naziv' => 'Test Company',
    'email' => '<EMAIL>',
    'adresa' => 'Test Street 123',
    'mesto' => 'Belgrade'
);

$wc_customer = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);
print_r($wc_customer);
```

## Step 6: Production Testing

### 6.1 Switch to Production API
1. Update API settings with your real Wings Portal credentials
2. Test connection with production data
3. Start with a small batch of customers

### 6.2 Enable Customer Sync
1. In Wings Sync settings, enable **Customer Sync**
2. Set sync interval (hourly recommended for testing)
3. Monitor the first sync carefully

### 6.3 Test WooCommerce Integration
1. Create a test customer in WooCommerce
2. Check if it appears in Wings Portal
3. Update customer data and verify sync

## Troubleshooting

### Debug Mode
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Check Logs
- WordPress debug log: `/wp-content/debug.log`
- Wings Sync logs: Available in admin panel

### Common Issues

1. **Memory Limit**: Increase PHP memory limit
2. **Timeout**: Increase max execution time
3. **Permissions**: Ensure proper file permissions

## Test Checklist

- [ ] Plugin installed and activated
- [ ] API settings configured
- [ ] Connection test successful
- [ ] Customer sync tests pass
- [ ] Data transformation working
- [ ] Customer validation working
- [ ] Real API data retrieval working
- [ ] Production settings configured
- [ ] Customer sync enabled
- [ ] First sync monitored

## Next Steps

After successful testing:

1. **Configure Production**: Update to real API credentials
2. **Enable Sync**: Turn on customer synchronization
3. **Monitor**: Watch initial syncs carefully
4. **Train Users**: Educate team on new functionality
5. **Backup**: Always backup before major syncs

## Support

If tests fail:
1. Check WordPress error logs
2. Verify API credentials
3. Test with Wings Portal test environment
4. Review plugin logs
5. Contact support with specific error messages

---

**Remember**: Always test in a staging environment before production use!
