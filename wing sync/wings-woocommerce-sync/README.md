# Wings WooCommerce Sync

A WordPress plugin that synchronizes product inventory and stock levels between Wings Portal ERP system and WooCommerce.

## 📋 Overview

Wings WooCommerce Sync provides real-time synchronization of product stock levels, prices, and inventory data between your Wings Portal ERP system and WooCommerce store. Keep your online store inventory accurate and up-to-date automatically.

## ✨ Features

### Core Functionality
- **Real-time Stock Sync**: Automatic inventory synchronization
- **Price Updates**: Sync product prices from Wings Portal
- **Scheduled Sync**: Configurable automatic synchronization intervals
- **Manual Sync**: On-demand synchronization via admin interface
- **Batch Processing**: Efficient handling of large product catalogs

### Advanced Features
- **Multi-warehouse Support**: Handle multiple Wings Portal warehouses
- **Stock Threshold Alerts**: Notifications for low stock levels
- **Sync Logging**: Detailed logs of all synchronization activities
- **Error Handling**: Robust error detection and recovery
- **Performance Optimization**: Efficient API calls and caching

### Admin Interface
- **Modern Dashboard**: Clean, responsive admin interface
- **Real-time Progress**: Live sync progress indicators
- **Connection Testing**: Verify Wings Portal API connectivity
- **Detailed Statistics**: Comprehensive sync reports and analytics
- **Log Management**: View, filter, and clear sync logs

## 🚀 Installation

1. **Upload** the plugin folder to `/wp-content/plugins/`
2. **Activate** the plugin through WordPress admin
3. **Configure** Wings Portal API credentials
4. **Map** product SKUs between systems
5. **Test** connection and run initial sync

## ⚙️ Configuration

### API Settings
- **API URL**: Wings Portal API endpoint
- **Username**: Your Wings Portal username
- **Password**: Your Wings Portal password
- **Warehouse**: Default warehouse for stock sync

### Sync Settings
- **Auto Sync**: Enable/disable automatic synchronization
- **Sync Interval**: How often to sync (in minutes)
- **Batch Size**: Number of products to process per batch
- **Stock Threshold**: Minimum stock level for alerts

### Product Mapping
- **SKU Matching**: Map WooCommerce SKUs to Wings Portal codes
- **Price Sync**: Enable/disable price synchronization
- **Stock Only**: Sync only stock levels (not prices)

## 📁 File Structure

```
wings-woocommerce-sync/
├── wings-woocommerce-sync.php       # Main plugin file
├── README.md                        # This file
├── includes/                        # Core plugin classes
│   ├── class-wings-admin.php        # Admin interface
│   ├── class-wings-api.php          # Wings Portal API
│   ├── class-wings-scheduler.php    # Sync scheduling
│   └── class-wings-sync.php         # Sync logic
├── admin/                           # Admin assets
│   ├── css/
│   │   └── admin.css               # Admin styles
│   ├── js/
│   │   └── admin.js                # Admin JavaScript
│   └── partials/
│       └── admin-display.php       # Admin templates
├── languages/                       # Translations
│   └── wings-sync.pot              # Translation template
└── docs/                           # Documentation
    ├── README.md                   # Detailed documentation
    └── INSTALLATION.md             # Installation guide
```

## 🧪 Testing

### Connection Test
1. Go to **Wings Sync** in WordPress admin
2. Click **Test Connection** to verify API connectivity
3. Check connection status and error messages

### Manual Sync
1. Click **Manual Sync** to test synchronization
2. Monitor progress in real-time
3. Review sync results and statistics
4. Check logs for detailed information

## 🔧 Requirements

- **WordPress**: 5.0 or higher
- **WooCommerce**: 4.0 or higher
- **PHP**: 7.4 or higher
- **Wings Portal**: API access with valid credentials
- **Memory**: 128MB minimum (256MB recommended)

## 📊 Sync Process

### Stock Synchronization
1. **Fetch** current stock levels from Wings Portal
2. **Compare** with WooCommerce inventory
3. **Update** changed stock levels
4. **Log** all changes and errors
5. **Notify** on low stock or errors

### Price Synchronization
1. **Retrieve** product prices from Wings Portal
2. **Map** to WooCommerce products by SKU
3. **Update** regular and sale prices
4. **Maintain** price history
5. **Log** price changes

## 🎛️ Admin Dashboard

### Main Dashboard
- **Connection Status**: Real-time API connectivity
- **Sync Statistics**: Products synced, errors, timing
- **Quick Actions**: Test, sync, clear logs
- **Recent Activity**: Latest sync operations

### Sync Logs
- **Filterable Logs**: By date, type, product
- **Export Logs**: Download for analysis
- **Auto-cleanup**: Configurable log retention
- **Real-time Updates**: Live log streaming

### Settings
- **API Configuration**: Credentials and endpoints
- **Sync Options**: Intervals, batches, thresholds
- **Product Mapping**: SKU relationships
- **Notifications**: Email alerts and webhooks

## 🆘 Support

### Documentation
- **Installation Guide**: `/docs/INSTALLATION.md`
- **Detailed Docs**: `/docs/README.md`

### Common Issues
- **API Connection**: Check credentials and network
- **SKU Mapping**: Verify product codes match
- **Performance**: Adjust batch sizes and intervals
- **Memory**: Increase PHP memory limit if needed

## 🔄 Sync Scheduling

### Automatic Sync
- **Cron Jobs**: WordPress cron-based scheduling
- **Intervals**: 5, 15, 30, 60 minutes or custom
- **Staggered**: Avoid peak traffic times
- **Retry Logic**: Automatic retry on failures

### Manual Sync
- **On-demand**: Immediate synchronization
- **Selective**: Sync specific products or categories
- **Bulk Operations**: Process large inventories
- **Progress Tracking**: Real-time status updates

## 📈 Performance

### Optimization
- **Caching**: API response caching
- **Batching**: Efficient bulk operations
- **Throttling**: Rate limiting for API calls
- **Memory Management**: Optimized for large catalogs

### Monitoring
- **Execution Time**: Track sync performance
- **Memory Usage**: Monitor resource consumption
- **API Calls**: Count and optimize requests
- **Error Rates**: Track and reduce failures

## 👨‍💻 Developer

**RadoDslav** - [Brandbusters.net](https://Brandbusters.net)

## 📄 License

GPL v2 or later - https://www.gnu.org/licenses/gpl-2.0.html

## 🔗 Related Plugins

- **Wings Customer Sync**: Synchronize customer data between Wings Portal and WooCommerce
