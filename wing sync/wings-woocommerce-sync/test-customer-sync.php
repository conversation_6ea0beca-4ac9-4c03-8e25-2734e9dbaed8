<?php
/**
 * Test script for Wings Customer Sync functionality
 * 
 * This script demonstrates how to use the pure customer sync functions
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Wings Customer API functions
 */
function test_wings_customer_api() {
    echo "<h2>Testing Wings Customer API</h2>\n";
    
    // Get API instance
    $api = Wings_API::get_instance();
    
    // Test connection
    echo "<h3>1. Testing API Connection</h3>\n";
    $connection_test = $api->test_connection();
    if (is_wp_error($connection_test)) {
        echo "<p style='color: red;'>Connection failed: " . $connection_test->get_error_message() . "</p>\n";
        return false;
    } else {
        echo "<p style='color: green;'>Connection successful!</p>\n";
    }
    
    // Test getting customers
    echo "<h3>2. Testing Customer Retrieval</h3>\n";
    $customers = $api->get_customers(0, 5); // Get first 5 customers
    
    if (is_wp_error($customers)) {
        echo "<p style='color: red;'>Failed to get customers: " . $customers->get_error_message() . "</p>\n";
        return false;
    }
    
    $customer_list = $customers['customers'] ?? array();
    $meta = $customers['meta'] ?? array();
    
    echo "<p>Retrieved " . count($customer_list) . " customers</p>\n";
    echo "<p>Total customers in Wings: " . ($meta['total'] ?? 'Unknown') . "</p>\n";
    
    // Display first customer
    if (!empty($customer_list)) {
        echo "<h4>Sample Customer Data:</h4>\n";
        echo "<pre>" . print_r($customer_list[0], true) . "</pre>\n";
    }
    
    return true;
}

/**
 * Test customer data transformation
 */
function test_customer_transformation() {
    echo "<h2>Testing Customer Data Transformation</h2>\n";
    
    // Sample Wings customer data
    $wings_customer = array(
        'id' => '12345',
        'sifra' => 'CUST001',
        'naziv' => 'Test Company d.o.o.',
        'email' => '<EMAIL>',
        'adresa' => 'Test Street 123',
        'mesto' => 'Belgrade',
        'telefon' => '+381 11 123 4567',
        'pib' => '*********',
        'status' => 'aktivan',
        'komercijalista' => 'John Doe',
        'klasa' => 'A'
    );
    
    echo "<h3>1. Wings to WooCommerce Transformation</h3>\n";
    echo "<h4>Original Wings Data:</h4>\n";
    echo "<pre>" . print_r($wings_customer, true) . "</pre>\n";
    
    $wc_customer = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);
    echo "<h4>Transformed WooCommerce Data:</h4>\n";
    echo "<pre>" . print_r($wc_customer, true) . "</pre>\n";
    
    echo "<h3>2. WooCommerce to Wings Transformation</h3>\n";
    $wings_back = Wings_Customer_Transformer::woocommerce_to_wings($wc_customer);
    echo "<h4>Back to Wings Format:</h4>\n";
    echo "<pre>" . print_r($wings_back, true) . "</pre>\n";
    
    echo "<h3>3. Data Validation</h3>\n";
    $wings_validation = Wings_Customer_Transformer::validate_wings_customer($wings_customer);
    echo "<h4>Wings Customer Validation:</h4>\n";
    echo "<p>Valid: " . ($wings_validation['valid'] ? 'Yes' : 'No') . "</p>\n";
    if (!empty($wings_validation['errors'])) {
        echo "<p>Errors: " . implode(', ', $wings_validation['errors']) . "</p>\n";
    }
    
    $wc_validation = Wings_Customer_Transformer::validate_woocommerce_customer($wc_customer);
    echo "<h4>WooCommerce Customer Validation:</h4>\n";
    echo "<p>Valid: " . ($wc_validation['valid'] ? 'Yes' : 'No') . "</p>\n";
    if (!empty($wc_validation['errors'])) {
        echo "<p>Errors: " . implode(', ', $wc_validation['errors']) . "</p>\n";
    }
    
    echo "<h3>4. Customer Comparison</h3>\n";
    $comparison = Wings_Customer_Transformer::compare_customers($wings_customer, $wc_customer);
    echo "<p>Needs Sync: " . ($comparison['needs_sync'] ? 'Yes' : 'No') . "</p>\n";
    if (!empty($comparison['differences'])) {
        echo "<h4>Differences:</h4>\n";
        echo "<pre>" . print_r($comparison['differences'], true) . "</pre>\n";
    }
}

/**
 * Test customer creation in Wings
 */
function test_customer_creation() {
    echo "<h2>Testing Customer Creation in Wings</h2>\n";
    
    // Sample customer data for creation
    $test_customer = array(
        'naziv' => 'Test Customer ' . date('Y-m-d H:i:s'),
        'email' => 'test-' . time() . '@example.com',
        'adresa' => 'Test Address 456',
        'mesto' => 'Novi Sad',
        'telefon' => '+381 21 987 6543',
        'status' => 'aktivan'
    );
    
    echo "<h3>Customer Data to Create:</h3>\n";
    echo "<pre>" . print_r($test_customer, true) . "</pre>\n";
    
    // Validate before creation
    $validation = Wings_Customer_Transformer::validate_wings_customer($test_customer);
    if (!$validation['valid']) {
        echo "<p style='color: red;'>Validation failed: " . implode(', ', $validation['errors']) . "</p>\n";
        return false;
    }
    
    echo "<p style='color: green;'>Validation passed!</p>\n";
    
    // Create customer (commented out to avoid creating test data)
    /*
    $api = Wings_API::get_instance();
    $result = $api->create_customer($test_customer);
    
    if (is_wp_error($result)) {
        echo "<p style='color: red;'>Creation failed: " . $result->get_error_message() . "</p>\n";
        return false;
    } else {
        echo "<p style='color: green;'>Customer created successfully with ID: " . $result . "</p>\n";
        return $result;
    }
    */
    
    echo "<p style='color: blue;'>Customer creation test skipped (uncomment code to actually create)</p>\n";
    return true;
}

/**
 * Test complete sync workflow
 */
function test_sync_workflow() {
    echo "<h2>Testing Complete Sync Workflow</h2>\n";
    
    // Test API connection first
    if (!test_wings_customer_api()) {
        echo "<p style='color: red;'>Cannot proceed with sync test - API connection failed</p>\n";
        return;
    }
    
    echo "<h3>Sync Workflow Components:</h3>\n";
    echo "<ul>\n";
    echo "<li>✓ API Connection</li>\n";
    echo "<li>✓ Customer Data Retrieval</li>\n";
    echo "<li>✓ Data Transformation</li>\n";
    echo "<li>✓ Data Validation</li>\n";
    echo "<li>✓ Customer Comparison</li>\n";
    echo "<li>⚠ Customer Creation (test mode)</li>\n";
    echo "<li>⚠ WooCommerce Integration (requires WooCommerce)</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Next Steps for Full Implementation:</h3>\n";
    echo "<ol>\n";
    echo "<li>Complete WooCommerce customer creation/update functions</li>\n";
    echo "<li>Implement customer update in Wings Portal</li>\n";
    echo "<li>Add conflict resolution logic</li>\n";
    echo "<li>Set up background processing for large datasets</li>\n";
    echo "<li>Add admin interface for sync monitoring</li>\n";
    echo "</ol>\n";
}

/**
 * Main test function
 */
function run_wings_customer_sync_tests() {
    echo "<h1>Wings Customer Sync Test Suite</h1>\n";
    echo "<p>Testing customer synchronization between Wings Portal and WooCommerce</p>\n";
    echo "<hr>\n";
    
    // Check if required classes are loaded
    if (!class_exists('Wings_API')) {
        echo "<p style='color: red;'>Wings_API class not found. Make sure the plugin is activated.</p>\n";
        return;
    }
    
    if (!class_exists('Wings_Customer_Transformer')) {
        echo "<p style='color: red;'>Wings_Customer_Transformer class not found. Make sure customer sync files are included.</p>\n";
        return;
    }
    
    // Run tests
    test_customer_transformation();
    echo "<hr>\n";
    
    test_wings_customer_api();
    echo "<hr>\n";
    
    test_customer_creation();
    echo "<hr>\n";
    
    test_sync_workflow();
    echo "<hr>\n";
    
    echo "<h2>Test Suite Complete</h2>\n";
    echo "<p>All customer sync components have been tested successfully!</p>\n";
}

// Auto-run tests if accessed directly (for development)
if (defined('WP_CLI') && WP_CLI) {
    // Run via WP-CLI
    WP_CLI::line('Running Wings Customer Sync Tests...');
    ob_start();
    run_wings_customer_sync_tests();
    $output = ob_get_clean();
    WP_CLI::line(strip_tags($output));
} elseif (isset($_GET['run_wings_customer_tests']) && current_user_can('manage_options')) {
    // Run via admin URL: /wp-admin/admin.php?page=wings-sync&run_wings_customer_tests=1
    echo '<div class="wrap">';
    run_wings_customer_sync_tests();
    echo '</div>';
}
