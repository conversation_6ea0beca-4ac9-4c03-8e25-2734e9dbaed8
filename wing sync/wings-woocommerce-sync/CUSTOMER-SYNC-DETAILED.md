# 🧑‍🤝‍🧑 Wings Customer Sync Documentation

## Overview

The Wings Customer Sync module provides bidirectional synchronization of customer data between Wings Portal ERP system and WooCommerce. This ensures customer information is consistent across both systems, enabling seamless business operations.

## Features

### ✅ Core Functionality
- **Bidirectional Synchronization** - Wings ↔ WooCommerce
- **Real-time Customer Creation** - Auto-sync new registrations
- **Scheduled Batch Sync** - Periodic customer updates
- **Data Transformation** - Pure functions for format conversion
- **Comprehensive Validation** - Data integrity checks
- **Conflict Resolution** - Handle data discrepancies

### ✅ Advanced Features
- **Enhanced Data Capture** - 13+ business fields
- **HPOS Compatibility** - Works with WooCommerce HPOS
- **Background Processing** - Non-blocking operations
- **Audit Trail** - Complete sync history
- **Test Suite** - Built-in testing framework

## Wings Portal API Endpoints

### Customer Reading Operations

**Get All Customers**: `local.kupac.svi`
- **Purpose**: Retrieve customer list with pagination
- **Parameters**:
  - `dStart`: Starting record (pagination)
  - `dLength`: Number of records to fetch
  - `komercijalista`: Filter by sales representative
  - `status`: Filter by customer status
  - `search`: Search term for filtering
  - `output`: Response format (jsonapi)

**Get Customer Details**: `local.kupac.info`
- **Purpose**: Retrieve detailed customer information
- **Parameters**:
  - `id`: Customer ID in Wings Portal
- **Returns**: Complete customer record

**Get Partner Info**: `partner.kupac.info`
- **Purpose**: Get logged-in partner information
- **Returns**: Current partner/customer details

### Customer Writing Operations

**Create Customer**: `local.kupac.nov`
- **Purpose**: Create new customer in Wings Portal
- **Method**: POST
- **Required Fields**:
  - `naziv`: Customer/company name
  - `email`: Email address
  - `adresa`: Street address
  - `mesto`: City
  - `telefon`: Phone number
  - `status`: Customer status (default: 'aktivan')

**Customer Interactions**: `partner.kontakt.nov`
- **Purpose**: Add customer interaction/contact record
- **Method**: POST

## Data Mapping

### Wings → WooCommerce Customer Fields

| Wings Field | WooCommerce Field | Type | Description |
|-------------|-------------------|------|-------------|
| `id` | `meta: wings_customer_id` | string | Wings customer ID |
| `sifra` | `meta: wings_customer_code` | string | Customer code |
| `naziv` | `display_name`, `billing_company` | string | Customer/company name |
| `email` | `user_email`, `billing_email` | email | Primary email address |
| `adresa` | `billing_address_1` | string | Street address |
| `mesto` | `billing_city` | string | City |
| `telefon` | `billing_phone` | string | Phone number |
| `pib` | `meta: wings_pib` | string | Tax identification number |
| `status` | `meta: wings_status` | string | Customer status |
| `komercijalista` | `meta: wings_sales_rep` | string | Sales representative |
| `klasa` | `meta: wings_customer_class` | string | Customer classification |

### WordPress Database Storage

**wp_users Table**
- `user_login`: Customer email (used as username)
- `user_email`: Customer email address
- `display_name`: Customer/company name
- `user_registered`: Registration timestamp
- `user_status`: Account status (0 = active)

**wp_usermeta Table**
- `first_name`, `last_name`: Customer name components
- `billing_*`: Complete billing address information
- `wings_*`: Wings Portal specific metadata

## Synchronization Workflows

### WooCommerce → Wings (Real-time)

**Customer Registration Hook**
```php
add_action('woocommerce_created_customer', array($this, 'on_customer_created'), 10, 3);
```

**Process Flow**:
1. Customer registers in WooCommerce
2. Hook triggers customer sync function
3. Transform WooCommerce data to Wings format
4. Validate customer data
5. Create customer in Wings Portal
6. Store Wings customer ID in WooCommerce

**Address Update Hook**
```php
add_action('woocommerce_customer_save_address', array($this, 'on_customer_address_updated'), 10, 2);
```

**Account Update Hook**
```php
add_action('woocommerce_save_account_details', array($this, 'on_customer_account_updated'), 10, 1);
```

### Wings → WooCommerce (Scheduled)

**Scheduled Sync Hook**
```php
add_action('wings_sync_customers_cron', array($this, 'sync_customers_from_wings'));
```

**Process Flow**:
1. Fetch customers from Wings Portal (paginated)
2. Transform Wings data to WooCommerce format
3. Check for existing customers by email
4. Create new customers or update existing ones
5. Store Wings metadata in WordPress
6. Log sync results

## Data Transformation Functions

### Pure Transformation Functions

**Wings to WooCommerce**
```php
public static function wings_to_woocommerce($wings_customer) {
    // Transform Wings customer data to WooCommerce format
    // Returns: WooCommerce customer data array
}
```

**WooCommerce to Wings**
```php
public static function woocommerce_to_wings($wc_customer) {
    // Transform WooCommerce customer data to Wings format
    // Returns: Wings customer data array
}
```

**Data Validation**
```php
public static function validate_wings_customer($wings_customer) {
    // Validate Wings customer data
    // Returns: ['valid' => bool, 'errors' => array]
}

public static function validate_woocommerce_customer($wc_customer) {
    // Validate WooCommerce customer data
    // Returns: ['valid' => bool, 'errors' => array]
}
```

**Customer Comparison**
```php
public static function compare_customers($wings_customer, $wc_customer) {
    // Compare customer records for sync decisions
    // Returns: ['needs_sync' => bool, 'differences' => array]
}
```

## Customer Creation Process

### WordPress User Creation
```php
// 1. Create WordPress user account
$user_data = array(
    'user_login' => $customer_data['user_email'],
    'user_email' => $customer_data['user_email'],
    'user_pass' => wp_generate_password(),
    'display_name' => $customer_data['display_name'],
    'role' => 'customer'
);
$user_id = wp_insert_user($user_data);
```

### WooCommerce Customer Setup
```php
// 2. Create WooCommerce customer object
$customer = new WC_Customer($user_id);

// Set customer properties
$customer->set_first_name($customer_data['first_name']);
$customer->set_billing_address_1($billing['address_1']);
$customer->save();
```

### Wings Metadata Storage
```php
// 3. Store Wings-specific metadata
update_user_meta($user_id, 'wings_customer_id', $wings_id);
update_user_meta($user_id, 'wings_customer_code', $customer_code);
update_user_meta($user_id, 'wings_last_sync', current_time('mysql'));
```

## Configuration Options

### Basic Settings
- `enable_customer_sync`: Enable/disable customer synchronization
- `customer_sync_interval`: Sync frequency (hourly, daily, weekly)
- `customer_batch_size`: Customers per batch (10-100)
- `sync_direction`: Bidirectional, Wings→WC, or WC→Wings

### Advanced Settings
- `auto_create_customers`: Auto-create missing customers
- `update_existing_customers`: Update existing customer data
- `conflict_resolution`: How to handle data conflicts
- `required_fields`: Specify required customer fields
- `field_mapping`: Custom field mapping configuration

## Testing Framework

### Built-in Test Suite

**Run Complete Tests**
```php
// URL parameter method
/wp-admin/admin.php?page=wings-sync&run_wings_customer_tests=1

// Direct function call
run_wings_customer_sync_tests();
```

**Individual Test Functions**
```php
test_customer_transformation();  // Test data transformation
test_wings_customer_api();      // Test API connectivity
test_customer_creation();       // Test customer creation
test_sync_workflow();          // Test complete workflow
```

### Test Coverage

**API Connection Testing**
- Wings Portal authentication
- Customer data retrieval
- API response validation

**Data Transformation Testing**
- Wings → WooCommerce conversion
- WooCommerce → Wings conversion
- Field mapping accuracy
- Data validation rules

**Customer Creation Testing**
- WordPress user creation
- WooCommerce customer setup
- Metadata storage
- Error handling

### Test Environment
- **Wings Portal Test URL**: `https://portal.wings.rs/grosstest`
- **Test Credentials**: Username: `aql`, Password: `grossaql`
- **Safe Testing**: No production data affected

## Monitoring and Logging

### Sync Statistics
- **Total Customers**: Number of customers in each system
- **Synced Customers**: Successfully synchronized customers
- **Last Sync**: Timestamp of last synchronization
- **Sync Direction**: Current sync configuration
- **Error Count**: Number of sync errors

### Log Categories
- **Customer Creation**: New customer records
- **Customer Updates**: Modified customer data
- **Sync Operations**: Batch sync activities
- **API Calls**: Wings Portal API interactions
- **Errors**: Failed operations and exceptions

### Sample Log Entries
```
[2024-01-15 14:30:15] INFO: Starting customer sync from Wings Portal
[2024-01-15 14:30:16] INFO: Retrieved 25 customers from Wings Portal
[2024-01-15 14:30:17] INFO: Created WooCommerce customer: <EMAIL>
[2024-01-15 14:30:18] INFO: Updated customer billing address: <EMAIL>
[2024-01-15 14:30:20] WARNING: Customer validation failed: missing phone number
[2024-01-15 14:30:22] INFO: Customer sync completed. 23/25 customers processed
```

## Error Handling

### Common Errors

**Customer Validation Errors**
- Missing required fields (name, email)
- Invalid email format
- Duplicate email addresses
- Missing billing information

**API Errors**
- Connection timeouts
- Authentication failures
- Invalid customer data format
- Wings Portal service unavailable

**WordPress Errors**
- User creation failures
- Permission issues
- Database connection problems
- Plugin conflicts

### Error Recovery
- **Retry Logic**: Automatic retry for temporary failures
- **Partial Sync**: Continue processing other customers
- **Error Queuing**: Queue failed operations for retry
- **Notification System**: Alert administrators of critical errors

## Performance Optimization

### Batch Processing
- **Small Sites** (< 500 customers): Batch size 50-100
- **Medium Sites** (500-2000 customers): Batch size 25-50
- **Large Sites** (> 2000 customers): Batch size 10-25

### Background Processing
- **Async Operations**: Non-blocking customer creation
- **Queue Management**: Process customers in background
- **Progress Tracking**: Monitor sync progress
- **Resource Management**: Prevent server overload

### Caching Strategy
- **Customer Data**: Cache frequently accessed data
- **API Responses**: Short-term caching for API calls
- **Metadata**: Cache Wings customer metadata

## Security Considerations

### Data Protection
- **Input Sanitization**: All customer data sanitized
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Output escaping
- **Data Encryption**: Sensitive data encryption

### Access Control
- **API Security**: Secure Wings Portal authentication
- **User Permissions**: WordPress capability checks
- **Audit Trail**: Log all customer data access
- **Privacy Compliance**: GDPR/privacy law compliance

## Troubleshooting

### Sync Issues
1. **Customers Not Syncing**: Check API credentials and connectivity
2. **Validation Failures**: Review required fields and data format
3. **Duplicate Customers**: Verify email uniqueness and conflict resolution
4. **Performance Issues**: Adjust batch size and timeout settings

### Data Issues
1. **Missing Customer Data**: Check field mapping configuration
2. **Incorrect Billing Info**: Verify address transformation logic
3. **Wings Metadata Missing**: Check metadata storage functions
4. **Sync Conflicts**: Review conflict resolution settings

### Debug Steps
1. Enable WordPress debug mode
2. Check Wings Portal API logs
3. Review customer sync test results
4. Verify WooCommerce customer data
5. Test with Wings Portal test environment

## Best Practices

### Implementation
- Start with Wings Portal test environment
- Test with small customer batches first
- Verify data mapping before full sync
- Set up monitoring and alerting

### Maintenance
- Regular sync monitoring
- Periodic data validation
- Log cleanup and rotation
- Performance optimization

### Security
- Regular credential updates
- Monitor for unauthorized access
- Implement data backup strategy
- Follow privacy regulations
