<?php
/**
 * Test Customer Creation in WooCommerce
 * 
 * This script demonstrates where and how customers are created in WordPress/WooCommerce
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test WooCommerce customer creation
 */
function test_woocommerce_customer_creation() {
    echo "<h2>WooCommerce Customer Creation Test</h2>\n";
    
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        echo "<p style='color: red;'>WooCommerce is not active. Customer creation requires WooCommerce.</p>\n";
        return false;
    }
    
    // Sample customer data from Wings Portal
    $wings_customer_data = array(
        'naziv' => 'Test Customer ' . date('Y-m-d H:i:s'),
        'email' => 'test-customer-' . time() . '@example.com',
        'adresa' => 'Test Street 123',
        'mesto' => 'Belgrade',
        'telefon' => '+381 11 123 4567',
        'pib' => '*********',
        'status' => 'aktivan',
        'komercijalista' => '<PERSON>',
        'klasa' => 'A'
    );
    
    echo "<h3>1. Original Wings Customer Data:</h3>\n";
    echo "<pre>" . print_r($wings_customer_data, true) . "</pre>\n";
    
    // Transform to WooCommerce format
    $wc_customer_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer_data);
    
    echo "<h3>2. Transformed WooCommerce Data:</h3>\n";
    echo "<pre>" . print_r($wc_customer_data, true) . "</pre>\n";
    
    // Test customer creation (commented out to avoid creating real customers)
    echo "<h3>3. Customer Creation Process:</h3>\n";
    echo "<p><strong>Where customers are created in WordPress:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><strong>WordPress Users Table:</strong> <code>wp_users</code></li>\n";
    echo "<li><strong>User Meta Table:</strong> <code>wp_usermeta</code></li>\n";
    echo "<li><strong>WooCommerce Customer Data:</strong> Stored as user meta</li>\n";
    echo "</ul>\n";
    
    echo "<h4>Database Tables Involved:</h4>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Table</th><th>Purpose</th><th>Key Fields</th></tr>\n";
    echo "<tr><td><code>wp_users</code></td><td>Basic user account</td><td>user_login, user_email, display_name</td></tr>\n";
    echo "<tr><td><code>wp_usermeta</code></td><td>Customer details</td><td>first_name, last_name, billing_*, wings_*</td></tr>\n";
    echo "</table>\n";
    
    // Show what would be created
    echo "<h4>What Would Be Created:</h4>\n";
    echo "<p><strong>WordPress User Record:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>User Login: " . esc_html($wc_customer_data['user_email']) . "</li>\n";
    echo "<li>Email: " . esc_html($wc_customer_data['user_email']) . "</li>\n";
    echo "<li>Display Name: " . esc_html($wc_customer_data['display_name']) . "</li>\n";
    echo "<li>Role: customer</li>\n";
    echo "</ul>\n";
    
    echo "<p><strong>Customer Meta Data:</strong></p>\n";
    echo "<ul>\n";
    foreach ($wc_customer_data['meta_data'] as $key => $value) {
        echo "<li>" . esc_html($key) . ": " . esc_html($value) . "</li>\n";
    }
    echo "</ul>\n";
    
    echo "<p><strong>Billing Information:</strong></p>\n";
    echo "<ul>\n";
    foreach ($wc_customer_data['billing'] as $key => $value) {
        echo "<li>billing_" . esc_html($key) . ": " . esc_html($value) . "</li>\n";
    }
    echo "</ul>\n";
    
    return true;
}

/**
 * Test actual customer creation (DISABLED by default)
 */
function test_actual_customer_creation() {
    echo "<h2>Actual Customer Creation Test</h2>\n";
    
    // This is disabled by default to prevent creating test customers
    $enable_actual_creation = false;
    
    if (!$enable_actual_creation) {
        echo "<p style='color: orange;'><strong>Actual customer creation is DISABLED by default.</strong></p>\n";
        echo "<p>To enable actual customer creation, set <code>\$enable_actual_creation = true</code> in this function.</p>\n";
        echo "<p><strong>Warning:</strong> This will create real customer records in your WordPress database!</p>\n";
        return;
    }
    
    // If enabled, this would create a real customer
    echo "<p style='color: red;'><strong>Creating actual customer...</strong></p>\n";
    
    try {
        $customer_sync = new Wings_Customer_Sync();
        
        // Sample customer data
        $test_customer = array(
            'user_email' => 'test-' . time() . '@example.com',
            'display_name' => 'Test Customer',
            'first_name' => 'Test',
            'last_name' => 'Customer',
            'billing' => array(
                'first_name' => 'Test',
                'last_name' => 'Customer',
                'company' => 'Test Company',
                'address_1' => 'Test Street 123',
                'city' => 'Belgrade',
                'phone' => '+381 11 123 4567',
                'email' => 'test-' . time() . '@example.com',
                'country' => 'RS'
            ),
            'meta_data' => array(
                'wings_customer_id' => '12345',
                'wings_customer_code' => 'TEST001',
                'wings_pib' => '*********'
            )
        );
        
        // This would call the private method (need to make it public for testing)
        // $customer_id = $customer_sync->create_woocommerce_customer($test_customer);
        
        echo "<p style='color: green;'>Customer creation would happen here.</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
    }
}

/**
 * Show existing customers
 */
function show_existing_customers() {
    echo "<h2>Existing WooCommerce Customers</h2>\n";
    
    // Get existing customers
    $customers = get_users(array(
        'role' => 'customer',
        'number' => 10,
        'orderby' => 'registered',
        'order' => 'DESC'
    ));
    
    if (empty($customers)) {
        echo "<p>No customers found in WooCommerce.</p>\n";
        return;
    }
    
    echo "<p>Found " . count($customers) . " customers (showing last 10):</p>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Registered</th><th>Wings Data</th></tr>\n";
    
    foreach ($customers as $customer) {
        $wings_id = get_user_meta($customer->ID, 'wings_customer_id', true);
        $wings_code = get_user_meta($customer->ID, 'wings_customer_code', true);
        $wings_data = $wings_id ? "ID: {$wings_id}" : 'No Wings data';
        if ($wings_code) {
            $wings_data .= ", Code: {$wings_code}";
        }
        
        echo "<tr>\n";
        echo "<td>" . $customer->ID . "</td>\n";
        echo "<td>" . esc_html($customer->user_email) . "</td>\n";
        echo "<td>" . esc_html($customer->display_name) . "</td>\n";
        echo "<td>" . $customer->user_registered . "</td>\n";
        echo "<td>" . esc_html($wings_data) . "</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
}

/**
 * Main test function
 */
function run_customer_creation_tests() {
    echo "<h1>Wings Customer Creation Test Suite</h1>\n";
    echo "<p>This test shows where and how customers are created in WooCommerce.</p>\n";
    echo "<hr>\n";
    
    // Check if required classes are loaded
    if (!class_exists('Wings_Customer_Transformer')) {
        echo "<p style='color: red;'>Wings_Customer_Transformer class not found. Make sure the plugin is activated.</p>\n";
        return;
    }
    
    // Run tests
    test_woocommerce_customer_creation();
    echo "<hr>\n";
    
    show_existing_customers();
    echo "<hr>\n";
    
    test_actual_customer_creation();
    echo "<hr>\n";
    
    echo "<h2>Summary</h2>\n";
    echo "<p><strong>Customer Creation Location:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>✅ <strong>WordPress Users:</strong> <code>wp_users</code> table</li>\n";
    echo "<li>✅ <strong>Customer Details:</strong> <code>wp_usermeta</code> table</li>\n";
    echo "<li>✅ <strong>Wings Data:</strong> Custom meta fields with 'wings_' prefix</li>\n";
    echo "<li>✅ <strong>WooCommerce Integration:</strong> Uses WC_Customer class</li>\n";
    echo "</ul>\n";
    
    echo "<p><strong>Implementation Status:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>✅ Customer creation function implemented</li>\n";
    echo "<li>✅ Customer update function implemented</li>\n";
    echo "<li>✅ Data transformation working</li>\n";
    echo "<li>⚠️ Actual creation disabled by default (safety)</li>\n";
    echo "</ul>\n";
}

// Auto-run tests if accessed directly
if (isset($_GET['run_customer_creation_tests']) && current_user_can('manage_options')) {
    echo '<div class="wrap">';
    run_customer_creation_tests();
    echo '</div>';
}
