<?php
require_once('wp-config.php');

function import_customers_from_json($json_file_path) {
    $json_data = file_get_contents($json_file_path);
    $data = json_decode($json_data, true);
    
    $customers = $data['customers'] ?? [];
    $imported = 0;
    $errors = 0;
    
    foreach ($customers as $wings_customer) {
        try {
            // Transform Wings data to WooCommerce format
            $wc_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);
            
            // Check if customer already exists
            $existing_user = get_user_by('email', $wc_data['user_email']);
            if ($existing_user) {
                continue; // Skip existing customers
            }
            
            // Create WordPress user
            $user_id = wp_insert_user([
                'user_login' => $wc_data['user_email'],
                'user_email' => $wc_data['user_email'],
                'user_pass' => wp_generate_password(),
                'display_name' => $wc_data['display_name'],
                'first_name' => $wc_data['first_name'],
                'last_name' => $wc_data['last_name'],
                'role' => 'customer'
            ]);
            
            if (is_wp_error($user_id)) {
                $errors++;
                continue;
            }
            
            // Create WooCommerce customer
            $customer = new WC_Customer($user_id);
            $customer->set_billing_company($wc_data['billing']['company']);
            $customer->set_billing_address_1($wc_data['billing']['address_1']);
            $customer->set_billing_city($wc_data['billing']['city']);
            $customer->set_billing_phone($wc_data['billing']['phone']);
            $customer->save();
            
            // Add Wings metadata
            foreach ($wc_data['meta_data'] as $key => $value) {
                update_user_meta($user_id, $key, $value);
            }
            
            $imported++;
            
        } catch (Exception $e) {
            $errors++;
            error_log("Customer import error: " . $e->getMessage());
        }
    }
    
    return ['imported' => $imported, 'errors' => $errors];
}

// Run import
$result = import_customers_from_json('exports/wings_customers_20250804_221110.json');
echo "Imported: {$result['imported']}, Errors: {$result['errors']}";
?>