# 🧪 Wings Customer Sync - Complete Testing Guide

## Quick Testing Steps

### 1. Setup Plugin
1. **Install**: Upload `wings-woocommerce-sync` to `/wp-content/plugins/`
2. **Activate**: Go to Plugins → Activate "Wings WooCommerce Sync"
3. **Configure**: Go to WooCommerce → Wings Sync

### 2. Configure Test Environment
Enter these settings in Wings Sync admin:
```
API URL: https://portal.wings.rs/api/v1/
API Alias: grosstest
Username: aql
Password: grossaql
```

### 3. Test Connection
Click **"Test Connection"** button - should show ✅ "Connection successful!"

### 4. Run Customer Sync Tests

#### Option A: Admin Panel Button (Easiest)
1. In Wings Sync admin, find **"Test sinhronizacije kupaca"** section
2. Click **"Pokreni test kupaca"** button
3. Wait for results to appear below

#### Option B: URL Parameter
1. Go to Wings Sync admin page
2. Add `&run_wings_customer_tests=1` to URL:
   ```
   /wp-admin/admin.php?page=wings-sync&run_wings_customer_tests=1
   ```
3. Press Enter - tests run automatically

#### Option C: New Tab Link
1. In Wings Sync admin, click **"Otvori test u novom tabu"**
2. Tests will run in new browser tab

## Expected Test Results

### ✅ Successful Output
```
Wings Customer Sync Test Suite
==============================

Testing Customer Data Transformation
✓ Wings to WooCommerce transformation working
✓ WooCommerce to Wings transformation working
✓ Data validation passed
✓ Customer comparison logic working

Testing Wings Customer API
✓ API Connection successful!
✓ Retrieved 5 customers from Wings Portal
✓ Customer data structure correct

Testing Customer Creation
✓ Customer data validation passed
⚠ Customer creation test skipped (test mode)

Testing Complete Sync Workflow
✓ All components working correctly

Test Suite Complete
===================
All customer sync components tested successfully!
```

### ❌ Common Errors & Solutions

**"Wings_API class not found"**
- **Fix**: Make sure plugin is activated

**"Connection failed: Invalid credentials"**
- **Fix**: Check API settings, use test credentials above

**"Customer validation failed"**
- **Fix**: This is normal for some test scenarios

## What Gets Tested

### 1. API Connection
- ✅ Connects to Wings Portal test environment
- ✅ Authenticates with test credentials
- ✅ Retrieves customer data

### 2. Data Transformation
- ✅ Wings → WooCommerce format conversion
- ✅ WooCommerce → Wings format conversion
- ✅ Field mapping accuracy
- ✅ Data validation

### 3. Customer Operations
- ✅ Customer data retrieval
- ✅ Customer creation validation
- ✅ Data comparison logic

### 4. Integration Points
- ✅ WooCommerce hooks
- ✅ Background processing
- ✅ Error handling

## Test Environment Details

**Wings Portal Test Server:**
- URL: `https://portal.wings.rs/grosstest`
- Username: `aql`
- Password: `grossaql`
- Safe for testing - no real data affected

## Advanced Testing

### Manual API Test
```php
// Test API directly
$api = Wings_API::get_instance();
$customers = $api->get_customers(0, 5);
print_r($customers);
```

### Test Data Transformation
```php
// Test transformation
$wings_data = array(
    'naziv' => 'Test Company',
    'email' => '<EMAIL>'
);
$wc_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_data);
print_r($wc_data);
```

## Production Preparation

### Before Going Live
1. **Change API Settings** to production credentials
2. **Test with Real Data** (small batch first)
3. **Backup Database** before major sync
4. **Enable Customer Sync** in settings
5. **Monitor First Sync** carefully

### Production Checklist
- [ ] Production API credentials configured
- [ ] Connection test successful with production
- [ ] Customer sync enabled
- [ ] Sync interval set appropriately
- [ ] Backup completed
- [ ] Monitoring in place

## Troubleshooting

### Debug Mode
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Check Logs
- WordPress: `/wp-content/debug.log`
- Wings Sync: Admin panel logs section

### Common Issues
1. **Memory Limit**: Increase PHP memory
2. **Timeout**: Increase execution time
3. **Permissions**: Check file permissions
4. **API Limits**: Respect rate limiting

## Files Created

The customer sync implementation includes:

### Core Files
- `class-wings-customer-transformer.php` - Data transformation
- `class-wings-customer-sync.php` - Main sync logic
- `test-customer-sync.php` - Test suite
- Extended `class-wings-api.php` - Customer API methods

### Documentation
- `CUSTOMER-SYNC-README.md` - Complete documentation
- `TESTING-GUIDE.md` - Detailed testing instructions
- `HOW-TO-TEST.md` - Quick testing guide
- `TESTING-SUMMARY.md` - This summary

### Admin Interface
- Updated admin panel with test buttons
- AJAX handlers for testing
- JavaScript for test execution

## Key Features Implemented

### ✅ Pure Functions
- No side effects
- Predictable outputs
- Easy to test

### ✅ Self-Contained
- Each function handles validation
- Comprehensive error handling
- Independent components

### ✅ Clean Code
- Single responsibility
- Clear naming
- Extensive documentation

### ✅ Stable Environment
- Built on existing plugin foundation
- Reuses authentication system
- Maintains HPOS compatibility

## Next Steps

After successful testing:

1. **Production Setup**: Configure real API credentials
2. **Enable Sync**: Turn on customer synchronization
3. **Monitor**: Watch initial syncs
4. **Train Team**: Educate users on new features
5. **Maintain**: Regular monitoring and updates

## Support

For issues during testing:
1. Check test results output
2. Review WordPress error logs
3. Verify API credentials
4. Test with Wings Portal test environment
5. Contact support with specific error messages

---

**Remember**: Always test thoroughly before production use!
