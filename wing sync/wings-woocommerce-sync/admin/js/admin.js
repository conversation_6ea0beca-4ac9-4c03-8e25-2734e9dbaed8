/**
 * Wings WooCommerce Sync - Admin JavaScript
 */

(function($) {
    'use strict';

    var WingsAdmin = {
        
        init: function() {
            this.bindEvents();
            this.initTooltips();
        },

        bindEvents: function() {
            // Original events
            $('#test-connection').on('click', this.testConnection);
            $('#manual-sync').on('click', this.manualSync);
            $('#clear-log').on('click', this.clearLog);
            $('#test-customer-sync').on('click', this.testCustomerSync);

            // New API Settings events
            $('#set-test-product-api').on('click', this.setTestProductAPI);
            $('#set-test-customer-api').on('click', this.setTestCustomerAPI);

            // New Test Commands events
            $('#test-product-connection').on('click', this.testProductConnection);
            $('#test-customer-connection').on('click', this.testCustomerConnection);
            $('#test-product-retrieval').on('click', this.testProductRetrieval);
            $('#test-customer-transformation').on('click', this.testCustomerTransformation);

            // New Product Sync events
            $('#sync-stock-only').on('click', this.syncStockOnly);
            $('#sync-prices-only').on('click', this.syncPricesOnly);
            $('#sync-category').on('click', this.syncByCategory);
            $('#sync-sku').on('click', this.syncBySKU);

            // New Customer Management events
            $('#retrieve-all-customers').on('click', this.retrieveAllCustomers);
            $('#retrieve-new-customers').on('click', this.retrieveNewCustomers);
            $('#sync-all-customers').on('click', this.syncAllCustomers);
            $('#send-unsynced-to-wings').on('click', this.sendUnsyncedToWings);
            $('#create-wp-customers').on('click', this.createWPCustomers);

            // Progress Report events
            $('#generate-sync-report').on('click', this.generateSyncReport);
            $('#export-activity-log').on('click', this.exportActivityLog);
            $('#refresh-activity').on('click', this.refreshActivity);

            // Auto-refresh log every 30 seconds during sync
            this.logRefreshInterval = null;
        },

        testConnection: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $status = $('#connection-status');
            
            // Show loading state
            $button.addClass('loading').prop('disabled', true);
            $status.removeClass('success error').text('');
            
            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wings_test_connection',
                    nonce: wings_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $status.addClass('success').html(
                            '<span class="wings-status-indicator success"></span>' + 
                            response.data.message
                        );
                        WingsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        $status.addClass('error').html(
                            '<span class="wings-status-indicator error"></span>' + 
                            response.data.message
                        );
                        WingsAdmin.showNotice(response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    var message = wings_ajax.strings.error + ' ' + error;
                    $status.addClass('error').html(
                        '<span class="wings-status-indicator error"></span>' + message
                    );
                    WingsAdmin.showNotice(message, 'error');
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false);
                }
            });
        },

        manualSync: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $progress = $('#sync-progress');
            var $results = $('#sync-results');
            var $status = $('#sync-status');
            
            // Show loading state
            $button.addClass('loading').prop('disabled', true);
            $progress.show();
            $results.hide();
            $status.text(wings_ajax.strings.syncing);
            
            // Start progress animation
            WingsAdmin.animateProgress();
            
            // Start log refresh
            WingsAdmin.startLogRefresh();
            
            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wings_manual_sync',
                    nonce: wings_ajax.nonce
                },
                timeout: 300000, // 5 minutes
                success: function(response) {
                    if (response.success) {
                        WingsAdmin.showSyncResults(response.data);
                        WingsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        WingsAdmin.showNotice(response.data.message, 'error');
                        $progress.hide();
                    }
                },
                error: function(xhr, status, error) {
                    var message = wings_ajax.strings.error + ' ' + error;
                    if (status === 'timeout') {
                        message = 'Sinhronizacija je prekinuta zbog timeout-a. Proverite log za više detalja.';
                    }
                    WingsAdmin.showNotice(message, 'error');
                    $progress.hide();
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false);
                    WingsAdmin.stopLogRefresh();
                    WingsAdmin.stopProgress();
                }
            });
        },

        clearLog: function(e) {
            e.preventDefault();
            
            if (!confirm(wings_ajax.strings.confirm_clear_log)) {
                return;
            }
            
            var $button = $(this);
            
            $button.addClass('loading').prop('disabled', true);
            
            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wings_clear_log',
                    nonce: wings_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('.wings-log-container').html('<p>Nema log unosa.</p>');
                        WingsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        WingsAdmin.showNotice(response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    WingsAdmin.showNotice(wings_ajax.strings.error + ' ' + error, 'error');
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false);
                }
            });
        },

        testCustomerSync: function(e) {
            e.preventDefault();

            var $button = $(this);
            var $results = $('#customer-test-results');
            var $output = $('#customer-test-output');

            // Show loading state
            $button.addClass('loading').prop('disabled', true).text('Testiranje...');
            $results.show();
            $output.text('Pokretanje testova...\n');

            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wings_test_customer_sync',
                    nonce: wings_ajax.nonce
                },
                timeout: 60000, // 60 seconds timeout
                success: function(response) {
                    if (response.success) {
                        $output.html(response.data.output);
                        WingsAdmin.showNotice('Test sinhronizacije kupaca završen uspešno!', 'success');
                    } else {
                        $output.text('Greška: ' + (response.data.message || 'Nepoznata greška'));
                        WingsAdmin.showNotice('Test neuspešan: ' + (response.data.message || 'Nepoznata greška'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    var errorMsg = 'Greška pri testiranju: ';
                    if (status === 'timeout') {
                        errorMsg += 'Test je prekoračio vremensko ograničenje (60s)';
                    } else {
                        errorMsg += error || 'Nepoznata greška';
                    }
                    $output.text(errorMsg);
                    WingsAdmin.showNotice(errorMsg, 'error');
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false).text('Pokreni test kupaca');
                }
            });
        },

        animateProgress: function() {
            var $fill = $('.sync-progress-fill');
            var width = 0;
            
            this.progressInterval = setInterval(function() {
                width += Math.random() * 10;
                if (width > 90) width = 90; // Don't go to 100% until sync is done
                $fill.css('width', width + '%');
            }, 1000);
        },

        stopProgress: function() {
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                $('.sync-progress-fill').css('width', '100%');
                
                setTimeout(function() {
                    $('#sync-progress').hide();
                    $('.sync-progress-fill').css('width', '0%');
                }, 1000);
            }
        },

        showSyncResults: function(data) {
            var $results = $('#sync-results');
            var $stats = $('#sync-stats');
            
            var statsHtml = '';
            if (data.stats) {
                statsHtml = 
                    '<div class="sync-stat">' +
                        '<div class="sync-stat-number">' + data.stats.total_processed + '</div>' +
                        '<div class="sync-stat-label">Obrađeno</div>' +
                    '</div>' +
                    '<div class="sync-stat">' +
                        '<div class="sync-stat-number">' + data.stats.updated + '</div>' +
                        '<div class="sync-stat-label">Ažurirano</div>' +
                    '</div>' +
                    '<div class="sync-stat">' +
                        '<div class="sync-stat-number">' + data.stats.created + '</div>' +
                        '<div class="sync-stat-label">Kreirano</div>' +
                    '</div>' +
                    '<div class="sync-stat">' +
                        '<div class="sync-stat-number">' + data.stats.errors + '</div>' +
                        '<div class="sync-stat-label">Greške</div>' +
                    '</div>';
                    
                if (data.execution_time) {
                    statsHtml += 
                        '<div class="sync-stat">' +
                            '<div class="sync-stat-number">' + data.execution_time + 's</div>' +
                            '<div class="sync-stat-label">Vreme</div>' +
                        '</div>';
                }
            }
            
            $stats.html(statsHtml);
            $results.show();
        },

        startLogRefresh: function() {
            this.logRefreshInterval = setInterval(function() {
                // Refresh the page to get updated log
                // In a real implementation, you might want to use AJAX to refresh just the log section
                if ($('#sync-progress').is(':visible')) {
                    location.reload();
                }
            }, 10000); // Refresh every 10 seconds
        },

        stopLogRefresh: function() {
            if (this.logRefreshInterval) {
                clearInterval(this.logRefreshInterval);
                this.logRefreshInterval = null;
            }
        },

        showNotice: function(message, type) {
            var noticeClass = 'notice-' + type;
            var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible wings-notice">' +
                '<p>' + message + '</p>' +
                '<button type="button" class="notice-dismiss">' +
                    '<span class="screen-reader-text">Dismiss this notice.</span>' +
                '</button>' +
            '</div>');
            
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
            
            // Handle manual dismiss
            $notice.on('click', '.notice-dismiss', function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            });
        },

        // API Settings Methods
        setTestProductAPI: function(e) {
            e.preventDefault();
            WingsAdmin.setTestAPIPreset('product');
        },

        setTestCustomerAPI: function(e) {
            e.preventDefault();
            WingsAdmin.setTestAPIPreset('customer');
        },

        setTestAPIPreset: function(presetType) {
            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wings_set_test_api_preset',
                    preset_type: presetType,
                    nonce: wings_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WingsAdmin.showNotice(response.data.message, 'success');
                        // Refresh the page to show updated settings
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        WingsAdmin.showNotice(response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    WingsAdmin.showNotice('Greška: ' + error, 'error');
                }
            });
        },

        // Test Commands Methods
        testProductConnection: function(e) {
            e.preventDefault();
            WingsAdmin.runTest($(this), 'wings_test_product_connection', '#product-test-results', '#product-test-output');
        },

        testCustomerConnection: function(e) {
            e.preventDefault();
            WingsAdmin.runTest($(this), 'wings_test_customer_connection', '#customer-test-results', '#customer-test-output');
        },

        testProductRetrieval: function(e) {
            e.preventDefault();
            WingsAdmin.runTest($(this), 'wings_test_product_retrieval', '#product-test-results', '#product-test-output');
        },

        testCustomerTransformation: function(e) {
            e.preventDefault();
            WingsAdmin.runTest($(this), 'wings_test_customer_transformation', '#customer-test-results', '#customer-test-output');
        },

        runTest: function($button, action, resultsSelector, outputSelector) {
            var originalText = $button.text();
            $button.addClass('loading').prop('disabled', true).text('Testiranje...');
            $(resultsSelector).show();
            $(outputSelector).text('Pokretanje testa...\n');

            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: action,
                    nonce: wings_ajax.nonce
                },
                timeout: 60000,
                success: function(response) {
                    if (response.success) {
                        $(outputSelector).text(response.data.message + '\n' + (response.data.details || ''));
                        WingsAdmin.showNotice('Test uspešan!', 'success');
                    } else {
                        $(outputSelector).text('Greška: ' + response.data.message);
                        WingsAdmin.showNotice('Test neuspešan: ' + response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    var errorMsg = 'Greška pri testiranju: ' + (status === 'timeout' ? 'Timeout' : error);
                    $(outputSelector).text(errorMsg);
                    WingsAdmin.showNotice(errorMsg, 'error');
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false).text(originalText);
                }
            });
        },

        // Product Sync Methods
        syncStockOnly: function(e) {
            e.preventDefault();
            WingsAdmin.runSync($(this), 'wings_sync_stock_only', 'Sinhronizacija zaliha u toku...');
        },

        syncPricesOnly: function(e) {
            e.preventDefault();
            WingsAdmin.runSync($(this), 'wings_sync_prices_only', 'Sinhronizacija cena u toku...');
        },

        syncByCategory: function(e) {
            e.preventDefault();
            var categoryId = $('#sync-by-category').val();
            if (!categoryId) {
                WingsAdmin.showNotice('Molimo izaberite kategoriju.', 'warning');
                return;
            }
            WingsAdmin.runSync($(this), 'wings_sync_by_category', 'Sinhronizacija kategorije u toku...', {category_id: categoryId});
        },

        syncBySKU: function(e) {
            e.preventDefault();
            var skus = $('#sync-by-sku').val();
            if (!skus) {
                WingsAdmin.showNotice('Molimo unesite SKU.', 'warning');
                return;
            }
            WingsAdmin.runSync($(this), 'wings_sync_by_sku', 'Sinhronizacija SKU u toku...', {skus: skus});
        },

        runSync: function($button, action, statusMessage, extraData) {
            var originalText = $button.text();
            $button.addClass('loading').prop('disabled', true);

            var $progress = $('#sync-progress, #sync-customer-progress');
            var $results = $('#sync-results, #sync-customer-results');
            var $status = $('#sync-status, #sync-customer-status');

            $progress.show();
            $results.hide();
            $status.text(statusMessage);

            WingsAdmin.animateProgress();

            var data = {
                action: action,
                nonce: wings_ajax.nonce
            };

            if (extraData) {
                $.extend(data, extraData);
            }

            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: data,
                timeout: 300000,
                success: function(response) {
                    if (response.success) {
                        WingsAdmin.showSyncResults(response.data);
                        WingsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        WingsAdmin.showNotice(response.data.message, 'error');
                        $progress.hide();
                    }
                },
                error: function(xhr, status, error) {
                    var message = 'Greška: ' + (status === 'timeout' ? 'Timeout' : error);
                    WingsAdmin.showNotice(message, 'error');
                    $progress.hide();
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false).text(originalText);
                    WingsAdmin.stopProgress();
                }
            });
        },

        // Customer Management Methods
        retrieveAllCustomers: function(e) {
            e.preventDefault();
            var batchSize = $('#customer-batch-size').val() || 50;
            var dateFrom = $('#customer-date-from').val();
            var dateTo = $('#customer-date-to').val();

            WingsAdmin.runCustomerAction($(this), 'wings_retrieve_customers', 'Preuzimanje kupaca u toku...', {
                retrieve_type: 'all',
                batch_size: batchSize,
                date_from: dateFrom,
                date_to: dateTo
            });
        },

        retrieveNewCustomers: function(e) {
            e.preventDefault();
            var batchSize = $('#customer-batch-size').val() || 50;

            WingsAdmin.runCustomerAction($(this), 'wings_retrieve_customers', 'Preuzimanje novih kupaca u toku...', {
                retrieve_type: 'new',
                batch_size: batchSize
            });
        },

        syncAllCustomers: function(e) {
            e.preventDefault();
            WingsAdmin.runCustomerAction($(this), 'wings_sync_customers', 'Sinhronizacija kupaca u toku...', {
                sync_type: 'all'
            });
        },

        sendUnsyncedToWings: function(e) {
            e.preventDefault();
            var includeGuest = $('#include-guest-customers').is(':checked');
            var includeRegistered = $('#include-registered-only').is(':checked');
            var validateBeforeSend = $('#validate-before-send').is(':checked');

            WingsAdmin.runCustomerAction($(this), 'wings_send_unsynced_to_wings', 'Slanje nesinhronizovanih kupaca u toku...', {
                include_guest: includeGuest,
                include_registered: includeRegistered,
                validate_before_send: validateBeforeSend
            });
        },

        createWPCustomers: function(e) {
            e.preventDefault();
            var sendWelcomeEmail = $('#send-welcome-email').is(':checked');
            var generateRandomPassword = $('#generate-random-password').is(':checked');
            var setCustomerRole = $('#set-customer-role').is(':checked');

            WingsAdmin.runCustomerAction($(this), 'wings_create_wp_customers', 'Kreiranje WP kupaca u toku...', {
                send_welcome_email: sendWelcomeEmail,
                generate_random_password: generateRandomPassword,
                set_customer_role: setCustomerRole
            });
        },

        runCustomerAction: function($button, action, statusMessage, extraData) {
            var originalText = $button.text();
            $button.addClass('loading').prop('disabled', true);

            var $progress = $('#retrieve-progress, #sync-customer-progress, #wp-to-wings-results, #wings-to-wp-results').filter(':visible').first();
            if ($progress.length === 0) {
                $progress = $('#retrieve-progress');
            }

            $progress.show();
            $progress.find('.sync-progress-fill').css('width', '0%');

            var data = {
                action: action,
                nonce: wings_ajax.nonce
            };

            if (extraData) {
                $.extend(data, extraData);
            }

            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: data,
                timeout: 300000,
                success: function(response) {
                    if (response.success) {
                        WingsAdmin.showCustomerResults(response.data, $progress);
                        WingsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        WingsAdmin.showNotice(response.data.message, 'error');
                        $progress.hide();
                    }
                },
                error: function(xhr, status, error) {
                    var message = 'Greška: ' + (status === 'timeout' ? 'Timeout' : error);
                    WingsAdmin.showNotice(message, 'error');
                    $progress.hide();
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false).text(originalText);
                    $progress.find('.sync-progress-fill').css('width', '100%');
                }
            });
        },

        showCustomerResults: function(data, $progress) {
            var $results = $progress.siblings().filter('[id$="-results"]').first();
            if ($results.length === 0) {
                $results = $('#retrieve-results');
            }

            var statsHtml = '';
            if (data.stats) {
                statsHtml = '<div class="customer-stats">';
                Object.keys(data.stats).forEach(function(key) {
                    statsHtml += '<div class="stat-item"><span class="stat-label">' + key + ':</span> <span class="stat-value">' + data.stats[key] + '</span></div>';
                });
                statsHtml += '</div>';
            }

            $results.find('[id$="-stats"]').html(statsHtml);
            $results.show();
        },

        // Progress Report Methods
        generateSyncReport: function(e) {
            e.preventDefault();
            WingsAdmin.generateReport('sync');
        },

        exportActivityLog: function(e) {
            e.preventDefault();
            WingsAdmin.generateReport('activity');
        },

        generateReport: function(reportType) {
            $.ajax({
                url: wings_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wings_generate_progress_report',
                    report_type: reportType,
                    nonce: wings_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WingsAdmin.showNotice('Izveštaj uspešno generisan!', 'success');
                        if (response.data.download_url) {
                            window.open(response.data.download_url, '_blank');
                        }
                    } else {
                        WingsAdmin.showNotice(response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    WingsAdmin.showNotice('Greška pri generisanju izveštaja: ' + error, 'error');
                }
            });
        },

        refreshActivity: function(e) {
            e.preventDefault();
            location.reload();
        },

        initTooltips: function() {
            // Simple tooltip implementation
            $('[data-tooltip]').hover(
                function() {
                    $(this).addClass('wings-tooltip');
                },
                function() {
                    $(this).removeClass('wings-tooltip');
                }
            );
        },

        // Utility function to format numbers
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        // Utility function to format time
        formatTime: function(seconds) {
            if (seconds < 60) {
                return seconds + 's';
            } else if (seconds < 3600) {
                return Math.floor(seconds / 60) + 'm ' + (seconds % 60) + 's';
            } else {
                var hours = Math.floor(seconds / 3600);
                var minutes = Math.floor((seconds % 3600) / 60);
                return hours + 'h ' + minutes + 'm';
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        WingsAdmin.init();
    });

    // Make WingsAdmin globally available for debugging
    window.WingsAdmin = WingsAdmin;

})(jQuery);
