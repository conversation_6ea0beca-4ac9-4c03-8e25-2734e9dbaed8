<?php
/**
 * Test Commands Tab
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wings-main-content">
    <!-- Product API Tests -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Test API za proizvode', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Testirajte konekciju i funkcionalnost API-ja za proizvode.', 'wings-sync'); ?></p>
            
            <div class="wings-test-buttons">
                <button type="button" id="test-product-connection" class="button button-primary">
                    <?php _e('Test konekcije za proizvode', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="test-product-retrieval" class="button button-secondary">
                    <?php _e('Test preuzimanja proizvoda', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="test-product-stock" class="button button-secondary">
                    <?php _e('Test stanja zaliha', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="product-test-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati testa:', 'wings-sync'); ?></h4>
                <div id="product-test-output" class="wings-test-output"></div>
            </div>
        </div>
    </div>

    <!-- Customer API Tests -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Test API za kupce', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Testirajte funkcionalnost sinhronizacije kupaca između Wings Portal-a i WooCommerce-a.', 'wings-sync'); ?></p>

            <div class="wings-test-buttons">
                <button type="button" id="test-customer-connection" class="button button-primary">
                    <?php _e('Test konekcije za kupce', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="test-customer-sync" class="button button-secondary">
                    <?php _e('Pokreni test kupaca', 'wings-sync'); ?>
                </button>

                <button type="button" id="test-customer-transformation" class="button button-secondary">
                    <?php _e('Test transformacije podataka', 'wings-sync'); ?>
                </button>

                <a href="<?php echo admin_url('admin.php?page=wings-sync&tab=test-commands&run_wings_customer_tests=1'); ?>"
                   class="button button-secondary" target="_blank">
                    <?php _e('Otvori test u novom tabu', 'wings-sync'); ?>
                </a>

                <a href="<?php echo plugins_url('test-customer-creation-debug.php', dirname(dirname(__FILE__))); ?>"
                   class="button button-secondary" target="_blank" style="background-color: #ff6b35; border-color: #ff6b35; color: white;">
                    <?php _e('🔍 Debug test kreiranja kupaca', 'wings-sync'); ?>
                </a>
            </div>

            <div id="customer-test-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati testa:', 'wings-sync'); ?></h4>
                <div id="customer-test-output" class="wings-test-output"></div>
            </div>
        </div>
    </div>

    <!-- API Diagnostics -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('API Dijagnostika', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Detaljne informacije o API konekciji i konfiguraciji.', 'wings-sync'); ?></p>
            
            <div class="wings-test-buttons">
                <button type="button" id="run-api-diagnostics" class="button button-secondary">
                    <?php _e('Pokreni dijagnostiku', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="test-api-endpoints" class="button button-secondary">
                    <?php _e('Test svih endpoint-ova', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="diagnostics-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati dijagnostike:', 'wings-sync'); ?></h4>
                <div id="diagnostics-output" class="wings-test-output"></div>
            </div>
        </div>
    </div>

    <!-- Performance Tests -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Test performansi', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Testirajte performanse API poziva i sinhronizacije.', 'wings-sync'); ?></p>
            
            <div class="wings-test-buttons">
                <button type="button" id="test-api-speed" class="button button-secondary">
                    <?php _e('Test brzine API-ja', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="test-batch-processing" class="button button-secondary">
                    <?php _e('Test batch obrade', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="test-memory-usage" class="button button-secondary">
                    <?php _e('Test korišćenja memorije', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="performance-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati performansi:', 'wings-sync'); ?></h4>
                <div id="performance-output" class="wings-test-output"></div>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar -->
<div class="wings-sidebar">
    <!-- Test Environment Info -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Test okruženje', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><strong><?php _e('Wings Portal Test:', 'wings-sync'); ?></strong></p>
            <ul style="margin-left: 20px;">
                <li>URL: <code>https://portal.wings.rs/grosstest</code></li>
                <li>Username: <code>aql</code></li>
                <li>Password: <code>grossaql</code></li>
            </ul>
            <p class="description">
                <?php _e('Test koristi Wings Portal test okruženje sa test podacima. Bezbedno je za testiranje.', 'wings-sync'); ?>
            </p>
            
            <hr>
            
            <p><strong><?php _e('Trenutna konfiguracija:', 'wings-sync'); ?></strong></p>
            <ul style="margin-left: 20px;">
                <li>API URL: <code><?php echo esc_html($settings['api_url'] ?? 'Nije podešeno'); ?></code></li>
                <li>Alias: <code><?php echo esc_html($settings['api_alias'] ?? 'Nije podešeno'); ?></code></li>
                <li>Username: <code><?php echo esc_html($settings['api_username'] ?? 'Nije podešeno'); ?></code></li>
                <li>Status: 
                    <?php if (!empty($settings['api_alias']) && !empty($settings['api_username']) && !empty($settings['api_password'])): ?>
                        <span style="color: green;">✓ Konfigurisano</span>
                    <?php else: ?>
                        <span style="color: red;">✗ Nije potpuno konfigurisano</span>
                    <?php endif; ?>
                </li>
            </ul>
        </div>
    </div>

    <!-- Test Guidelines -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Smernice za testiranje', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><strong><?php _e('Redosled testiranja:', 'wings-sync'); ?></strong></p>
            <ol>
                <li><?php _e('Test konekcije za proizvode', 'wings-sync'); ?></li>
                <li><?php _e('Test konekcije za kupce', 'wings-sync'); ?></li>
                <li><?php _e('Test preuzimanja podataka', 'wings-sync'); ?></li>
                <li><?php _e('Test transformacije podataka', 'wings-sync'); ?></li>
                <li><?php _e('Test performansi', 'wings-sync'); ?></li>
            </ol>
            
            <p><strong><?php _e('Napomene:', 'wings-sync'); ?></strong></p>
            <ul>
                <li><?php _e('Testovi koriste test okruženje', 'wings-sync'); ?></li>
                <li><?php _e('Nema uticaja na produkciju', 'wings-sync'); ?></li>
                <li><?php _e('Rezultati se prikazuju u realnom vremenu', 'wings-sync'); ?></li>
            </ul>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Brze akcije', 'wings-sync'); ?></h2>
        <div class="inside">
            <button type="button" id="run-all-tests" class="button button-primary" style="width: 100%; margin-bottom: 10px;">
                <?php _e('Pokreni sve testove', 'wings-sync'); ?>
            </button>
            
            <button type="button" id="clear-test-results" class="button button-secondary" style="width: 100%; margin-bottom: 10px;">
                <?php _e('Obriši rezultate', 'wings-sync'); ?>
            </button>
            
            <button type="button" id="export-test-results" class="button button-secondary" style="width: 100%;">
                <?php _e('Izvezi rezultate', 'wings-sync'); ?>
            </button>
        </div>
    </div>
</div>

<?php
// Handle direct test execution
if (isset($_GET['run_wings_customer_tests']) && current_user_can('manage_options')) {
    echo '<div class="wrap" style="margin-top: 20px;">';
    echo '<h2>' . __('Wings Customer Sync Test Results', 'wings-sync') . '</h2>';
    
    // Include and run the test file
    if (file_exists(WINGS_SYNC_PLUGIN_DIR . 'test-customer-sync.php')) {
        include_once WINGS_SYNC_PLUGIN_DIR . 'test-customer-sync.php';
        run_wings_customer_sync_tests();
    } else {
        echo '<p style="color: red;">' . __('Test file not found.', 'wings-sync') . '</p>';
    }
    
    echo '</div>';
}
?>

<style>
.wings-test-output {
    background: #f9f9f9;
    padding: 10px;
    border: 1px solid #ddd;
    max-height: 400px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 12px;
}

.wings-test-buttons {
    margin-bottom: 15px;
}

.wings-test-buttons .button {
    margin-right: 10px;
    margin-bottom: 5px;
}
</style>
