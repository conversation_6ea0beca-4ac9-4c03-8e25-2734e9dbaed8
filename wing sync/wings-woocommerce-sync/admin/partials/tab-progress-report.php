<?php
/**
 * Progress Report Tab
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get progress data
$sync_stats = Wings_Sync::get_instance()->get_sync_stats();
$log_entries = Wings_WooCommerce_Sync::get_log();
?>

<div class="wings-main-content">
    <!-- Visual Progress Overview -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Vizuelni pregled napretka', 'wings-sync'); ?></h2>
        <div class="inside">
            <div class="wings-progress-dashboard">
                <!-- Product Sync Progress -->
                <div class="progress-card">
                    <h3><?php _e('Sinhronizacija proizvoda', 'wings-sync'); ?></h3>
                    <div class="progress-circle" data-progress="<?php echo $sync_stats['total_products'] > 0 ? round(($sync_stats['synced_products'] / $sync_stats['total_products']) * 100) : 0; ?>">
                        <div class="progress-circle-inner">
                            <span class="progress-percentage"><?php echo $sync_stats['total_products'] > 0 ? round(($sync_stats['synced_products'] / $sync_stats['total_products']) * 100) : 0; ?>%</span>
                        </div>
                    </div>
                    <div class="progress-details">
                        <p><?php echo esc_html($sync_stats['synced_products']); ?> / <?php echo esc_html($sync_stats['total_products']); ?> <?php _e('proizvoda', 'wings-sync'); ?></p>
                    </div>
                </div>

                <!-- Customer Sync Progress -->
                <div class="progress-card">
                    <h3><?php _e('Sinhronizacija kupaca', 'wings-sync'); ?></h3>
                    <?php
                    $total_customers = get_option('wings_customers_count', 0);
                    $synced_customers = get_option('wings_synced_customers_count', 0);
                    $customer_progress = $total_customers > 0 ? round(($synced_customers / $total_customers) * 100) : 0;
                    ?>
                    <div class="progress-circle" data-progress="<?php echo $customer_progress; ?>">
                        <div class="progress-circle-inner">
                            <span class="progress-percentage"><?php echo $customer_progress; ?>%</span>
                        </div>
                    </div>
                    <div class="progress-details">
                        <p><?php echo esc_html($synced_customers); ?> / <?php echo esc_html($total_customers); ?> <?php _e('kupaca', 'wings-sync'); ?></p>
                    </div>
                </div>

                <!-- API Health -->
                <div class="progress-card">
                    <h3><?php _e('Zdravlje API-ja', 'wings-sync'); ?></h3>
                    <?php
                    $api_health = get_transient('wings_api_health_status');
                    $health_percentage = $api_health === 'healthy' ? 100 : ($api_health === 'warning' ? 75 : 0);
                    ?>
                    <div class="progress-circle" data-progress="<?php echo $health_percentage; ?>">
                        <div class="progress-circle-inner">
                            <span class="progress-percentage"><?php echo $health_percentage; ?>%</span>
                        </div>
                    </div>
                    <div class="progress-details">
                        <p><?php 
                        if ($api_health === 'healthy') {
                            echo '<span style="color: green;">' . __('Zdravo', 'wings-sync') . '</span>';
                        } elseif ($api_health === 'warning') {
                            echo '<span style="color: orange;">' . __('Upozorenje', 'wings-sync') . '</span>';
                        } else {
                            echo '<span style="color: red;">' . __('Problem', 'wings-sync') . '</span>';
                        }
                        ?></p>
                    </div>
                </div>

                <!-- Overall Progress -->
                <div class="progress-card">
                    <h3><?php _e('Ukupan napredak', 'wings-sync'); ?></h3>
                    <?php
                    $overall_progress = round((($sync_stats['total_products'] > 0 ? ($sync_stats['synced_products'] / $sync_stats['total_products']) : 0) + 
                                             ($total_customers > 0 ? ($synced_customers / $total_customers) : 0) + 
                                             ($health_percentage / 100)) / 3 * 100);
                    ?>
                    <div class="progress-circle" data-progress="<?php echo $overall_progress; ?>">
                        <div class="progress-circle-inner">
                            <span class="progress-percentage"><?php echo $overall_progress; ?>%</span>
                        </div>
                    </div>
                    <div class="progress-details">
                        <p><?php _e('Sveukupno', 'wings-sync'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Progress Charts -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Detaljni grafici napretka', 'wings-sync'); ?></h2>
        <div class="inside">
            <div class="wings-charts-container">
                <!-- Sync Activity Chart -->
                <div class="chart-container">
                    <h4><?php _e('Aktivnost sinhronizacije (poslednih 7 dana)', 'wings-sync'); ?></h4>
                    <canvas id="syncActivityChart" width="400" height="200"></canvas>
                </div>

                <!-- Error Rate Chart -->
                <div class="chart-container">
                    <h4><?php _e('Stopa grešaka (poslednih 24 sata)', 'wings-sync'); ?></h4>
                    <canvas id="errorRateChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Activity Feed -->
    <div class="postbox">
        <h2 class="hndle">
            <?php _e('Aktivnost u realnom vremenu', 'wings-sync'); ?>
            <button type="button" id="refresh-activity" class="button button-secondary" style="float: right;">
                <?php _e('Osveži', 'wings-sync'); ?>
            </button>
        </h2>
        <div class="inside">
            <div id="real-time-activity" class="wings-activity-feed">
                <?php if (empty($log_entries)): ?>
                    <p><?php _e('Nema aktivnosti za prikaz.', 'wings-sync'); ?></p>
                <?php else: ?>
                    <?php foreach (array_reverse(array_slice($log_entries, -10)) as $entry): ?>
                        <div class="activity-item activity-<?php echo esc_attr($entry['type']); ?>">
                            <div class="activity-icon">
                                <?php
                                switch ($entry['type']) {
                                    case 'success':
                                        echo '✓';
                                        break;
                                    case 'error':
                                        echo '✗';
                                        break;
                                    case 'warning':
                                        echo '⚠';
                                        break;
                                    default:
                                        echo 'ℹ';
                                }
                                ?>
                            </div>
                            <div class="activity-content">
                                <div class="activity-message"><?php echo esc_html($entry['message']); ?></div>
                                <div class="activity-time"><?php echo esc_html($entry['timestamp']); ?></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Metrije performansi', 'wings-sync'); ?></h2>
        <div class="inside">
            <div class="wings-metrics-grid">
                <div class="metric-card">
                    <h4><?php _e('Prosečno vreme sinhronizacije', 'wings-sync'); ?></h4>
                    <div class="metric-value">
                        <?php 
                        $avg_sync_time = get_option('wings_avg_sync_time', 0);
                        echo $avg_sync_time ? round($avg_sync_time, 2) . 's' : __('N/A', 'wings-sync');
                        ?>
                    </div>
                </div>

                <div class="metric-card">
                    <h4><?php _e('API pozivi danas', 'wings-sync'); ?></h4>
                    <div class="metric-value">
                        <?php echo esc_html(get_option('wings_api_calls_today', 0)); ?>
                    </div>
                </div>

                <div class="metric-card">
                    <h4><?php _e('Uspešnost API poziva', 'wings-sync'); ?></h4>
                    <div class="metric-value">
                        <?php 
                        $success_rate = get_option('wings_api_success_rate', 0);
                        echo $success_rate ? round($success_rate, 1) . '%' : __('N/A', 'wings-sync');
                        ?>
                    </div>
                </div>

                <div class="metric-card">
                    <h4><?php _e('Poslednja greška', 'wings-sync'); ?></h4>
                    <div class="metric-value">
                        <?php 
                        $last_error = get_option('wings_last_error_time', 0);
                        echo $last_error ? human_time_diff($last_error) . ' ' . __('ago', 'wings-sync') : __('Nema', 'wings-sync');
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export and Reports -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Izveštaji i izvoz', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Generiši i izvezi detaljne izveštaje o sinhronizaciji.', 'wings-sync'); ?></p>
            
            <div class="wings-export-controls">
                <button type="button" id="generate-sync-report" class="button button-primary">
                    <?php _e('Generiši izveštaj o sinhronizaciji', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="export-activity-log" class="button button-secondary">
                    <?php _e('Izvezi log aktivnosti', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="export-error-report" class="button button-secondary">
                    <?php _e('Izvezi izveštaj o greškama', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="export-performance-metrics" class="button button-secondary">
                    <?php _e('Izvezi metrije performansi', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="export-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati izvoza:', 'wings-sync'); ?></h4>
                <div id="export-output" class="wings-export-output"></div>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar -->
<div class="wings-sidebar">
    <!-- Quick Stats -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Brze statistike', 'wings-sync'); ?></h2>
        <div class="inside">
            <div class="wings-quick-stats">
                <div class="stat-item">
                    <div class="stat-label"><?php _e('Ukupno sinhronizacija:', 'wings-sync'); ?></div>
                    <div class="stat-value"><?php echo esc_html(get_option('wings_total_syncs', 0)); ?></div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-label"><?php _e('Uspešne sinhronizacije:', 'wings-sync'); ?></div>
                    <div class="stat-value"><?php echo esc_html(get_option('wings_successful_syncs', 0)); ?></div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-label"><?php _e('Neuspešne sinhronizacije:', 'wings-sync'); ?></div>
                    <div class="stat-value"><?php echo esc_html(get_option('wings_failed_syncs', 0)); ?></div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-label"><?php _e('Vreme rada:', 'wings-sync'); ?></div>
                    <div class="stat-value">
                        <?php 
                        $uptime = time() - get_option('wings_plugin_activated_time', time());
                        echo human_time_diff(time() - $uptime);
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Zdravlje sistema', 'wings-sync'); ?></h2>
        <div class="inside">
            <div class="wings-health-indicators">
                <div class="health-item">
                    <span class="health-label"><?php _e('API konekcija:', 'wings-sync'); ?></span>
                    <span class="health-status health-<?php echo $api_health === 'healthy' ? 'good' : ($api_health === 'warning' ? 'warning' : 'bad'); ?>">
                        <?php echo $api_health === 'healthy' ? '●' : ($api_health === 'warning' ? '●' : '●'); ?>
                    </span>
                </div>
                
                <div class="health-item">
                    <span class="health-label"><?php _e('Cron zadaci:', 'wings-sync'); ?></span>
                    <span class="health-status health-<?php echo wp_next_scheduled('wings_sync_cron_hook') ? 'good' : 'bad'; ?>">
                        <?php echo wp_next_scheduled('wings_sync_cron_hook') ? '●' : '●'; ?>
                    </span>
                </div>
                
                <div class="health-item">
                    <span class="health-label"><?php _e('Memorija:', 'wings-sync'); ?></span>
                    <span class="health-status health-good">●</span>
                </div>
                
                <div class="health-item">
                    <span class="health-label"><?php _e('Disk prostor:', 'wings-sync'); ?></span>
                    <span class="health-status health-good">●</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Alerts -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Nedavna upozorenja', 'wings-sync'); ?></h2>
        <div class="inside">
            <?php 
            $alerts = array_filter($log_entries, function($entry) {
                return $entry['type'] === 'error' || $entry['type'] === 'warning';
            });
            $recent_alerts = array_slice($alerts, -3);
            
            if (empty($recent_alerts)): 
            ?>
                <p style="color: green;"><?php _e('Nema nedavnih upozorenja.', 'wings-sync'); ?></p>
            <?php else: ?>
                <div class="wings-alerts">
                    <?php foreach (array_reverse($recent_alerts) as $alert): ?>
                        <div class="alert-item alert-<?php echo esc_attr($alert['type']); ?>">
                            <div class="alert-message"><?php echo esc_html($alert['message']); ?></div>
                            <div class="alert-time"><?php echo esc_html(date('H:i', strtotime($alert['timestamp']))); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Progress Dashboard Styles */
.wings-progress-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.progress-card {
    text-align: center;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.progress-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: conic-gradient(#0073aa 0deg, #0073aa calc(var(--progress) * 3.6deg), #f0f0f0 calc(var(--progress) * 3.6deg));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px auto;
    position: relative;
}

.progress-circle-inner {
    width: 70px;
    height: 70px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-percentage {
    font-size: 18px;
    font-weight: bold;
    color: #0073aa;
}

/* Charts Container */
.wings-charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.chart-container {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* Activity Feed */
.wings-activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.activity-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 12px;
    color: white;
}

.activity-item.activity-success .activity-icon {
    background-color: #46b450;
}

.activity-item.activity-error .activity-icon {
    background-color: #dc3232;
}

.activity-item.activity-warning .activity-icon {
    background-color: #ffb900;
}

.activity-item.activity-info .activity-icon {
    background-color: #0073aa;
}

.activity-content {
    flex: 1;
}

.activity-message {
    font-weight: 500;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 12px;
    color: #666;
}

/* Metrics Grid */
.wings-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.metric-card {
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-align: center;
}

.metric-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

/* Export Controls */
.wings-export-controls .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.wings-export-output {
    background: #f9f9f9;
    padding: 10px;
    border: 1px solid #ddd;
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 12px;
}

/* Quick Stats */
.wings-quick-stats .stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #0073aa;
}

/* Health Indicators */
.wings-health-indicators .health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.health-status {
    font-size: 16px;
}

.health-status.health-good {
    color: #46b450;
}

.health-status.health-warning {
    color: #ffb900;
}

.health-status.health-bad {
    color: #dc3232;
}

/* Alerts */
.wings-alerts .alert-item {
    padding: 8px;
    margin-bottom: 8px;
    border-left: 3px solid #ddd;
    background: #f9f9f9;
}

.wings-alerts .alert-item.alert-error {
    border-left-color: #dc3232;
}

.wings-alerts .alert-item.alert-warning {
    border-left-color: #ffb900;
}

.alert-message {
    font-size: 12px;
    margin-bottom: 2px;
}

.alert-time {
    font-size: 11px;
    color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .wings-charts-container {
        grid-template-columns: 1fr;
    }

    .wings-progress-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
</style>
