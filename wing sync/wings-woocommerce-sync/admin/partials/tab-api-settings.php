<?php
/**
 * API Settings Tab
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wings-main-content">
    <!-- API Configuration -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('API Konfiguracija', 'wings-sync'); ?></h2>
        <div class="inside">
            <form method="post" action="options.php">
                <?php
                settings_fields('wings_sync_settings');
                do_settings_sections('wings_sync_settings');
                ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="api_url"><?php _e('Wings API URL', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="url" id="api_url" name="wings_sync_settings[api_url]" 
                                   value="<?php echo esc_attr($settings['api_url'] ?? 'https://portal.wings.rs/api/v1/'); ?>" 
                                   class="regular-text" />
                            <p class="description"><?php _e('Base URL za Wings Portal API', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="api_alias"><?php _e('Wings Alias', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="api_alias" name="wings_sync_settings[api_alias]" 
                                   value="<?php echo esc_attr($settings['api_alias'] ?? ''); ?>" 
                                   class="regular-text" required />
                            <p class="description"><?php _e('Klijentski alias za Wings Portal (npr. grosstest)', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="api_username"><?php _e('Korisničko ime', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="api_username" name="wings_sync_settings[api_username]" 
                                   value="<?php echo esc_attr($settings['api_username'] ?? ''); ?>" 
                                   class="regular-text" required />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="api_password"><?php _e('Lozinka', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="api_password" name="wings_sync_settings[api_password]" 
                                   value="<?php echo esc_attr($settings['api_password'] ?? ''); ?>" 
                                   class="regular-text" required />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Test konekcije', 'wings-sync'); ?></th>
                        <td>
                            <button type="button" id="test-connection" class="button button-secondary">
                                <?php _e('Testiraj konekciju', 'wings-sync'); ?>
                            </button>
                            <span id="connection-status"></span>
                        </td>
                    </tr>
                </table>

                <h3><?php _e('Opcije sinhronizacije', 'wings-sync'); ?></h3>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Automatska sinhronizacija', 'wings-sync'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wings_sync_settings[auto_sync]" value="1" 
                                       <?php checked($settings['auto_sync'] ?? false); ?> />
                                <?php _e('Omogući automatsku sinhronizaciju', 'wings-sync'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="sync_interval"><?php _e('Interval sinhronizacije', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <select id="sync_interval" name="wings_sync_settings[sync_interval]">
                                <option value="5" <?php selected($settings['sync_interval'] ?? 15, 5); ?>>
                                    <?php _e('5 minuta', 'wings-sync'); ?>
                                </option>
                                <option value="15" <?php selected($settings['sync_interval'] ?? 15, 15); ?>>
                                    <?php _e('15 minuta', 'wings-sync'); ?>
                                </option>
                                <option value="30" <?php selected($settings['sync_interval'] ?? 15, 30); ?>>
                                    <?php _e('30 minuta', 'wings-sync'); ?>
                                </option>
                                <option value="60" <?php selected($settings['sync_interval'] ?? 15, 60); ?>>
                                    <?php _e('1 sat', 'wings-sync'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="batch_size"><?php _e('Veličina batch-a', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="batch_size" name="wings_sync_settings[batch_size]" 
                                   value="<?php echo esc_attr($settings['batch_size'] ?? 50); ?>" 
                                   min="10" max="200" class="small-text" />
                            <p class="description"><?php _e('Broj proizvoda koji se obrađuje odjednom (10-200)', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="timeout"><?php _e('Timeout (sekunde)', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="timeout" name="wings_sync_settings[timeout]" 
                                   value="<?php echo esc_attr($settings['timeout'] ?? 60); ?>" 
                                   min="30" max="300" class="small-text" />
                            <p class="description"><?php _e('Maksimalno vreme čekanja za API pozive (30-300)', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                </table>

                <h3><?php _e('Podešavanja za kupce', 'wings-sync'); ?></h3>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Sinhronizacija kupaca', 'wings-sync'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wings_sync_settings[enable_customer_sync]" value="1" 
                                       <?php checked($settings['enable_customer_sync'] ?? false); ?> />
                                <?php _e('Omogući sinhronizaciju kupaca', 'wings-sync'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="customer_sync_interval"><?php _e('Interval sinhronizacije kupaca', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <select id="customer_sync_interval" name="wings_sync_settings[customer_sync_interval]">
                                <option value="hourly" <?php selected($settings['customer_sync_interval'] ?? 'hourly', 'hourly'); ?>>
                                    <?php _e('Svaki sat', 'wings-sync'); ?>
                                </option>
                                <option value="twicedaily" <?php selected($settings['customer_sync_interval'] ?? 'hourly', 'twicedaily'); ?>>
                                    <?php _e('Dva puta dnevno', 'wings-sync'); ?>
                                </option>
                                <option value="daily" <?php selected($settings['customer_sync_interval'] ?? 'hourly', 'daily'); ?>>
                                    <?php _e('Jednom dnevno', 'wings-sync'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="customer_batch_size"><?php _e('Veličina batch-a za kupce', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="customer_batch_size" name="wings_sync_settings[customer_batch_size]" 
                                   value="<?php echo esc_attr($settings['customer_batch_size'] ?? 50); ?>" 
                                   min="10" max="100" class="small-text" />
                            <p class="description"><?php _e('Broj kupaca koji se obrađuje odjednom (10-100)', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(__('Sačuvaj podešavanja', 'wings-sync')); ?>
            </form>
        </div>
    </div>

    <!-- Test Environment Presets -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Test okruženje - Brza konfiguracija', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Koristite ove predefinirane vrednosti za brzo testiranje:', 'wings-sync'); ?></p>
            
            <div class="wings-preset-buttons">
                <button type="button" id="set-test-product-api" class="button button-secondary">
                    <?php _e('Podesi test API za proizvode', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="set-test-customer-api" class="button button-secondary">
                    <?php _e('Podesi test API za kupce', 'wings-sync'); ?>
                </button>
            </div>
            
            <div style="margin-top: 15px;">
                <p><strong><?php _e('Test okruženje:', 'wings-sync'); ?></strong></p>
                <ul style="margin-left: 20px;">
                    <li>URL: <code>https://portal.wings.rs/api/v1/</code></li>
                    <li>Alias: <code>grosstest</code></li>
                    <li>Username: <code>aql</code></li>
                    <li>Password: <code>grossaql</code></li>
                </ul>
                <p class="description">
                    <?php _e('Test okruženje koristi Wings Portal test bazu sa test podacima. Bezbedno je za testiranje.', 'wings-sync'); ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar -->
<div class="wings-sidebar">
    <!-- Sync Status -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Status sinhronizacije', 'wings-sync'); ?></h2>
        <div class="inside">
            <table class="widefat">
                <tr>
                    <td><?php _e('Ukupno proizvoda:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($sync_stats['total_products']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Sinhronizovani:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($sync_stats['synced_products']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Poslednja sinhronizacija:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($sync_stats['last_sync_formatted']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Auto sinhronizacija:', 'wings-sync'); ?></td>
                    <td><strong>
                        <?php echo ($settings['auto_sync'] ?? false) ? 
                            '<span style="color: green;">' . __('Uključena', 'wings-sync') . '</span>' : 
                            '<span style="color: red;">' . __('Isključena', 'wings-sync') . '</span>'; ?>
                    </strong></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Help -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Pomoć', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><strong><?php _e('Potrebna pomoć?', 'wings-sync'); ?></strong></p>
            <ul>
                <li><a href="#" target="_blank"><?php _e('Dokumentacija', 'wings-sync'); ?></a></li>
                <li><a href="#" target="_blank"><?php _e('Podrška', 'wings-sync'); ?></a></li>
                <li><a href="#" target="_blank"><?php _e('FAQ', 'wings-sync'); ?></a></li>
            </ul>
            
            <p><strong><?php _e('Plugin verzija:', 'wings-sync'); ?></strong> <?php echo WINGS_SYNC_VERSION; ?></p>
        </div>
    </div>
</div>
