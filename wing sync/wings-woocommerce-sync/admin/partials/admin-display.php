<?php
/**
 * Admin page template - Reorganized with tabs
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get current tab
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'api-settings';
?>

<div class="wrap">
    <h1><?php _e('Wings WooCommerce Sync', 'wings-sync'); ?></h1>

    <?php settings_errors(); ?>

    <!-- Navigation Tabs -->
    <nav class="nav-tab-wrapper">
        <a href="<?php echo admin_url('admin.php?page=wings-sync&tab=api-settings'); ?>"
           class="nav-tab <?php echo $current_tab === 'api-settings' ? 'nav-tab-active' : ''; ?>">
            <?php _e('API Podešavanja', 'wings-sync'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=wings-sync&tab=test-commands'); ?>"
           class="nav-tab <?php echo $current_tab === 'test-commands' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Test Komande', 'wings-sync'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=wings-sync&tab=product-sync'); ?>"
           class="nav-tab <?php echo $current_tab === 'product-sync' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Sinhronizacija Proizvoda', 'wings-sync'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=wings-sync&tab=customer-management'); ?>"
           class="nav-tab <?php echo $current_tab === 'customer-management' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Upravljanje Kupcima', 'wings-sync'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=wings-sync&tab=progress-report'); ?>"
           class="nav-tab <?php echo $current_tab === 'progress-report' ? 'nav-tab-active' : ''; ?>">
            <?php _e('Izveštaj o Napretku', 'wings-sync'); ?>
        </a>
    </nav>

    <div class="wings-admin-container">
        <?php
        // Display content based on current tab
        switch ($current_tab) {
            case 'api-settings':
                include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/tab-api-settings.php';
                break;
            case 'test-commands':
                include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/tab-test-commands.php';
                break;
            case 'product-sync':
                include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/tab-product-sync.php';
                break;
            case 'customer-management':
                include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/tab-customer-management.php';
                break;
            case 'progress-report':
                include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/tab-progress-report.php';
                break;
            default:
                include WINGS_SYNC_PLUGIN_DIR . 'admin/partials/tab-api-settings.php';
                break;
        }
        ?>

    </div>
</div>
