<?php
/**
 * Customer Management Tab
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wings-main-content">
    <!-- Retrieve and Create Customers -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Preuzmi i kreiraj kupce iz Wings-a', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Preuzmite kupce iz Wings Portal-a i kreirajte ih kao WooCommerce korisnike.', 'wings-sync'); ?></p>
            
            <div class="wings-customer-controls">
                <button type="button" id="retrieve-all-customers" class="button button-primary">
                    <?php _e('Preuzmi sve kupce iz Wings-a', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="retrieve-new-customers" class="button button-secondary">
                    <?php _e('Preuzmi samo nove kupce', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="retrieve-updated-customers" class="button button-secondary">
                    <?php _e('Preuzmi ažurirane kupce', 'wings-sync'); ?>
                </button>
            </div>
            
            <div class="wings-customer-options" style="margin-top: 15px;">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="customer-batch-size"><?php _e('Broj kupaca po batch-u', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="customer-batch-size" value="50" min="10" max="200" class="small-text" />
                            <p class="description"><?php _e('Koliko kupaca da se obradi odjednom (10-200)', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="customer-date-filter"><?php _e('Filter po datumu', 'wings-sync'); ?></label>
                        </th>
                        <td>
                            <input type="date" id="customer-date-from" class="regular-text" />
                            <span><?php _e('do', 'wings-sync'); ?></span>
                            <input type="date" id="customer-date-to" class="regular-text" />
                            <p class="description"><?php _e('Preuzmi kupce kreirane/ažurirane u datom periodu', 'wings-sync'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div id="retrieve-progress" style="display: none; margin-top: 15px;">
                <div class="sync-progress-bar">
                    <div class="sync-progress-fill"></div>
                </div>
                <p id="retrieve-status"><?php _e('Preuzimanje u toku...', 'wings-sync'); ?></p>
                <div id="retrieve-details"></div>
            </div>
            
            <div id="retrieve-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati preuzimanja:', 'wings-sync'); ?></h4>
                <div id="retrieve-stats"></div>
            </div>
        </div>
    </div>

    <!-- Sync Customers -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Sinhronizuj kupce između Wings-a i WP', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Sinhronizujte postojeće kupce između Wings Portal-a i WooCommerce-a.', 'wings-sync'); ?></p>
            
            <div class="wings-sync-controls">
                <button type="button" id="sync-all-customers" class="button button-primary">
                    <?php _e('Sinhronizuj sve kupce', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="sync-customer-data" class="button button-secondary">
                    <?php _e('Sinhronizuj samo podatke', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="sync-customer-addresses" class="button button-secondary">
                    <?php _e('Sinhronizuj adrese', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="check-customer-conflicts" class="button button-secondary">
                    <?php _e('Proveri konflikte', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="sync-customer-progress" style="display: none; margin-top: 15px;">
                <div class="sync-progress-bar">
                    <div class="sync-progress-fill"></div>
                </div>
                <p id="sync-customer-status"><?php _e('Sinhronizacija u toku...', 'wings-sync'); ?></p>
                <div id="sync-customer-details"></div>
            </div>
            
            <div id="sync-customer-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati sinhronizacije:', 'wings-sync'); ?></h4>
                <div id="sync-customer-stats"></div>
            </div>
        </div>
    </div>

    <!-- Write Unsynced from WP to Wings -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Pošalji nesinhronizovane kupce iz WP u Wings', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Pronađite WooCommerce kupce koji nisu sinhronizovani sa Wings Portal-om i pošaljite ih.', 'wings-sync'); ?></p>
            
            <div class="wings-unsynced-controls">
                <button type="button" id="find-unsynced-wp-customers" class="button button-secondary">
                    <?php _e('Pronađi nesinhronizovane WP kupce', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="send-unsynced-to-wings" class="button button-primary">
                    <?php _e('Pošalji u Wings Portal', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="preview-wp-to-wings" class="button button-secondary">
                    <?php _e('Pregled pre slanja', 'wings-sync'); ?>
                </button>
            </div>
            
            <div class="wings-unsynced-options" style="margin-top: 15px;">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Kriterijumi za slanje', 'wings-sync'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" id="include-guest-customers" checked />
                                <?php _e('Uključi guest kupce sa narudžbinama', 'wings-sync'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" id="include-registered-only" checked />
                                <?php _e('Uključi samo registrovane korisnike', 'wings-sync'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" id="validate-before-send" checked />
                                <?php _e('Validiraj podatke pre slanja', 'wings-sync'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div id="wp-to-wings-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati slanja:', 'wings-sync'); ?></h4>
                <div id="wp-to-wings-output" class="wings-sync-output"></div>
            </div>
        </div>
    </div>

    <!-- Write Unsynced from Wings to WP -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Pošalji nesinhronizovane kupce iz Wings-a u WP', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Pronađite Wings kupce koji nisu sinhronizovani sa WooCommerce-om i kreirajte ih.', 'wings-sync'); ?></p>
            
            <div class="wings-wings-to-wp-controls">
                <button type="button" id="find-unsynced-wings-customers" class="button button-secondary">
                    <?php _e('Pronađi nesinhronizovane Wings kupce', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="create-wp-customers" class="button button-primary">
                    <?php _e('Kreiraj WP korisnike', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="preview-wings-to-wp" class="button button-secondary">
                    <?php _e('Pregled pre kreiranja', 'wings-sync'); ?>
                </button>
            </div>
            
            <div class="wings-wings-to-wp-options" style="margin-top: 15px;">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Opcije kreiranja', 'wings-sync'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" id="send-welcome-email" />
                                <?php _e('Pošalji email dobrodošlice', 'wings-sync'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" id="generate-random-password" checked />
                                <?php _e('Generiši nasumičnu lozinku', 'wings-sync'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" id="set-customer-role" checked />
                                <?php _e('Postavi ulogu kupca', 'wings-sync'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div id="wings-to-wp-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati kreiranja:', 'wings-sync'); ?></h4>
                <div id="wings-to-wp-output" class="wings-sync-output"></div>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar -->
<div class="wings-sidebar">
    <!-- Customer Statistics -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Statistike kupaca', 'wings-sync'); ?></h2>
        <div class="inside">
            <?php
            // Get customer statistics
            $wp_customers = count_users();
            $total_wp_customers = $wp_customers['total_users'];
            
            // Get Wings customers count (this would need to be implemented)
            $wings_customers_count = get_option('wings_customers_count', 0);
            $synced_customers = get_option('wings_synced_customers_count', 0);
            ?>
            
            <table class="widefat">
                <tr>
                    <td><?php _e('WP kupci ukupno:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($total_wp_customers); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Wings kupci ukupno:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($wings_customers_count); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Sinhronizovani:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($synced_customers); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Poslednja sinhronizacija:', 'wings-sync'); ?></td>
                    <td><strong>
                        <?php 
                        $last_customer_sync = get_option('wings_last_customer_sync', 0);
                        echo $last_customer_sync ? date('Y-m-d H:i', $last_customer_sync) : __('Nikad', 'wings-sync');
                        ?>
                    </strong></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Customer Sync Status -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Status sinhronizacije kupaca', 'wings-sync'); ?></h2>
        <div class="inside">
            <?php if ($settings['enable_customer_sync'] ?? false): ?>
                <p style="color: green;">✓ <?php _e('Sinhronizacija kupaca je uključena', 'wings-sync'); ?></p>
                <p><strong><?php _e('Interval:', 'wings-sync'); ?></strong> <?php echo esc_html($settings['customer_sync_interval'] ?? 'hourly'); ?></p>
                <p><strong><?php _e('Batch veličina:', 'wings-sync'); ?></strong> <?php echo esc_html($settings['customer_batch_size'] ?? 50); ?></p>
            <?php else: ?>
                <p style="color: red;">✗ <?php _e('Sinhronizacija kupaca je isključena', 'wings-sync'); ?></p>
                <p><a href="<?php echo admin_url('admin.php?page=wings-sync&tab=api-settings'); ?>"><?php _e('Uključi u podešavanjima', 'wings-sync'); ?></a></p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Customer Activity -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Nedavna aktivnost kupaca', 'wings-sync'); ?></h2>
        <div class="inside">
            <?php 
            $customer_logs = array_filter(Wings_WooCommerce_Sync::get_log(), function($entry) {
                return strpos(strtolower($entry['message']), 'customer') !== false || 
                       strpos(strtolower($entry['message']), 'kupac') !== false;
            });
            $recent_customer_logs = array_slice($customer_logs, -5);
            
            if (empty($recent_customer_logs)): 
            ?>
                <p><?php _e('Nema nedavne aktivnosti kupaca.', 'wings-sync'); ?></p>
            <?php else: ?>
                <div class="wings-recent-activity">
                    <?php foreach (array_reverse($recent_customer_logs) as $entry): ?>
                        <div class="activity-entry activity-<?php echo esc_attr($entry['type']); ?>">
                            <div class="activity-time"><?php echo esc_html(date('H:i', strtotime($entry['timestamp']))); ?></div>
                            <div class="activity-message"><?php echo esc_html($entry['message']); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Customer Actions -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Brze akcije za kupce', 'wings-sync'); ?></h2>
        <div class="inside">
            <button type="button" id="quick-customer-count" class="button button-secondary" style="width: 100%; margin-bottom: 10px;">
                <?php _e('Prebroj kupce', 'wings-sync'); ?>
            </button>
            
            <button type="button" id="validate-customer-data" class="button button-secondary" style="width: 100%; margin-bottom: 10px;">
                <?php _e('Validiraj podatke kupaca', 'wings-sync'); ?>
            </button>
            
            <button type="button" id="export-customer-report" class="button button-secondary" style="width: 100%;">
                <?php _e('Izvezi izveštaj o kupcima', 'wings-sync'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.wings-customer-controls .button,
.wings-sync-controls .button,
.wings-unsynced-controls .button,
.wings-wings-to-wp-controls .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.wings-sync-output {
    background: #f9f9f9;
    padding: 10px;
    border: 1px solid #ddd;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 12px;
}

.sync-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.sync-progress-fill {
    height: 100%;
    background-color: #0073aa;
    width: 0%;
    transition: width 0.3s ease;
}

.wings-recent-activity .activity-entry {
    display: flex;
    margin-bottom: 8px;
    padding: 5px;
    border-left: 3px solid #ddd;
}

.wings-recent-activity .activity-entry.activity-success {
    border-left-color: #46b450;
}

.wings-recent-activity .activity-entry.activity-error {
    border-left-color: #dc3232;
}

.wings-recent-activity .activity-entry.activity-warning {
    border-left-color: #ffb900;
}

.wings-recent-activity .activity-time {
    font-weight: bold;
    margin-right: 10px;
    min-width: 40px;
}

.wings-recent-activity .activity-message {
    flex: 1;
    font-size: 12px;
}
</style>
