<?php
/**
 * Product Sync Tab
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wings-main-content">
    <!-- Manual Product Sync -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Manualna sinhronizacija proizvoda', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Pokrenite manualnu sinhronizaciju svih proizvoda sa Wings Portal-om.', 'wings-sync'); ?></p>
            
            <div class="wings-sync-controls">
                <button type="button" id="manual-sync" class="button button-primary">
                    <?php _e('Sinhronizuj sve proizvode', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="sync-stock-only" class="button button-secondary">
                    <?php _e('Sinhronizuj samo zalihe', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="sync-prices-only" class="button button-secondary">
                    <?php _e('Sinhronizuj samo cene', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="sync-progress" style="display: none; margin-top: 15px;">
                <div class="sync-progress-bar">
                    <div class="sync-progress-fill"></div>
                </div>
                <p id="sync-status"><?php _e('Sinhronizacija u toku...', 'wings-sync'); ?></p>
                <div id="sync-details"></div>
            </div>
            
            <div id="sync-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati sinhronizacije:', 'wings-sync'); ?></h4>
                <div id="sync-stats"></div>
            </div>
        </div>
    </div>

    <!-- Selective Product Sync -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Selektivna sinhronizacija', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Sinhronizujte specifične proizvode ili kategorije.', 'wings-sync'); ?></p>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="sync-by-category"><?php _e('Sinhronizuj po kategoriji', 'wings-sync'); ?></label>
                    </th>
                    <td>
                        <select id="sync-by-category" class="regular-text">
                            <option value=""><?php _e('Izaberite kategoriju...', 'wings-sync'); ?></option>
                            <?php
                            $categories = get_terms(array(
                                'taxonomy' => 'product_cat',
                                'hide_empty' => false,
                            ));
                            foreach ($categories as $category) {
                                echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                            }
                            ?>
                        </select>
                        <button type="button" id="sync-category" class="button button-secondary">
                            <?php _e('Sinhronizuj kategoriju', 'wings-sync'); ?>
                        </button>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="sync-by-sku"><?php _e('Sinhronizuj po SKU', 'wings-sync'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="sync-by-sku" class="regular-text" 
                               placeholder="<?php _e('Unesite SKU ili više SKU-ova odvojenih zarezom', 'wings-sync'); ?>" />
                        <button type="button" id="sync-sku" class="button button-secondary">
                            <?php _e('Sinhronizuj SKU', 'wings-sync'); ?>
                        </button>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="sync-date-range"><?php _e('Sinhronizuj po datumu', 'wings-sync'); ?></label>
                    </th>
                    <td>
                        <input type="date" id="sync-date-from" class="regular-text" />
                        <span><?php _e('do', 'wings-sync'); ?></span>
                        <input type="date" id="sync-date-to" class="regular-text" />
                        <button type="button" id="sync-date-range" class="button button-secondary">
                            <?php _e('Sinhronizuj period', 'wings-sync'); ?>
                        </button>
                        <p class="description"><?php _e('Sinhronizuj proizvode ažurirane u datom periodu', 'wings-sync'); ?></p>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Stock Management -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Upravljanje zalihama', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Specifične opcije za sinhronizaciju zaliha.', 'wings-sync'); ?></p>
            
            <div class="wings-stock-controls">
                <button type="button" id="sync-low-stock" class="button button-secondary">
                    <?php _e('Sinhronizuj proizvode sa niskim zalihama', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="sync-out-of-stock" class="button button-secondary">
                    <?php _e('Sinhronizuj proizvode bez zaliha', 'wings-sync'); ?>
                </button>
                
                <button type="button" id="check-stock-discrepancies" class="button button-secondary">
                    <?php _e('Proveri neusklađenost zaliha', 'wings-sync'); ?>
                </button>
            </div>
            
            <div id="stock-results" style="display: none; margin-top: 15px;">
                <h4><?php _e('Rezultati upravljanja zalihama:', 'wings-sync'); ?></h4>
                <div id="stock-output" class="wings-sync-output"></div>
            </div>
        </div>
    </div>

    <!-- Sync Schedule -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Raspored sinhronizacije', 'wings-sync'); ?></h2>
        <div class="inside">
            <p><?php _e('Upravljajte automatskim rasporedom sinhronizacije.', 'wings-sync'); ?></p>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Status automatske sinhronizacije', 'wings-sync'); ?></th>
                    <td>
                        <?php if ($settings['auto_sync'] ?? false): ?>
                            <span style="color: green;">✓ <?php _e('Uključena', 'wings-sync'); ?></span>
                            <p class="description">
                                <?php printf(__('Sledeća sinhronizacija: %s', 'wings-sync'), 
                                    wp_next_scheduled('wings_sync_cron_hook') ? 
                                    date('Y-m-d H:i:s', wp_next_scheduled('wings_sync_cron_hook')) : 
                                    __('Nije zakazana', 'wings-sync')); ?>
                            </p>
                        <?php else: ?>
                            <span style="color: red;">✗ <?php _e('Isključena', 'wings-sync'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Kontrola rasporedа', 'wings-sync'); ?></th>
                    <td>
                        <button type="button" id="force-schedule-sync" class="button button-secondary">
                            <?php _e('Pokreni zakazanu sinhronizaciju sada', 'wings-sync'); ?>
                        </button>
                        
                        <button type="button" id="clear-schedule" class="button button-secondary">
                            <?php _e('Obriši raspored', 'wings-sync'); ?>
                        </button>
                        
                        <button type="button" id="reschedule-sync" class="button button-secondary">
                            <?php _e('Ponovo zakaži sinhronizaciju', 'wings-sync'); ?>
                        </button>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>

<!-- Sidebar -->
<div class="wings-sidebar">
    <!-- Sync Statistics -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Statistike sinhronizacije', 'wings-sync'); ?></h2>
        <div class="inside">
            <table class="widefat">
                <tr>
                    <td><?php _e('Ukupno proizvoda:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($sync_stats['total_products']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Sinhronizovani:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($sync_stats['synced_products']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Poslednja sinhronizacija:', 'wings-sync'); ?></td>
                    <td><strong><?php echo esc_html($sync_stats['last_sync_formatted']); ?></strong></td>
                </tr>
                <tr>
                    <td><?php _e('Uspešnost:', 'wings-sync'); ?></td>
                    <td><strong>
                        <?php 
                        $success_rate = $sync_stats['total_products'] > 0 ? 
                            round(($sync_stats['synced_products'] / $sync_stats['total_products']) * 100, 1) : 0;
                        echo $success_rate . '%';
                        ?>
                    </strong></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Recent Sync Activity -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Nedavna aktivnost', 'wings-sync'); ?></h2>
        <div class="inside">
            <?php 
            $recent_logs = array_slice(Wings_WooCommerce_Sync::get_log(), -5);
            if (empty($recent_logs)): 
            ?>
                <p><?php _e('Nema nedavne aktivnosti.', 'wings-sync'); ?></p>
            <?php else: ?>
                <div class="wings-recent-activity">
                    <?php foreach (array_reverse($recent_logs) as $entry): ?>
                        <div class="activity-entry activity-<?php echo esc_attr($entry['type']); ?>">
                            <div class="activity-time"><?php echo esc_html(date('H:i', strtotime($entry['timestamp']))); ?></div>
                            <div class="activity-message"><?php echo esc_html($entry['message']); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="postbox">
        <h2 class="hndle"><?php _e('Brze akcije', 'wings-sync'); ?></h2>
        <div class="inside">
            <button type="button" id="quick-stock-sync" class="button button-primary" style="width: 100%; margin-bottom: 10px;">
                <?php _e('Brza sinhronizacija zaliha', 'wings-sync'); ?>
            </button>
            
            <button type="button" id="emergency-stop" class="button button-secondary" style="width: 100%; margin-bottom: 10px; background-color: #dc3232; border-color: #dc3232; color: white;">
                <?php _e('Zaustavi sinhronizaciju', 'wings-sync'); ?>
            </button>
            
            <button type="button" id="view-sync-log" class="button button-secondary" style="width: 100%;">
                <?php _e('Prikaži kompletan log', 'wings-sync'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.wings-sync-controls .button,
.wings-stock-controls .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.sync-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.sync-progress-fill {
    height: 100%;
    background-color: #0073aa;
    width: 0%;
    transition: width 0.3s ease;
}

.wings-sync-output {
    background: #f9f9f9;
    padding: 10px;
    border: 1px solid #ddd;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 12px;
}

.wings-recent-activity .activity-entry {
    display: flex;
    margin-bottom: 8px;
    padding: 5px;
    border-left: 3px solid #ddd;
}

.wings-recent-activity .activity-entry.activity-success {
    border-left-color: #46b450;
}

.wings-recent-activity .activity-entry.activity-error {
    border-left-color: #dc3232;
}

.wings-recent-activity .activity-entry.activity-warning {
    border-left-color: #ffb900;
}

.wings-recent-activity .activity-time {
    font-weight: bold;
    margin-right: 10px;
    min-width: 40px;
}

.wings-recent-activity .activity-message {
    flex: 1;
    font-size: 12px;
}
</style>
