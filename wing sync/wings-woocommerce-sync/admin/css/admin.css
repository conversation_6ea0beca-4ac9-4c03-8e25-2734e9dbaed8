/**
 * Wings WooCommerce Sync - Admin Styles
 */

.wings-admin-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.wings-main-content {
    flex: 2;
}

.wings-sidebar {
    flex: 1;
    max-width: 300px;
}

/* Status indicators */
.wings-status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.wings-status-indicator.success {
    background-color: #46b450;
}

.wings-status-indicator.error {
    background-color: #dc3232;
}

.wings-status-indicator.warning {
    background-color: #ffb900;
}

.wings-status-indicator.info {
    background-color: #00a0d2;
}

/* Connection status */
#connection-status {
    margin-left: 10px;
    font-weight: bold;
}

#connection-status.success {
    color: #46b450;
}

#connection-status.error {
    color: #dc3232;
}

/* Sync progress */
#sync-progress {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.sync-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.sync-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00a0d2, #0073aa);
    width: 0%;
    transition: width 0.3s ease;
    animation: progress-animation 2s infinite;
}

@keyframes progress-animation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Sync results */
#sync-results {
    margin-top: 15px;
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #0073aa;
    border-radius: 4px;
}

#sync-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.sync-stat {
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.sync-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

.sync-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

/* Log container */
.wings-log-container {
    max-height: 400px;
    overflow-y: auto;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.log-entry {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    font-family: monospace;
    font-size: 12px;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #666;
    margin-right: 10px;
    min-width: 140px;
}

.log-type {
    font-weight: bold;
    margin-right: 10px;
    min-width: 60px;
}

.log-message {
    flex: 1;
}

.log-entry.log-error .log-type {
    color: #dc3232;
}

.log-entry.log-warning .log-type {
    color: #ffb900;
}

.log-entry.log-info .log-type {
    color: #00a0d2;
}

.log-entry.log-success .log-type {
    color: #46b450;
}

/* Buttons */
.wings-button-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #fff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status table */
.wings-sidebar .widefat td {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
}

.wings-sidebar .widefat td:first-child {
    font-weight: 500;
    color: #555;
}

/* Responsive design */
@media (max-width: 1200px) {
    .wings-admin-container {
        flex-direction: column;
    }
    
    .wings-sidebar {
        max-width: none;
    }
}

@media (max-width: 768px) {
    #sync-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .log-entry {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .log-time,
    .log-type {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 4px;
    }
}

/* Notices */
.wings-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    background: #fff;
}

.wings-notice.notice-success {
    border-left-color: #46b450;
}

.wings-notice.notice-error {
    border-left-color: #dc3232;
}

.wings-notice.notice-warning {
    border-left-color: #ffb900;
}

.wings-notice.notice-info {
    border-left-color: #00a0d2;
}

/* Form improvements */
.form-table th {
    width: 200px;
}

.form-table td input[type="text"],
.form-table td input[type="url"],
.form-table td input[type="password"],
.form-table td input[type="number"],
.form-table td select {
    width: 100%;
    max-width: 400px;
}

.form-table td .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Loading overlay */
.wings-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.wings-loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Tooltips */
.wings-tooltip {
    position: relative;
    cursor: help;
}

.wings-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
    z-index: 1000;
}

.wings-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}
