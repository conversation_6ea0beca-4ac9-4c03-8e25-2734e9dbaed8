#!/usr/bin/env python3
"""
Analiza preuzenih podataka iz Wings Portal API

Ova skripta analizira CSV fajlove preuzete iz Wings Portal-a i generiše izveštaje.
"""

import pandas as pd
import os
import sys
from datetime import datetime
import glob

def find_latest_export_dir():
    """
    Pronalazi najnoviji wings_export direktorijum
    
    Returns:
        str: Putanja do najnovijeg direktorijuma ili None
    """
    pattern = "wings_export_*"
    dirs = glob.glob(pattern)
    
    if not dirs:
        return None
        
    # Sortiraj po imenu (koji sadrži timestamp)
    dirs.sort(reverse=True)
    return dirs[0]

def analyze_warehouses(df_warehouses):
    """
    Analizira podatke o magacinima
    
    Args:
        df_warehouses: DataFrame sa podacima o magacinima
    """
    print("=== ANALIZA MAGACINA ===")
    print(f"Ukupno magacina: {len(df_warehouses)}")
    print("\nLista magacina:")
    for _, row in df_warehouses.iterrows():
        print(f"  - {row['naziv']} (ID: {row['id']}, Šifra: {row['sifra']})")
    print()

def analyze_articles(df_articles):
    """
    Analizira podatke o artiklima
    
    Args:
        df_articles: DataFrame sa podacima o artiklima
    """
    print("=== ANALIZA ARTIKALA ===")
    print(f"Ukupno artikala: {len(df_articles)}")
    
    # Osnovne statistike
    if 'kolicina' in df_articles.columns:
        total_quantity = df_articles['kolicina'].sum()
        avg_quantity = df_articles['kolicina'].mean()
        print(f"Ukupna količina na stanju: {total_quantity:,.2f}")
        print(f"Prosečna količina po artiklu: {avg_quantity:.2f}")
        
        # Artikli bez stanja
        zero_stock = df_articles[df_articles['kolicina'] == 0]
        print(f"Artikli bez stanja: {len(zero_stock)} ({len(zero_stock)/len(df_articles)*100:.1f}%)")
        
        # Top 10 artikala po količini
        print("\nTop 10 artikala po količini:")
        top_quantity = df_articles.nlargest(10, 'kolicina')[['naziv', 'sifra', 'kolicina', 'jm']]
        for _, row in top_quantity.iterrows():
            print(f"  - {row['naziv']} ({row['sifra']}): {row['kolicina']} {row['jm']}")
    
    # Analiza po vrstama artikala
    if 'vrsta' in df_articles.columns:
        print(f"\nAnaliza po vrstama artikala:")
        vrsta_counts = df_articles['vrsta'].value_counts()
        for vrsta, count in vrsta_counts.head(10).items():
            print(f"  - {vrsta}: {count} artikala")
    
    # Analiza po proizvođačima
    if 'proizvodjac' in df_articles.columns:
        print(f"\nTop 10 proizvođača:")
        proizvodjac_counts = df_articles['proizvodjac'].value_counts()
        for proizvodjac, count in proizvodjac_counts.head(10).items():
            if pd.notna(proizvodjac) and proizvodjac.strip():
                print(f"  - {proizvodjac}: {count} artikala")
    
    # Cene
    if 'cena' in df_articles.columns:
        print(f"\nAnaliza cena:")
        avg_price = df_articles['cena'].mean()
        min_price = df_articles['cena'].min()
        max_price = df_articles['cena'].max()
        print(f"  - Prosečna cena: {avg_price:.2f}")
        print(f"  - Najniža cena: {min_price:.2f}")
        print(f"  - Najviša cena: {max_price:.2f}")
        
        # Top 10 najskupljih artikala
        print(f"\nTop 10 najskupljih artikala:")
        top_price = df_articles.nlargest(10, 'cena')[['naziv', 'sifra', 'cena']]
        for _, row in top_price.iterrows():
            print(f"  - {row['naziv']} ({row['sifra']}): {row['cena']:.2f}")
    
    print()

def analyze_warehouse_stock(df_stock):
    """
    Analizira stanje po magacinima
    
    Args:
        df_stock: DataFrame sa stanjem po magacinima
    """
    print("=== ANALIZA STANJA PO MAGACINIMA ===")
    print(f"Ukupno stavki stanja: {len(df_stock)}")
    
    # Analiza po magacinima
    if 'magacin-id' in df_stock.columns:
        print(f"\nBroj artikala po magacinima:")
        magacin_counts = df_stock['magacin-id'].value_counts()
        for magacin_id, count in magacin_counts.items():
            print(f"  - Magacin {magacin_id}: {count} artikala")
    
    # Jedinstveni artikli
    if 'artikal-id' in df_stock.columns:
        unique_articles = df_stock['artikal-id'].nunique()
        print(f"\nJedinstveni artikli sa stanjem: {unique_articles}")
    
    print()

def generate_summary_report(export_dir):
    """
    Generiše sažeti izveštaj
    
    Args:
        export_dir: Direktorijum sa izvezenim podacima
    """
    print("=== SAŽETI IZVEŠTAJ ===")
    print(f"Direktorijum: {export_dir}")
    print(f"Vreme analize: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Proveri koje fajlove imamo
    files = os.listdir(export_dir)
    print(f"\nDostupni fajlovi:")
    for file in files:
        file_path = os.path.join(export_dir, file)
        size = os.path.getsize(file_path)
        print(f"  - {file}: {size:,} bytes")
    
    print()

def main():
    """
    Glavna funkcija
    """
    print("=== ANALIZA WINGS PORTAL PODATAKA ===")
    print()
    
    # Pronađi najnoviji export direktorijum
    export_dir = find_latest_export_dir()
    
    if not export_dir:
        print("Greška: Nije pronađen nijedan wings_export direktorijum!")
        print("Prvo pokrenite wings_portal_sync.py da preuzmete podatke.")
        return
    
    print(f"Analiziram podatke iz: {export_dir}")
    print()
    
    # Generiši sažeti izveštaj
    generate_summary_report(export_dir)
    
    try:
        # Učitaj podatke o magacinima
        warehouses_file = os.path.join(export_dir, "magacini.csv")
        if os.path.exists(warehouses_file):
            df_warehouses = pd.read_csv(warehouses_file)
            analyze_warehouses(df_warehouses)
        else:
            print("Upozorenje: Fajl magacini.csv nije pronađen")
        
        # Učitaj podatke o artiklima
        articles_file = os.path.join(export_dir, "artikli_stanje.csv")
        if os.path.exists(articles_file):
            df_articles = pd.read_csv(articles_file)
            analyze_articles(df_articles)
        else:
            print("Upozorenje: Fajl artikli_stanje.csv nije pronađen")
        
        # Učitaj stanje po magacinima
        stock_file = os.path.join(export_dir, "stanje_po_magacinima.csv")
        if os.path.exists(stock_file):
            df_stock = pd.read_csv(stock_file)
            analyze_warehouse_stock(df_stock)
        else:
            print("Upozorenje: Fajl stanje_po_magacinima.csv nije pronađen")
            
    except Exception as e:
        print(f"Greška pri analizi podataka: {e}")
        return
    
    print("=== ANALIZA ZAVRŠENA ===")

if __name__ == "__main__":
    main()
