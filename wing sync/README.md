# Wings WooCommerce Sync Plugin

A comprehensive WordPress plugin for integrating Wings Portal ERP system with WooCommerce, providing both product and customer synchronization.

## 📋 Overview

This plugin enables complete bidirectional synchronization between Wings Portal and WooCommerce:

- **📦 Product Sync** - Inventory, stock levels, and prices
- **🧑‍🤝‍🧑 Customer Sync** - Customer data and business information

## 🚀 Main Features

- ✅ **Dual Synchronization**: Both products and customers
- ✅ **Bidirectional Sync**: Wings ↔ WooCommerce
- ✅ **Real-time Updates**: Automatic synchronization
- ✅ **Batch Processing**: Handle large datasets efficiently
- ✅ **HPOS Compatible**: Works with WooCommerce HPOS
- ✅ **Comprehensive Testing**: Built-in test suites
- ✅ **Admin Dashboard**: Easy management interface

---

# 📦 Product Synchronization

## Features

- ✅ **Real-time stock synchronization** from Wings Portal
- ✅ **Price updates** automatically from Wings
- ✅ **Multi-warehouse support** for complex inventory
- ✅ **Batch processing** for large product catalogs
- ✅ **SKU mapping** between Wings and WooCommerce
- ✅ **Low stock alerts** and notifications
- ✅ **Performance optimization** for large stores

## Product Data Mapping

| Wings Field | WooCommerce Field | Purpose |
|-------------|-------------------|---------|
| `sifra` | `sku` | Product identifier |
| `naziv` | `name` | Product name |
| `cena` | `regular_price` | Product price |
| `stanje` | `stock_quantity` | Stock level |
| `opis` | `description` | Product description |
| `kategorija` | `product_cat` | Product category |

## Product Sync Workflow

### Wings → WooCommerce (Scheduled)
1. **Fetch Products**: Get product data from Wings Portal API
2. **Transform Data**: Convert Wings format to WooCommerce
3. **Update Products**: Create/update WooCommerce products
4. **Sync Stock**: Update inventory levels
5. **Update Prices**: Sync current pricing

### Configuration
- **Sync Interval**: 5, 15, 30, or 60 minutes
- **Batch Size**: 10-200 products per batch
- **Warehouse Selection**: Choose specific warehouses
- **Price Rules**: Configure pricing logic

---

# 🧑‍🤝‍🧑 Customer Synchronization

## Features

- ✅ **Bidirectional customer synchronization** Wings ↔ WooCommerce
- ✅ **Enhanced data capture** (13+ business fields)
- ✅ **Advanced validation** and sanitization
- ✅ **Configurable sync controls** and intervals
- ✅ **Automatic and manual sync** options
- ✅ **Real-time sync** on customer registration/updates

## Customer Data Mapping

| Wings Field | WooCommerce Field | Purpose |
|-------------|-------------------|---------|
| `naziv` | `display_name`, `billing_company` | Customer/company name |
| `email` | `user_email` | Primary email |
| `adresa` | `billing_address_1` | Street address |
| `mesto` | `billing_city` | City |
| `telefon` | `billing_phone` | Phone number |
| `sifra` | `meta: wings_customer_code` | Customer code |
| `pib` | `meta: wings_pib` | Tax ID |
| `status` | `meta: wings_status` | Customer status |
| `komercijalista` | `meta: wings_sales_rep` | Sales representative |
| `klasa` | `meta: wings_customer_class` | Customer class |

## Customer Sync Workflow

### WooCommerce → Wings (Real-time)
1. **Customer Registration**: Auto-sync new customers to Wings
2. **Address Updates**: Sync billing address changes
3. **Account Updates**: Sync profile modifications

### Wings → WooCommerce (Scheduled)
1. **Fetch Customers**: Get customer data from Wings Portal
2. **Transform Data**: Convert to WooCommerce format
3. **Create/Update**: Sync customer records
4. **Metadata Sync**: Update Wings-specific fields

## Where Customers Are Created

### WordPress Database Tables
- **`wp_users`**: Main user account (email, name, registration)
- **`wp_usermeta`**: Customer details and Wings metadata

### Customer Fields Stored
- **Standard Fields**: name, email, address, phone
- **Wings Fields**: customer_id, customer_code, pib, status, sales_rep
- **Billing Info**: complete billing address details
---

# 🛠️ Installation & Setup

## Prerequisites

- **WordPress 5.5+**
- **WooCommerce 5.0+**
- **PHP 7.4+**
- **cURL extension**
- **Wings Portal API access**

## Installation Steps

1. **Upload Plugin**
   ```
   Upload wings-woocommerce-sync folder to /wp-content/plugins/
   ```

2. **Activate Plugin**
   ```
   Go to Plugins → Activate "Wings WooCommerce Sync"
   ```

3. **Configure Settings**
   ```
   Navigate to WooCommerce → Wings Sync
   ```

## API Configuration

### Production Environment
```
API URL: https://portal.wings.rs/api/v1/
API Alias: [your-alias]
Username: [your-username]
Password: [your-password]
```

### Test Environment
```
API URL: https://portal.wings.rs/api/v1/
API Alias: grosstest
Username: aql
Password: grossaql
```

---

# ⚙️ Configuration

## Product Sync Settings

### Basic Configuration
- **Enable Product Sync**: ✅ Turn on/off product synchronization
- **Sync Interval**: Choose frequency (5, 15, 30, 60 minutes)
- **Batch Size**: Number of products per batch (10-200)
- **Timeout**: API request timeout (30-300 seconds)

### Advanced Options
- **Warehouse Selection**: Choose specific warehouses to sync
- **Price Rules**: Configure pricing calculations
- **Stock Thresholds**: Set low stock alert levels
- **Category Mapping**: Map Wings categories to WooCommerce

## Customer Sync Settings

### Basic Configuration
- **Enable Customer Sync**: ✅ Turn on/off customer synchronization
- **Sync Interval**: Choose frequency (hourly, daily, weekly)
- **Customer Batch Size**: Number of customers per batch (10-100)
- **Sync Direction**: Bidirectional, Wings→WC, or WC→Wings

### Advanced Options
- **Field Mapping**: Customize field mappings
- **Validation Rules**: Set data validation requirements
- **Conflict Resolution**: Handle data conflicts
- **Auto-Registration**: Auto-create customers in Wings

---

# 🧪 Testing

## Product Sync Testing

### Test Connection
1. Go to **WooCommerce → Wings Sync**
2. Click **"Test Connection"**
3. Verify ✅ "Connection successful!"

### Manual Product Sync
1. Click **"Manual Sync"** button
2. Monitor sync progress
3. Check sync results and logs

### Test Products
- Verify products appear in WooCommerce
- Check stock levels are correct
- Confirm prices are updated

## Customer Sync Testing

### Quick Test Methods

#### Method 1: Admin Panel Button
1. Go to **WooCommerce → Wings Sync**
2. Find **"Test sinhronizacije kupaca"** section
3. Click **"Pokreni test kupaca"**

#### Method 2: URL Parameter
```
/wp-admin/admin.php?page=wings-sync&run_wings_customer_tests=1
```

#### Method 3: Customer Creation Test
```
/wp-admin/admin.php?page=wings-sync&run_customer_creation_tests=1
```

### Expected Test Results
```
Wings Customer Sync Test Suite
==============================

Testing Customer Data Transformation
✓ Wings to WooCommerce transformation working
✓ Data validation passed

Testing Wings Customer API
✓ API Connection successful!
✓ Retrieved customers from Wings Portal

Testing Customer Creation
✓ Customer data validation passed
✓ Customer creation functions working

Test Suite Complete
===================
All customer sync components tested successfully!
```

---

# 📊 Monitoring & Logs

## Admin Dashboard

### Sync Status Panel
- **Total Products**: Current product count
- **Synced Products**: Successfully synchronized
- **Last Sync**: Timestamp of last synchronization
- **Auto Sync Status**: Enabled/disabled status

### Customer Status Panel
- **Total Customers**: Current customer count
- **Synced Customers**: Successfully synchronized
- **Last Customer Sync**: Timestamp of last sync
- **Sync Direction**: Current sync configuration

## Log Management

### View Logs
- **Location**: WooCommerce → Wings Sync → Log section
- **Types**: Info, Warning, Error messages
- **Filtering**: Filter by date, type, or component

### Clear Logs
- **Manual**: Click "Clear Log" button
- **Automatic**: Logs auto-rotate after 30 days
- **Export**: Download logs for analysis

---

# 🔧 Troubleshooting

## Common Issues

### Product Sync Issues

**"Connection failed"**
- ✅ Check API credentials
- ✅ Verify Wings Portal access
- ✅ Test with Wings test environment

**"Timeout errors"**
- ✅ Increase timeout value
- ✅ Reduce batch size
- ✅ Check server performance

**"Memory errors"**
- ✅ Increase PHP memory limit
- ✅ Reduce batch size
- ✅ Enable background processing

### Customer Sync Issues

**"Customer validation failed"**
- ✅ Check required fields (name, email)
- ✅ Verify email format
- ✅ Ensure unique email addresses

**"Wings customer creation failed"**
- ✅ Check Wings Portal permissions
- ✅ Verify customer data format
- ✅ Review Wings API documentation

## Debug Mode

Enable detailed logging in `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## Support Resources

- **Documentation**: Complete guides in plugin folder
- **Test Suites**: Built-in testing functionality
- **Log Analysis**: Detailed error reporting
- **API Testing**: Wings Portal test environment

---

# � Documentation

## Detailed Guides

### 📦 Product Synchronization
- **[PRODUCT-SYNC-README.md](PRODUCT-SYNC-README.md)** - Complete product sync documentation
  - API endpoints and data mapping
  - Configuration options and performance tuning
  - Troubleshooting and best practices

### 🧑‍🤝‍🧑 Customer Synchronization
- **[CUSTOMER-SYNC-DETAILED.md](CUSTOMER-SYNC-DETAILED.md)** - Complete customer sync documentation
  - Bidirectional sync workflows
  - Data transformation and validation
  - WordPress database integration

### 🧪 Testing Guides
- **[TESTING-GUIDE.md](TESTING-GUIDE.md)** - Comprehensive testing instructions
- **[HOW-TO-TEST.md](HOW-TO-TEST.md)** - Quick testing guide
- **[TESTING-SUMMARY.md](TESTING-SUMMARY.md)** - Testing overview

## Quick Reference

### Product Sync
```php
// Manual product sync
$sync = Wings_Sync::get_instance();
$result = $sync->sync_all_products();

// Get product stock
$api = Wings_API::get_instance();
$stock = $api->get_article_warehouse_stock($article_id);
```

### Customer Sync
```php
// Manual customer sync
$customer_sync = new Wings_Customer_Sync();
$customer_sync->sync_customers_from_wings();

// Transform customer data
$wc_data = Wings_Customer_Transformer::wings_to_woocommerce($wings_customer);
```

# �📁 File Structure

```
wings-woocommerce-sync/
├── wings-woocommerce-sync.php              # Main plugin file
├── README.md                               # This file - main documentation
├── includes/
│   ├── class-wings-api.php                 # Wings Portal API client
│   ├── class-wings-sync.php                # Product sync logic
│   ├── class-wings-customer-sync.php       # Customer sync logic
│   ├── class-wings-customer-transformer.php # Data transformation
│   ├── class-wings-scheduler.php           # Sync scheduling
│   └── class-wings-admin.php               # Admin interface
├── admin/
│   ├── css/admin.css                       # Admin styles
│   ├── js/admin.js                         # Admin JavaScript
│   └── partials/admin-display.php          # Admin templates
├── test-customer-sync.php                  # Customer sync tests
├── test-customer-creation.php              # Customer creation tests
└── documentation/
    ├── PRODUCT-SYNC-README.md              # 📦 Product sync guide
    ├── CUSTOMER-SYNC-DETAILED.md           # 🧑‍🤝‍🧑 Customer sync guide
    ├── CUSTOMER-SYNC-README.md             # Legacy customer sync docs
    ├── TESTING-GUIDE.md                    # 🧪 Testing instructions
    ├── HOW-TO-TEST.md                      # Quick test guide
    └── TESTING-SUMMARY.md                  # Testing overview
```

---

# 🎯 Quick Start

## 1. Installation
```bash
# Upload to WordPress plugins directory
/wp-content/plugins/wings-woocommerce-sync/

# Activate in WordPress admin
Plugins → Activate "Wings WooCommerce Sync"
```

## 2. Configuration
```bash
# Navigate to settings
WooCommerce → Wings Sync

# Configure API (test environment)
API URL: https://portal.wings.rs/api/v1/
API Alias: grosstest
Username: aql
Password: grossaql
```

## 3. Testing
```bash
# Test connection
Click "Test Connection" button

# Test product sync
Click "Manual Sync" button

# Test customer sync
Click "Test Customer Sync" button
```

## 4. Production Setup
```bash
# Update API settings with production credentials
# Enable automatic sync
# Monitor sync logs
# Set appropriate sync intervals
```

---

# 📞 Support

## Getting Help
- **Documentation**: Complete guides in plugin folder
- **Testing**: Built-in test suites for validation
- **Logs**: Detailed error reporting and monitoring
- **API Testing**: Wings Portal test environment available

## Before Contacting Support
1. ✅ Review relevant documentation
2. ✅ Test with Wings Portal test environment
3. ✅ Check WordPress and plugin error logs
4. ✅ Run built-in test suites
5. ✅ Verify API credentials and connectivity

## License
GPL v2 or later - https://www.gnu.org/licenses/gpl-2.0.html
