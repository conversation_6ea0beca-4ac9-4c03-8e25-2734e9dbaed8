#!/usr/bin/env python3
"""
Wings Portal API Script - Logovanje i preuzimanje stanja po magacinima

Ova skripta se loguje na Wings Portal API i downloaduje stanje po magacinima za sve artikle.
"""

import requests
import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Optional
import time

class WingsPortalAPI:
    def __init__(self, base_url: str, alias: str):
        """
        Inicijalizuje Wings Portal API klijent

        Args:
            base_url: Base URL za Wings Portal (npr. https://portal.wings.rs)
            alias: <PERSON><PERSON> za klije<PERSON> (npr. grosstest)
        """
        self.base_url = base_url.rstrip('/')
        self.alias = alias
        self.session = requests.Session()
        self.session_token = None

    def login(self, username: str, password: str) -> bool:
        """
        Loguje se na Wings Portal API

        Args:
            username: <PERSON><PERSON><PERSON><PERSON><PERSON> ime
            password: <PERSON><PERSON><PERSON>

        Returns:
            True ako je login uspešan, False inače
        """
        login_url = f"{self.base_url}/api/v1/{self.alias}/system.user.log"

        # Pokušaj sa JSON POST formatom
        login_data = {
            "aUn": username,
            "aUp": password
        }

        try:
            print(f"Pokušavam login na: {login_url}")
            print(f"Korisnik: {username}")

            response = self.session.post(
                login_url,
                json=login_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            print(f"Status kod: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"Login odgovor: {json.dumps(result, indent=2)}")

                    if 'data' in result and len(result['data']) > 0:
                        data = result['data'][0]
                        if 'attributes' in data:
                            self.session_token = data['attributes'].get('token')
                            print(f"Uspešan login! Token: {self.session_token}")
                            return True
                except json.JSONDecodeError as e:
                    print(f"Greška pri parsiranju JSON odgovora: {e}")
                    print(f"Raw odgovor: {response.text}")
            else:
                print(f"Login neuspešan. Status: {response.status_code}")
                print(f"Odgovor: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"Greška pri login zahtev: {e}")

        return False
    
    def get_all_warehouses(self) -> List[Dict]:
        """
        Preuzima listu svih magacina
        
        Returns:
            Lista magacina
        """
        url = f"{self.base_url}/api/v1/{self.alias}/lager.magacin.svi"
        
        try:
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result:
                    print(f"Pronađeno {len(result['data'])} magacina")
                    return result['data']
            else:
                print(f"Greška pri preuzimanju magacina: {response.status_code}")
                print(f"Odgovor: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Greška pri zahtev za magacine: {e}")
            
        return []
    
    def get_all_articles_with_stock(self, warehouse_id: Optional[str] = None,
                                   batch_size: int = 500) -> List[Dict]:
        """
        Preuzima sve artikle sa stanjem

        Args:
            warehouse_id: ID magacina (opciono)
            batch_size: Veličina batch-a za paginaciju

        Returns:
            Lista artikala sa stanjem
        """
        url = f"{self.base_url}/api/v1/{self.alias}/local.cache.artikal"
        all_articles = []
        start = 0

        while True:
            params = {
                'dStart': start,
                'dLength': batch_size,
                'output': 'jsonapi'
            }

            if warehouse_id:
                params['magacin'] = warehouse_id

            try:
                print(f"Preuzimam artikle {start}-{start + batch_size}...")
                response = self.session.get(url, params=params, timeout=60)

                if response.status_code == 200:
                    result = response.json()

                    if 'data' in result and result['data']:
                        articles = result['data']
                        all_articles.extend(articles)
                        print(f"Preuzeto {len(articles)} artikala")

                        # Proveri da li ima još podataka
                        meta = result.get('meta', {})
                        total = meta.get('total', 0)

                        if start + batch_size >= total:
                            break

                        start += batch_size
                        time.sleep(0.5)  # Kratka pauza između zahteva
                    else:
                        break
                else:
                    print(f"Greška pri preuzimanju artikala: {response.status_code}")
                    print(f"Odgovor: {response.text}")
                    break

            except requests.exceptions.RequestException as e:
                print(f"Greška pri zahtev za artikle: {e}")
                break

        print(f"Ukupno preuzeto {len(all_articles)} artikala")
        return all_articles

    def get_warehouse_stock_summary(self) -> List[Dict]:
        """
        Preuzima sažetak stanja po magacinima za sve artikle

        Returns:
            Lista stanja po magacinima
        """
        url = f"{self.base_url}/api/v1/{self.alias}/lager.magkol.svi"

        try:
            print("Preuzimam sažetak stanja po magacinima...")
            response = self.session.get(url, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if 'data' in result:
                    print(f"Pronađeno {len(result['data'])} stavki stanja")
                    return result['data']
            else:
                print(f"Greška pri preuzimanju stanja: {response.status_code}")
                print(f"Odgovor: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"Greška pri zahtev za stanje: {e}")

        return []

    def get_detailed_warehouse_stock(self, warehouse_id: str) -> List[Dict]:
        """
        Preuzima detaljno stanje artikala u određenom magacinu

        Args:
            warehouse_id: ID magacina

        Returns:
            Lista artikala sa stanjem u magacinu
        """
        url = f"{self.base_url}/api/v1/{self.alias}/lager.magacin.artikli"

        try:
            print(f"Preuzimam detaljno stanje za magacin {warehouse_id}...")
            response = self.session.get(url, params={'magacin': warehouse_id}, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if 'data' in result:
                    print(f"Pronađeno {len(result['data'])} artikala u magacinu")
                    return result['data']
            else:
                print(f"Greška pri preuzimanju stanja magacina: {response.status_code}")
                print(f"Odgovor: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"Greška pri zahtev za stanje magacina: {e}")

        return []
    
    def get_article_warehouse_stock(self, article_id: str) -> List[Dict]:
        """
        Preuzima stanje artikla po svim magacinima
        
        Args:
            article_id: ID artikla
            
        Returns:
            Lista stanja po magacinima
        """
        url = f"{self.base_url}/api/v1/{self.alias}/lager.artikal.magacin"
        
        try:
            response = self.session.get(url, params={'artikal': article_id}, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result:
                    return result['data']
            else:
                print(f"Greška pri preuzimanju stanja artikla {article_id}: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"Greška pri zahtev za stanje artikla {article_id}: {e}")
            
        return []

def save_to_csv(data: List[Dict], filename: str):
    """
    Čuva podatke u CSV fajl
    
    Args:
        data: Lista podataka za čuvanje
        filename: Ime fajla
    """
    if not data:
        print("Nema podataka za čuvanje")
        return
        
    # Pripremi podatke za CSV
    csv_data = []
    for item in data:
        if 'attributes' in item:
            row = {
                'id': item.get('id', ''),
                'type': item.get('type', ''),
                **item['attributes']
            }
            csv_data.append(row)
    
    if csv_data:
        # Dobij sve ključeve
        all_keys = set()
        for row in csv_data:
            all_keys.update(row.keys())
        
        # Sortiraj ključeve
        fieldnames = sorted(all_keys)
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
            
        print(f"Podaci sačuvani u: {filename}")

def main():
    """
    Glavna funkcija
    """
    # Konfiguracija
    BASE_URL = "https://portal.wings.rs"
    ALIAS = "grosstest"
    USERNAME = "aql"
    PASSWORD = "grossaql"

    print("=== Wings Portal API - Preuzimanje stanja po magacinima ===")
    print(f"URL: {BASE_URL}")
    print(f"Alias: {ALIAS}")
    print(f"Korisnik: {USERNAME}")
    print()

    # Inicijalizuj API klijent
    api = WingsPortalAPI(BASE_URL, ALIAS)

    # Logovaj se
    if not api.login(USERNAME, PASSWORD):
        print("Login neuspešan. Prekidam izvršavanje.")
        return

    print()

    # Kreiraj direktorijum za rezultate
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"wings_export_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)

    # Preuzmi magacine
    print("Preuzimam listu magacina...")
    warehouses = api.get_all_warehouses()
    if warehouses:
        save_to_csv(warehouses, os.path.join(output_dir, "magacini.csv"))

    # Preuzmi sve artikle sa stanjem
    print("\nPreuzimam sve artikle sa stanjem...")
    articles = api.get_all_articles_with_stock()
    if articles:
        save_to_csv(articles, os.path.join(output_dir, "artikli_stanje.csv"))

    # Preuzmi sažetak stanja po magacinima
    print("\nPreuzimam sažetak stanja po magacinima...")
    stock_summary = api.get_warehouse_stock_summary()
    if stock_summary:
        save_to_csv(stock_summary, os.path.join(output_dir, "stanje_po_magacinima.csv"))

    # Preuzmi detaljno stanje za svaki magacin
    if warehouses:
        for warehouse in warehouses:
            warehouse_id = warehouse.get('id')
            warehouse_name = warehouse.get('attributes', {}).get('naziv', 'Nepoznat')

            print(f"\nPreuzimam detaljno stanje za magacin: {warehouse_name} (ID: {warehouse_id})")
            detailed_stock = api.get_detailed_warehouse_stock(warehouse_id)
            if detailed_stock:
                filename = f"magacin_{warehouse_id}_detaljno_stanje.csv"
                save_to_csv(detailed_stock, os.path.join(output_dir, filename))

    print(f"\nIzvoz završen! Fajlovi su sačuvani u direktorijum: {output_dir}")
    print("\nSačuvani fajlovi:")
    print("- magacini.csv - Lista svih magacina")
    print("- artikli_stanje.csv - Svi artikli sa osnovnim stanjem")
    print("- stanje_po_magacinima.csv - Sažetak stanja po magacinima")
    if warehouses:
        for warehouse in warehouses:
            warehouse_id = warehouse.get('id')
            print(f"- magacin_{warehouse_id}_detaljno_stanje.csv - Detaljno stanje magacina")
    print(f"\nUkupno preuzeto {len(articles)} artikala iz {len(warehouses)} magacina.")

if __name__ == "__main__":
    main()
