<?php
/**
 * Wings Customer Importer Class
 * Handles the import process with batch processing and session management
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Wings_Customer_Importer {
    
    private $api;
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('wings_import_settings', array());
        $this->api = new Wings_API($this->settings);
    }
    
    /**
     * Start import process
     */
    public function start_import() {
        // Test API connection first
        $connection_test = $this->api->test_connection();
        if (is_wp_error($connection_test)) {
            return $connection_test;
        }
        
        // Create import session
        $session_id = uniqid('wings_import_');
        $session_data = array(
            'status' => 'starting',
            'total_customers' => 0,
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'current_batch' => 0,
            'batch_size' => intval($this->settings['batch_size'] ?? 25),
            'start_time' => time(),
            'log' => array(),
            'error_details' => array()
        );
        
        // Store session data
        set_transient('wings_import_session_' . $session_id, $session_data, 7200); // 2 hours
        
        // Log import start
        $this->log_message($session_id, 'info', 'Import session started', $session_data);
        
        return array('session_id' => $session_id);
    }
    
    /**
     * Process a batch of customers
     */
    public function process_batch($session_id) {
        $session_data = get_transient('wings_import_session_' . $session_id);
        
        if (!$session_data) {
            return new WP_Error('session_expired', __('Import session expired or invalid', 'wings-customer-import'));
        }
        
        $this->add_log($session_data, "🔄 Processing batch " . ($session_data['current_batch'] + 1));
        
        // First batch - get total count
        if ($session_data['current_batch'] === 0) {
            $total_count = $this->api->get_total_customer_count();
            if ($total_count > 0) {
                $session_data['total_customers'] = $total_count;
                $this->add_log($session_data, "📊 Found {$total_count} total customers");
            }
        }
        
        // Get batch of customers
        $start = $session_data['current_batch'] * $session_data['batch_size'];
        $batch_data = $this->api->get_customers($start, $session_data['batch_size']);
        
        if (is_wp_error($batch_data)) {
            $session_data['status'] = 'error';
            $this->add_log($session_data, "❌ API Error: " . $batch_data->get_error_message());
            $this->log_message($session_id, 'error', 'API Error', $batch_data->get_error_message());
            set_transient('wings_import_session_' . $session_id, $session_data, 7200);
            return $session_data;
        }
        
        if (!isset($batch_data['data'])) {
            $session_data['status'] = 'error';
            $this->add_log($session_data, "❌ Invalid API response format");
            set_transient('wings_import_session_' . $session_id, $session_data, 7200);
            return $session_data;
        }
        
        $customers = $batch_data['data'];
        $batch_count = count($customers);
        
        $this->add_log($session_data, "📥 Retrieved {$batch_count} customers");
        
        // Process each customer in the batch
        foreach ($customers as $customer) {
            $result = $this->process_single_customer($customer);
            
            if ($result['success']) {
                if ($result['action'] === 'created') {
                    $session_data['created']++;
                } else {
                    $session_data['updated']++;
                }
                $this->add_log($session_data, "✅ {$result['action']}: {$result['name']}");
            } else {
                $session_data['errors']++;
                $session_data['error_details'][] = $result['error'];
                $this->add_log($session_data, "❌ Error: {$result['error']}");
                $this->log_message($session_id, 'error', 'Customer processing error', $result['error']);
            }
            
            $session_data['processed']++;
        }
        
        $session_data['current_batch']++;
        
        // Check if we're done
        if ($batch_count < $session_data['batch_size'] || 
            ($session_data['total_customers'] > 0 && $session_data['processed'] >= $session_data['total_customers'])) {
            $session_data['status'] = 'completed';
            $elapsed = time() - $session_data['start_time'];
            $this->add_log($session_data, "🎉 Import completed in {$elapsed} seconds");
            $this->log_message($session_id, 'info', 'Import completed', array(
                'total_processed' => $session_data['processed'],
                'created' => $session_data['created'],
                'updated' => $session_data['updated'],
                'errors' => $session_data['errors'],
                'elapsed_time' => $elapsed
            ));
        } else {
            $session_data['status'] = 'processing';
        }
        
        // Update session data
        set_transient('wings_import_session_' . $session_id, $session_data, 7200);
        
        return $session_data;
    }
    
    /**
     * Get import status
     */
    public function get_status($session_id) {
        $session_data = get_transient('wings_import_session_' . $session_id);
        
        if (!$session_data) {
            return new WP_Error('session_expired', __('Import session expired or invalid', 'wings-customer-import'));
        }
        
        return $session_data;
    }
    
    /**
     * Process single customer
     */
    private function process_single_customer($customer) {
        if (!isset($customer['attributes'])) {
            return array('success' => false, 'error' => 'Missing customer attributes');
        }
        
        $attrs = $customer['attributes'];
        $wings_id = $customer['id'] ?? '';
        
        // Extract and clean data
        $customer_data = $this->extract_customer_data($attrs, $wings_id);
        
        if (!$customer_data) {
            return array('success' => false, 'error' => 'Could not extract valid customer data');
        }
        
        // Check if user already exists
        $existing_user = $this->find_existing_user($customer_data);
        
        if ($existing_user) {
            // Update existing user
            $result = $this->update_existing_user($existing_user, $customer_data);
            if ($result) {
                return array(
                    'success' => true, 
                    'action' => 'updated', 
                    'name' => $customer_data['display_name'],
                    'user_id' => $existing_user->ID
                );
            } else {
                return array('success' => false, 'error' => 'Failed to update user: ' . $customer_data['display_name']);
            }
        } else {
            // Create new user
            $user_id = $this->create_new_user($customer_data);
            if ($user_id && !is_wp_error($user_id)) {
                return array(
                    'success' => true, 
                    'action' => 'created', 
                    'name' => $customer_data['display_name'],
                    'user_id' => $user_id
                );
            } else {
                $error_msg = is_wp_error($user_id) ? $user_id->get_error_message() : 'Unknown error';
                return array('success' => false, 'error' => 'Failed to create user: ' . $customer_data['display_name'] . ' - ' . $error_msg);
            }
        }
    }
    
    /**
     * Extract customer data from Wings attributes
     */
    private function extract_customer_data($attrs, $wings_id) {
        // Basic validation
        $naziv = trim($attrs['naziv'] ?? '');
        if (empty($naziv)) {
            return false;
        }
        
        // Generate email if missing
        $email = trim($attrs['email'] ?? '');
        if (empty($email)) {
            $email = $this->generate_email_from_naziv($naziv);
        }
        
        // Parse address to extract postcode and city
        $address_info = $this->parse_address($attrs['mesto'] ?? '');
        
        // Parse contact name
        $contact_info = $this->parse_contact_name($attrs['kontakt'] ?? '');
        
        // Generate username from email
        $username = $this->generate_username($email, $wings_id);
        
        return array(
            // WordPress user fields
            'user_login' => $username,
            'user_email' => $email,
            'user_pass' => wp_generate_password(12, false),
            'display_name' => $naziv,
            'nickname' => $naziv,
            'first_name' => $contact_info['first_name'],
            'last_name' => $contact_info['last_name'],
            'role' => $this->settings['default_role'] ?? 'customer',
            
            // Billing information
            'billing_company' => $naziv,
            'billing_first_name' => $contact_info['first_name'],
            'billing_last_name' => $contact_info['last_name'],
            'billing_address_1' => trim($attrs['adresa'] ?? ''),
            'billing_city' => $address_info['city'],
            'billing_postcode' => $address_info['postcode'],
            'billing_country' => 'RS',
            'billing_phone' => $this->get_primary_phone($attrs),
            'billing_email' => $email,
            
            // Wings-specific metadata
            'wings_customer_id' => $wings_id,
            'wings_customer_code' => trim($attrs['sifra'] ?? ''),
            'wings_pib' => trim($attrs['pib'] ?? ''),
            'wings_mb' => trim($attrs['mb'] ?? ''),
            'wings_status' => trim($attrs['status'] ?? 'K'),
            'wings_sales_rep' => trim($attrs['komercijalista'] ?? ''),
            'wings_customer_class' => intval($attrs['klasa'] ?? 0),
            'wings_discount' => floatval($attrs['rabat'] ?? 0.0),
            'wings_credit_limit' => floatval($attrs['limit'] ?? 0.0),
            'wings_payment_terms' => intval($attrs['rokplacanja'] ?? 0),
            'wings_tolerance' => intval($attrs['tolerancija'] ?? 0),
            'wings_working_hours' => trim($attrs['radnovreme'] ?? ''),
            'wings_phone_2' => trim($attrs['fax'] ?? ''),
            'wings_phone_3' => trim($attrs['mobilni'] ?? ''),
            'wings_last_sync' => current_time('mysql')
        );
    }

    /**
     * Generate email from naziv (company name)
     */
    private function generate_email_from_naziv($naziv) {
        $clean_naziv = strtolower($naziv);
        $clean_naziv = preg_replace('/[^a-z0-9]/', '', $clean_naziv);
        $clean_naziv = substr($clean_naziv, 0, 30);

        if (empty($clean_naziv)) {
            $clean_naziv = 'customer' . uniqid();
        }

        return $clean_naziv . '@mail.com';
    }

    /**
     * Parse address in format "36000 Kraljevo"
     */
    private function parse_address($mesto) {
        $mesto = trim($mesto);

        if (preg_match('/^(\d{5})\s+(.+)$/', $mesto, $matches)) {
            return array(
                'postcode' => $matches[1],
                'city' => trim($matches[2])
            );
        }

        return array(
            'postcode' => '',
            'city' => $mesto
        );
    }

    /**
     * Parse contact name
     */
    private function parse_contact_name($kontakt) {
        $kontakt = trim($kontakt);

        if (empty($kontakt)) {
            return array('first_name' => '', 'last_name' => '');
        }

        $parts = explode(' ', $kontakt, 2);

        return array(
            'first_name' => $parts[0] ?? '',
            'last_name' => $parts[1] ?? ''
        );
    }

    /**
     * Get primary phone number
     */
    private function get_primary_phone($attrs) {
        $telefon = trim($attrs['telefon'] ?? '');
        $mobilni = trim($attrs['mobilni'] ?? '');

        return !empty($telefon) ? $telefon : $mobilni;
    }

    /**
     * Generate unique username
     */
    private function generate_username($email, $wings_id) {
        $base_username = strstr($email, '@', true);

        if (username_exists($base_username)) {
            $base_username = $base_username . '_' . $wings_id;
        }

        $counter = 1;
        $username = $base_username;
        while (username_exists($username)) {
            $username = $base_username . '_' . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Find existing user
     */
    private function find_existing_user($customer_data) {
        // Try by Wings customer ID first
        if (!empty($customer_data['wings_customer_id'])) {
            $users = get_users(array(
                'meta_key' => 'wings_customer_id',
                'meta_value' => $customer_data['wings_customer_id'],
                'number' => 1
            ));

            if (!empty($users)) {
                return $users[0];
            }
        }

        // Try by email
        $user = get_user_by('email', $customer_data['user_email']);
        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Create new WordPress user
     */
    private function create_new_user($customer_data) {
        $user_data = array(
            'user_login' => $customer_data['user_login'],
            'user_email' => $customer_data['user_email'],
            'user_pass' => $customer_data['user_pass'],
            'display_name' => $customer_data['display_name'],
            'nickname' => $customer_data['nickname'],
            'first_name' => $customer_data['first_name'],
            'last_name' => $customer_data['last_name'],
            'role' => $customer_data['role']
        );

        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            return $user_id;
        }

        $this->add_user_metadata($user_id, $customer_data);

        return $user_id;
    }

    /**
     * Update existing user
     */
    private function update_existing_user($user, $customer_data) {
        $user_data = array(
            'ID' => $user->ID,
            'display_name' => $customer_data['display_name'],
            'nickname' => $customer_data['nickname'],
            'first_name' => $customer_data['first_name'],
            'last_name' => $customer_data['last_name']
        );

        $result = wp_update_user($user_data);

        if (is_wp_error($result)) {
            return false;
        }

        $this->add_user_metadata($user->ID, $customer_data);

        return true;
    }

    /**
     * Add user metadata
     */
    private function add_user_metadata($user_id, $customer_data) {
        $billing_fields = array(
            'billing_company', 'billing_first_name', 'billing_last_name',
            'billing_address_1', 'billing_city', 'billing_postcode',
            'billing_country', 'billing_phone', 'billing_email'
        );

        foreach ($billing_fields as $field) {
            if (isset($customer_data[$field])) {
                update_user_meta($user_id, $field, $customer_data[$field]);
            }
        }

        $wings_fields = array(
            'wings_customer_id', 'wings_customer_code', 'wings_pib', 'wings_mb',
            'wings_status', 'wings_sales_rep', 'wings_customer_class',
            'wings_discount', 'wings_credit_limit', 'wings_payment_terms',
            'wings_tolerance', 'wings_working_hours', 'wings_phone_2',
            'wings_phone_3', 'wings_last_sync'
        );

        foreach ($wings_fields as $field) {
            if (isset($customer_data[$field]) && $customer_data[$field] !== '') {
                update_user_meta($user_id, $field, $customer_data[$field]);
            }
        }
    }

    /**
     * Add log entry to session
     */
    private function add_log(&$session_data, $message) {
        $session_data['log'][] = array(
            'time' => current_time('H:i:s'),
            'message' => $message
        );

        // Keep only last 50 log entries
        if (count($session_data['log']) > 50) {
            $session_data['log'] = array_slice($session_data['log'], -50);
        }
    }

    /**
     * Log message to database
     */
    private function log_message($session_id, $level, $message, $context = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'wings_import_logs';

        $wpdb->insert(
            $table_name,
            array(
                'session_id' => $session_id,
                'level' => $level,
                'message' => $message,
                'context' => is_array($context) ? wp_json_encode($context) : $context,
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%s')
        );
    }
}
