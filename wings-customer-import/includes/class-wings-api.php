<?php
/**
 * Wings API Class
 * Handles communication with Wings Portal API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Wings_API {
    
    private $api_url;
    private $api_alias;
    private $username;
    private $password;
    private $session_token = null;
    private $authenticated = false;
    
    /**
     * Constructor
     */
    public function __construct($settings = null) {
        if (!$settings) {
            $settings = get_option('wings_import_settings', array());
        }
        
        $this->api_url = trailingslashit($settings['api_url'] ?? 'https://portal.wings.rs/api/v1/');
        $this->api_alias = $settings['api_alias'] ?? 'grosstest';
        $this->username = $settings['api_username'] ?? 'aql';
        $this->password = $settings['api_password'] ?? 'grossaql';
    }
    
    /**
     * Test API connection
     */
    public function test_connection() {
        // Reset authentication state for fresh test
        $this->authenticated = false;
        $this->session_token = null;

        $auth_result = $this->authenticate();

        if (is_wp_error($auth_result)) {
            // Add more detailed error information
            $error_data = array(
                'api_url' => $this->api_url,
                'alias' => $this->api_alias,
                'login_url' => $this->api_url . $this->api_alias . '/system.user.log',
                'error_code' => $auth_result->get_error_code(),
                'error_message' => $auth_result->get_error_message()
            );

            return new WP_Error(
                $auth_result->get_error_code(),
                $auth_result->get_error_message() . ' | Debug: ' . wp_json_encode($error_data)
            );
        }

        return array(
            'success' => true,
            'message' => __('Connection successful!', 'wings-customer-import'),
            'api_url' => $this->api_url,
            'alias' => $this->api_alias,
            'login_url' => $this->api_url . $this->api_alias . '/system.user.log'
        );
    }
    
    /**
     * Authenticate with Wings Portal API
     */
    public function authenticate() {
        if ($this->authenticated) {
            return true;
        }

        $login_url = $this->api_url . $this->api_alias . '/system.user.log';

        $response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $this->username,
                'aUp' => $this->password
            ),
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded'
            )
        ));

        if (is_wp_error($response)) {
            return new WP_Error(
                'connection_failed',
                sprintf(
                    __('Failed to connect to Wings API at %s: %s', 'wings-customer-import'),
                    $login_url,
                    $response->get_error_message()
                )
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code === 200) {
            // Extract session cookie
            $cookies = wp_remote_retrieve_cookies($response);
            foreach ($cookies as $cookie) {
                if ($cookie->name === 'PHPSESSID') {
                    $this->session_token = $cookie->value;
                    $this->authenticated = true;
                    return true;
                }
            }

            // No session cookie found
            return new WP_Error(
                'no_session_cookie',
                sprintf(
                    __('No session cookie received. Response code: %d, Body: %s', 'wings-customer-import'),
                    $response_code,
                    substr($response_body, 0, 200)
                )
            );
        }

        return new WP_Error(
            'auth_failed',
            sprintf(
                __('Authentication failed. Response code: %d, Body: %s', 'wings-customer-import'),
                $response_code,
                substr($response_body, 0, 200)
            )
        );
    }
    
    /**
     * Make authenticated API request
     */
    public function make_request($endpoint, $params = array(), $method = 'GET') {
        if (!$this->authenticated) {
            $auth_result = $this->authenticate();
            if (is_wp_error($auth_result)) {
                return $auth_result;
            }
        }
        
        $url = $this->api_url . $this->api_alias . '/' . $endpoint;
        
        $args = array(
            'timeout' => 60,
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $this->session_token
            )
        );
        
        if ($method === 'GET' && !empty($params)) {
            $url = add_query_arg($params, $url);
            $response = wp_remote_get($url, $args);
        } else {
            $args['method'] = 'POST';
            $args['body'] = $params;
            $response = wp_remote_post($url, $args);
        }
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'json_error',
                __('Invalid JSON response: ', 'wings-customer-import') . json_last_error_msg()
            );
        }
        
        return $data;
    }
    
    /**
     * Get customers from Wings API
     */
    public function get_customers($start = 0, $length = 25) {
        $params = array(
            'dStart' => $start,
            'dLength' => $length,
            'output' => 'jsonapi'
        );
        
        return $this->make_request('local.kupac.svi', $params, 'GET');
    }
    
    /**
     * Get total customer count
     */
    public function get_total_customer_count() {
        $response = $this->make_request('local.kupac.svi', array(
            'dStart' => 0,
            'dLength' => 1,
            'output' => 'jsonapi'
        ), 'GET');
        
        if (is_wp_error($response) || !isset($response['meta']['total'])) {
            return 0;
        }
        
        return intval($response['meta']['total'] ?? 0);
    }
    
    /**
     * Create customer in Wings
     */
    public function create_customer($customer_data) {
        $params = array(
            'naziv' => $customer_data['naziv'] ?? '',
            'adresa' => $customer_data['adresa'] ?? '',
            'mesto' => $customer_data['mesto'] ?? '',
            'kontakt' => $customer_data['kontakt'] ?? '',
            'telefon' => $customer_data['telefon'] ?? '',
            'email' => $customer_data['email'] ?? '',
            'pib' => $customer_data['pib'] ?? '',
            'mb' => $customer_data['mb'] ?? '',
            'komercijalista' => $customer_data['komercijalista'] ?? '1',
            'klasa' => $customer_data['klasa'] ?? '1'
        );
        
        return $this->make_request('local.kupac.nov', $params, 'POST');
    }
    
    /**
     * Get customer by ID
     */
    public function get_customer_by_id($customer_id) {
        $params = array(
            'id' => $customer_id
        );
        
        return $this->make_request('local.kupac.info', $params, 'GET');
    }
    
    /**
     * Get API status
     */
    public function get_api_status() {
        return array(
            'authenticated' => $this->authenticated,
            'api_url' => $this->api_url,
            'alias' => $this->api_alias,
            'session_token' => $this->session_token ? substr($this->session_token, 0, 10) . '...' : null
        );
    }
}
