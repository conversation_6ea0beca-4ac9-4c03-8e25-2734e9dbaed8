<?php
/**
 * Wings Import Admin Class
 * Handles admin interface and pages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Wings_Import_Admin {
    
    /**
     * Display main dashboard
     */
    public function display_dashboard() {
        $settings = get_option('wings_import_settings', array());
        ?>
        <div class="wrap">
            <h1><?php _e('Wings Customer Import Dashboard', 'wings-customer-import'); ?></h1>
            
            <?php if (get_transient('wings_import_activated')): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Wings Customer Import plugin activated successfully!', 'wings-customer-import'); ?></p>
                </div>
                <?php delete_transient('wings_import_activated'); ?>
            <?php endif; ?>
            
            <div id="wings-import-dashboard">
                <!-- Configuration Section -->
                <div class="wings-config-section">
                    <h2><?php _e('Configuration', 'wings-customer-import'); ?></h2>
                    <div class="wings-config-grid">
                        <div class="wings-config-item">
                            <label><?php _e('API URL:', 'wings-customer-import'); ?></label>
                            <input type="text" id="api_url" value="<?php echo esc_attr($settings['api_url'] ?? ''); ?>" readonly>
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Alias:', 'wings-customer-import'); ?></label>
                            <input type="text" id="api_alias" value="<?php echo esc_attr($settings['api_alias'] ?? ''); ?>">
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Username:', 'wings-customer-import'); ?></label>
                            <input type="text" id="api_username" value="<?php echo esc_attr($settings['api_username'] ?? ''); ?>">
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Password:', 'wings-customer-import'); ?></label>
                            <input type="password" id="api_password" value="<?php echo esc_attr($settings['api_password'] ?? ''); ?>">
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Batch Size:', 'wings-customer-import'); ?></label>
                            <input type="number" id="batch_size" value="<?php echo esc_attr($settings['batch_size'] ?? 25); ?>" min="10" max="100">
                        </div>
                    </div>
                    
                    <div class="wings-actions">
                        <button id="test-connection" class="button button-secondary">
                            <?php _e('Test Connection', 'wings-customer-import'); ?>
                        </button>
                        <button id="start-import" class="button button-primary">
                            <?php _e('Start Import', 'wings-customer-import'); ?>
                        </button>
                        <button id="stop-import" class="button button-secondary" style="display: none;">
                            <?php _e('Stop Import', 'wings-customer-import'); ?>
                        </button>
                    </div>
                </div>
                
                <!-- Progress Section -->
                <div id="progress-section" class="wings-progress-section">
                    <h2><?php _e('Import Progress', 'wings-customer-import'); ?></h2>
                    
                    <div class="wings-status">
                        <span class="status-indicator" id="status-indicator"></span>
                        <span id="status-text"><?php _e('Ready to start', 'wings-customer-import'); ?></span>
                    </div>
                    
                    <div class="wings-progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div class="wings-progress-text">
                        <span id="progress-text">0 / 0 <?php _e('customers processed', 'wings-customer-import'); ?> (0%)</span>
                    </div>
                    
                    <div class="wings-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label"><?php _e('Total Found', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-processed">0</div>
                            <div class="stat-label"><?php _e('Processed', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-created">0</div>
                            <div class="stat-label"><?php _e('Created', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-updated">0</div>
                            <div class="stat-label"><?php _e('Updated', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-errors">0</div>
                            <div class="stat-label"><?php _e('Errors', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-time">0s</div>
                            <div class="stat-label"><?php _e('Elapsed Time', 'wings-customer-import'); ?></div>
                        </div>
                    </div>
                    
                    <div id="error-details" class="wings-error-details" style="display: none;">
                        <h3><?php _e('Error Details', 'wings-customer-import'); ?></h3>
                        <ul id="error-list"></ul>
                    </div>
                    
                    <div class="wings-log-section">
                        <h3><?php _e('Live Log', 'wings-customer-import'); ?></h3>
                        <div class="wings-log-container" id="log-container">
                            <div class="log-entry"><?php _e('Ready to start import...', 'wings-customer-import'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Display settings page
     */
    public function display_settings() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        $settings = get_option('wings_import_settings', array());
        ?>
        <div class="wrap">
            <h1><?php _e('Wings Import Settings', 'wings-customer-import'); ?></h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('wings_import_settings', 'wings_import_settings_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('API URL', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="url" name="api_url" value="<?php echo esc_attr($settings['api_url'] ?? 'https://portal.wings.rs/api/v1/'); ?>" class="regular-text" />
                            <p class="description"><?php _e('Wings Portal API base URL', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Alias', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="text" name="api_alias" value="<?php echo esc_attr($settings['api_alias'] ?? ''); ?>" class="regular-text" />
                            <p class="description"><?php _e('Your Wings Portal alias', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Username', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="text" name="api_username" value="<?php echo esc_attr($settings['api_username'] ?? ''); ?>" class="regular-text" />
                            <p class="description"><?php _e('Wings Portal API username', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Password', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="password" name="api_password" value="<?php echo esc_attr($settings['api_password'] ?? ''); ?>" class="regular-text" />
                            <p class="description"><?php _e('Wings Portal API password', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Batch Size', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="number" name="batch_size" value="<?php echo esc_attr($settings['batch_size'] ?? 25); ?>" min="10" max="100" />
                            <p class="description"><?php _e('Number of customers to process per batch (recommended: 25)', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Default User Role', 'wings-customer-import'); ?></th>
                        <td>
                            <select name="default_role">
                                <?php
                                $roles = wp_roles()->get_names();
                                $selected_role = $settings['default_role'] ?? 'Professionals';
                                foreach ($roles as $role_key => $role_name) {
                                    echo '<option value="' . esc_attr($role_key) . '"' . selected($selected_role, $role_key, false) . '>' . esc_html($role_name) . '</option>';
                                }
                                ?>
                            </select>
                            <p class="description"><?php _e('Default role for imported users', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
            
            <div class="wings-test-section">
                <h2><?php _e('Connection Test', 'wings-customer-import'); ?></h2>
                <button id="test-api-connection" class="button button-secondary">
                    <?php _e('Test API Connection', 'wings-customer-import'); ?>
                </button>
                <div id="test-results" style="margin-top: 10px;"></div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Display logs page
     */
    public function display_logs() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wings_import_logs';
        $per_page = 50;
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($page - 1) * $per_page;
        
        $total_logs = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        $logs = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT %d OFFSET %d",
            $per_page,
            $offset
        ));
        
        ?>
        <div class="wrap">
            <h1><?php _e('Import Logs', 'wings-customer-import'); ?></h1>
            
            <div class="tablenav top">
                <div class="alignleft actions">
                    <button id="clear-logs" class="button button-secondary">
                        <?php _e('Clear All Logs', 'wings-customer-import'); ?>
                    </button>
                </div>
                <?php
                $total_pages = ceil($total_logs / $per_page);
                if ($total_pages > 1) {
                    echo paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo;'),
                        'next_text' => __('&raquo;'),
                        'total' => $total_pages,
                        'current' => $page
                    ));
                }
                ?>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Date/Time', 'wings-customer-import'); ?></th>
                        <th><?php _e('Session ID', 'wings-customer-import'); ?></th>
                        <th><?php _e('Level', 'wings-customer-import'); ?></th>
                        <th><?php _e('Message', 'wings-customer-import'); ?></th>
                        <th><?php _e('Context', 'wings-customer-import'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($logs)): ?>
                        <tr>
                            <td colspan="5"><?php _e('No logs found.', 'wings-customer-import'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo esc_html($log->created_at); ?></td>
                                <td><?php echo esc_html(substr($log->session_id, -8)); ?></td>
                                <td>
                                    <span class="log-level log-level-<?php echo esc_attr($log->level); ?>">
                                        <?php echo esc_html(ucfirst($log->level)); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html($log->message); ?></td>
                                <td>
                                    <?php if (!empty($log->context)): ?>
                                        <details>
                                            <summary><?php _e('View Context', 'wings-customer-import'); ?></summary>
                                            <pre><?php echo esc_html($log->context); ?></pre>
                                        </details>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['wings_import_settings_nonce'], 'wings_import_settings')) {
            wp_die(__('Security check failed', 'wings-customer-import'));
        }
        
        $settings = array(
            'api_url' => sanitize_url($_POST['api_url']),
            'api_alias' => sanitize_text_field($_POST['api_alias']),
            'api_username' => sanitize_text_field($_POST['api_username']),
            'api_password' => sanitize_text_field($_POST['api_password']),
            'batch_size' => intval($_POST['batch_size']),
            'default_role' => sanitize_text_field($_POST['default_role'])
        );
        
        update_option('wings_import_settings', $settings);
        
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'wings-customer-import') . '</p></div>';
    }
}
