<?php
/**
 * Plugin Name: Wings Customer Import
 * Plugin URI: https://yoursite.com
 * Description: Import customers from Wings Portal API to WordPress users with real-time progress tracking. Handles 2000+ customers efficiently with batch processing.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yoursite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wings-customer-import
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WINGS_IMPORT_VERSION', '1.0.0');
define('WINGS_IMPORT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WINGS_IMPORT_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WINGS_IMPORT_PLUGIN_FILE', __FILE__);

/**
 * Main Wings Customer Import Plugin Class
 */
class Wings_Customer_Import_Plugin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('Wings_Customer_Import_Plugin', 'uninstall'));
        
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // AJAX hooks
        add_action('wp_ajax_wings_import_start', array($this, 'ajax_import_start'));
        add_action('wp_ajax_wings_import_batch', array($this, 'ajax_import_batch'));
        add_action('wp_ajax_wings_import_status', array($this, 'ajax_import_status'));
        add_action('wp_ajax_wings_test_connection', array($this, 'ajax_test_connection'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once WINGS_IMPORT_PLUGIN_PATH . 'includes/class-wings-api.php';
        require_once WINGS_IMPORT_PLUGIN_PATH . 'includes/class-wings-importer.php';
        require_once WINGS_IMPORT_PLUGIN_PATH . 'admin/class-wings-admin.php';
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create plugin options
        add_option('wings_import_version', WINGS_IMPORT_VERSION);
        add_option('wings_import_settings', array(
            'api_url' => 'https://portal.wings.rs/api/v1/',
            'api_alias' => 'grosstest',
            'api_username' => 'aql',
            'api_password' => 'grossaql',
            'batch_size' => 25,
            'default_role' => 'Professionals'
        ));
        
        // Create log table
        $this->create_log_table();
        
        // Set activation flag
        set_transient('wings_import_activated', true, 30);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up transients
        delete_transient('wings_import_activated');
        
        // Clear any ongoing import sessions
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'wings_import_session_%'");
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove plugin options
        delete_option('wings_import_version');
        delete_option('wings_import_settings');
        
        // Drop log table
        global $wpdb;
        $table_name = $wpdb->prefix . 'wings_import_logs';
        $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
        
        // Clean up user meta
        $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'wings_%'");
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'wings-customer-import',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Wings Import', 'wings-customer-import'),
            __('Wings Import', 'wings-customer-import'),
            'manage_options',
            'wings-import',
            array($this, 'admin_page'),
            'dashicons-download',
            30
        );
        
        add_submenu_page(
            'wings-import',
            __('Import Dashboard', 'wings-customer-import'),
            __('Import Dashboard', 'wings-customer-import'),
            'manage_options',
            'wings-import',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'wings-import',
            __('Settings', 'wings-customer-import'),
            __('Settings', 'wings-customer-import'),
            'manage_options',
            'wings-import-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'wings-import',
            __('Import Logs', 'wings-customer-import'),
            __('Import Logs', 'wings-customer-import'),
            'manage_options',
            'wings-import-logs',
            array($this, 'logs_page')
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'wings-import') === false) {
            return;
        }
        
        wp_enqueue_script(
            'wings-import-admin',
            WINGS_IMPORT_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WINGS_IMPORT_VERSION,
            true
        );
        
        wp_enqueue_style(
            'wings-import-admin',
            WINGS_IMPORT_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WINGS_IMPORT_VERSION
        );
        
        wp_localize_script('wings-import-admin', 'wingsImport', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wings_import_nonce'),
            'strings' => array(
                'starting' => __('Starting import...', 'wings-customer-import'),
                'processing' => __('Processing customers...', 'wings-customer-import'),
                'completed' => __('Import completed!', 'wings-customer-import'),
                'error' => __('Import failed', 'wings-customer-import'),
                'confirm_stop' => __('Are you sure you want to stop the import?', 'wings-customer-import')
            )
        ));
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        $admin = new Wings_Import_Admin();
        $admin->display_dashboard();
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        $admin = new Wings_Import_Admin();
        $admin->display_settings();
    }
    
    /**
     * Logs page
     */
    public function logs_page() {
        $admin = new Wings_Import_Admin();
        $admin->display_logs();
    }
    
    /**
     * AJAX: Start import
     */
    public function ajax_import_start() {
        check_ajax_referer('wings_import_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'wings-customer-import'));
        }
        
        $importer = new Wings_Customer_Importer();
        $result = $importer->start_import();
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success($result);
    }
    
    /**
     * AJAX: Process batch
     */
    public function ajax_import_batch() {
        check_ajax_referer('wings_import_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'wings-customer-import'));
        }
        
        $session_id = sanitize_text_field($_POST['session_id']);
        $importer = new Wings_Customer_Importer();
        $result = $importer->process_batch($session_id);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success($result);
    }
    
    /**
     * AJAX: Get status
     */
    public function ajax_import_status() {
        check_ajax_referer('wings_import_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'wings-customer-import'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);
        $importer = new Wings_Customer_Importer();
        $result = $importer->get_status($session_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success($result);
    }

    /**
     * AJAX: Test connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('wings_import_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'wings-customer-import'));
        }

        // Get current settings or use posted values
        $settings = get_option('wings_import_settings', array());

        // Override with any posted values for testing
        if (isset($_POST['api_url'])) {
            $settings['api_url'] = sanitize_url($_POST['api_url']);
        }
        if (isset($_POST['api_alias'])) {
            $settings['api_alias'] = sanitize_text_field($_POST['api_alias']);
        }
        if (isset($_POST['api_username'])) {
            $settings['api_username'] = sanitize_text_field($_POST['api_username']);
        }
        if (isset($_POST['api_password'])) {
            $settings['api_password'] = sanitize_text_field($_POST['api_password']);
        }

        $api = new Wings_API($settings);
        $result = $api->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success($result);
    }
    
    /**
     * Create log table
     */
    private function create_log_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wings_import_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            session_id varchar(50) NOT NULL,
            level varchar(20) NOT NULL DEFAULT 'info',
            message text NOT NULL,
            context longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY session_id (session_id),
            KEY level (level),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize the plugin
Wings_Customer_Import_Plugin::get_instance();
