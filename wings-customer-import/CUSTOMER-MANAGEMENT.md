# Wings Customer Management

## 🎯 Overview

The Wings Customer Import plugin now includes a comprehensive customer management interface that allows you to view, edit, and manage all imported Wings customers directly from your WordPress admin.

## 📍 Access

Navigate to: **WordPress Admin → Wings Import → Manage Customers**

## ✨ Features

### 🔍 **Customer Search & Filtering**
- **Real-time search** across company names, emails, and usernames
- **Instant filtering** with search-as-you-type functionality
- **Clear search** button to reset filters

### 📊 **Customer Table View**
- **Paginated display** (20 customers per page)
- **Sortable columns** for easy organization
- **Status indicators** (Active/Inactive badges)
- **Bulk selection** with checkboxes

### ✏️ **Edit Customer Information**
- **Modal-based editing** for seamless user experience
- **Complete customer data** editing including:
  - Company name and contact information
  - Email and phone details
  - Billing address information
  - Wings-specific data (PIB, status, customer ID)
  - WordPress user role assignment

### 🗑️ **Customer Deletion**
- **Safe deletion** with confirmation dialog
- **Wings customer verification** (only deletes actual Wings customers)
- **Permanent removal** from WordPress database

### 📤 **Data Export**
- **CSV export** of current customer list
- **Complete data export** including all customer fields
- **Date-stamped filenames** for organization

## 🎨 **Interface Layout**

```
🏢 Manage Wings Customers

Search & Actions Bar
┌─────────────────────────────────────────────────────────┐
│ [Search customers...] [Search] [Clear] | [Refresh] [Export CSV] │
└─────────────────────────────────────────────────────────┘

Customer Table
┌─────────────────────────────────────────────────────────┐
│ ☐ | Company Name | Contact | Email | Phone | City | Wings ID | Status | Actions │
│ ☐ | ADŽIĆ OPTIKA | John Doe | john@... | +381... | Belgrade | 12345 | Active | [Edit] [Delete] │
│ ☐ | NOVA KOMPANIJA | Jane Smith | jane@... | +381... | Novi Sad | 12346 | Active | [Edit] [Delete] │
└─────────────────────────────────────────────────────────┘

Pagination
┌─────────────────────────────────────────────────────────┐
│ Showing 1 to 20 of 2,000 customers | [Previous] [1] [2] [3] [Next] │
└─────────────────────────────────────────────────────────┘
```

## 🛠️ **Customer Data Fields**

### **WordPress User Fields**
- **Company Name** (`display_name`) - Main company identifier
- **Email** (`user_email`) - Primary contact email
- **First Name** (`first_name`) - Contact person's first name
- **Last Name** (`last_name`) - Contact person's last name
- **User Role** (`role`) - WordPress user role (default: Professionals)

### **Billing Information**
- **Phone** (`billing_phone`) - Primary phone number
- **Address** (`billing_address_1`) - Street address
- **City** (`billing_city`) - City name
- **Postcode** (`billing_postcode`) - Postal code

### **Wings-Specific Data**
- **Wings Customer ID** (`wings_customer_id`) - Unique Wings identifier (read-only)
- **PIB** (`wings_pib`) - Tax identification number
- **Status** (`wings_status`) - Customer status (K=Active, N=Inactive)
- **Last Sync** (`wings_last_sync`) - Last synchronization timestamp

## 🔧 **Usage Instructions**

### **Viewing Customers**
1. Go to **Wings Import → Manage Customers**
2. Browse the paginated customer list
3. Use search to find specific customers
4. View customer details in the table

### **Editing a Customer**
1. Click **"Edit"** button next to any customer
2. Modify fields in the popup modal
3. Click **"Save Changes"** to update
4. Changes are immediately reflected in the table

### **Deleting a Customer**
1. Click **"Delete"** button next to any customer
2. Confirm deletion in the popup dialog
3. Customer is permanently removed from WordPress

### **Searching Customers**
1. Type in the search box (company name, email, or username)
2. Click **"Search"** or press Enter
3. Use **"Clear"** to reset search results

### **Exporting Data**
1. Click **"Export CSV"** button
2. CSV file downloads automatically
3. File includes all visible customer data

## 📱 **Responsive Design**

The interface is fully responsive and works on:
- **Desktop** - Full table view with all columns
- **Tablet** - Condensed view with essential columns
- **Mobile** - Stacked layout with touch-friendly controls

## 🔒 **Security Features**

- **Admin-only access** - Requires `manage_options` capability
- **Nonce verification** - All AJAX requests are secured
- **Input sanitization** - All data is properly sanitized
- **Wings customer verification** - Only Wings customers can be deleted

## 🎯 **Performance Optimizations**

- **Paginated loading** - Only loads 20 customers at a time
- **AJAX-based operations** - No page reloads required
- **Efficient queries** - Optimized database queries
- **Client-side caching** - Reduces server requests

## 🚀 **Advanced Features**

### **Bulk Operations** (Future Enhancement)
- Select multiple customers with checkboxes
- Bulk edit common fields
- Bulk status changes
- Bulk export of selected customers

### **Advanced Filtering** (Future Enhancement)
- Filter by customer status
- Filter by city or region
- Filter by last sync date
- Filter by user role

### **Customer Analytics** (Future Enhancement)
- Customer statistics dashboard
- Import history tracking
- Data quality reports
- Sync status monitoring

## 🛠️ **Technical Implementation**

### **Backend (PHP)**
- `Wings_Import_Admin::display_manage_customers()` - Main interface
- `Wings_Import_Admin::get_customers_data()` - Data retrieval
- `Wings_Import_Admin::update_customer()` - Customer updates
- `Wings_Import_Admin::delete_customer()` - Customer deletion

### **Frontend (JavaScript)**
- AJAX-based data loading and operations
- Modal-based editing interface
- Real-time search and pagination
- CSV export functionality

### **Database**
- Uses WordPress `users` and `usermeta` tables
- Queries only users with `wings_customer_id` meta
- Efficient pagination with `WP_User_Query`

## 📋 **Example Use Cases**

### **Scenario 1: Update Customer Contact**
A customer changes their email address:
1. Search for the customer by company name
2. Click "Edit" to open the modal
3. Update the email field
4. Save changes - customer can now log in with new email

### **Scenario 2: Bulk Data Export**
Need to send customer list to sales team:
1. Use search to filter customers by city
2. Click "Export CSV" to download filtered data
3. Share CSV file with team

### **Scenario 3: Clean Up Inactive Customers**
Remove customers that are no longer active:
1. Search for customers with "Inactive" status
2. Review each customer individually
3. Delete customers that are no longer needed
4. Keep database clean and organized

## 🎉 **Benefits**

- **Complete Control** - Full CRUD operations on customer data
- **User-Friendly** - Intuitive interface with modern design
- **Efficient** - Fast operations with minimal server load
- **Secure** - Proper authentication and data validation
- **Flexible** - Easy to extend with additional features

The customer management interface provides a complete solution for managing your imported Wings customers directly within WordPress! 🚀
