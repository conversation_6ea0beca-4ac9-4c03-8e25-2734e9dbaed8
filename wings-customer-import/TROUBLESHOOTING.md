# Wings Customer Import - Troubleshooting Guide

## 🚨 Connection Test Error

If you're getting "❌ Connection test error" with your credentials:

```
API URL: https://portal.wings.rs/api/v1/
Alias: grosstest
Username: aql
Password: grossaql
```

## 🔍 Debugging Steps

### Step 1: Run Debug Script

1. **Access the debug script** in your browser:
   ```
   https://yoursite.com/wp-content/plugins/wings-customer-import/debug-connection.php
   ```

2. **Review the detailed output** to identify the specific issue

### Step 2: Manual Verification

1. **Test Wings Portal web access**:
   - Go to `https://portal.wings.rs`
   - Try logging in with the same credentials
   - Verify the alias `grosstest` is correct

2. **Check the exact login URL**:
   ```
   https://portal.wings.rs/api/v1/grosstest/system.user.log
   ```

### Step 3: Common Issues & Solutions

#### Issue 1: "Failed to connect to Wings API"
**Cause**: Network/firewall blocking external requests

**Solutions**:
- Contact your hosting provider to allow external HTTP requests
- Check if your server has firewall restrictions
- Verify DNS resolution for `portal.wings.rs`

#### Issue 2: "Authentication failed"
**Cause**: Incorrect credentials or account issues

**Solutions**:
- Double-check username, password, and alias
- Verify account is active in Wings Portal
- Contact Wings Portal administrator

#### Issue 3: "No session cookie received"
**Cause**: Wings Portal not returning session cookie

**Solutions**:
- Check if API access is enabled for your account
- Verify Wings Portal API is functioning
- Contact Wings Portal support

#### Issue 4: HTTP 404 Error
**Cause**: Incorrect API endpoint or alias

**Solutions**:
- Verify the alias `grosstest` is correct
- Check if Wings Portal API structure has changed
- Try different API URL format

### Step 4: Test with Different Credentials

If you have access to different Wings Portal credentials, try testing with those to isolate the issue.

## 🛠️ Manual Connection Test

You can also test the connection manually using these methods:

### Method 1: Browser Test
1. Open browser developer tools (F12)
2. Go to Network tab
3. Visit: `https://portal.wings.rs/api/v1/grosstest/system.user.log`
4. Check the response

### Method 2: cURL Test
```bash
curl -X POST "https://portal.wings.rs/api/v1/grosstest/system.user.log" \
     -d "aUn=aql&aUp=grossaql" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -v
```

### Method 3: Postman/Insomnia
- **URL**: `https://portal.wings.rs/api/v1/grosstest/system.user.log`
- **Method**: POST
- **Body**: `aUn=aql&aUp=grossaql`
- **Content-Type**: `application/x-www-form-urlencoded`

## 📋 Information to Collect

When reporting the issue, please provide:

1. **Debug script output** (full output from debug-connection.php)
2. **WordPress version** and hosting provider
3. **PHP version** and server configuration
4. **Any server error logs** related to the connection attempt
5. **Wings Portal account status** (confirmed with Wings admin)

## 🔧 Plugin Fixes Applied

I've already implemented these fixes in the plugin:

1. **Added missing AJAX handler** for connection testing
2. **Enhanced error reporting** with detailed debug information
3. **Improved form data handling** to use current form values
4. **Added connection reset** for fresh testing
5. **Better error messages** with specific failure reasons

## 📞 Next Steps

1. **Run the debug script** first to get detailed error information
2. **Check with Wings Portal admin** to verify:
   - Account is active and has API access
   - Credentials are correct
   - API endpoint hasn't changed
3. **Test network connectivity** from your server to Wings Portal
4. **Contact hosting provider** if network issues are suspected

## 🎯 Expected Successful Response

When the connection works, you should see:

```
✅ Connection successful!
API URL: https://portal.wings.rs/api/v1/
Alias: grosstest
Login URL: https://portal.wings.rs/api/v1/grosstest/system.user.log
```

And in the debug script:
- HTTP 200 response
- PHPSESSID cookie received
- No error messages in response body

## 🚀 Alternative Testing

If the main credentials don't work, you can also test with the original credentials from your working script:

```
API URL: https://portal.wings.rs/api/v1/
Alias: grosstest  
Username: aql
Password: grossaql
```

The issue might be:
1. **Server-side restrictions** on your WordPress hosting
2. **Wings Portal API changes** or maintenance
3. **Account permissions** for API access
4. **Network connectivity** issues

Run the debug script to get the exact error details! 🔍
