/**
 * Wings Customer Import - Admin Styles
 */

/* Configuration Section */
.wings-config-section {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

.wings-config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.wings-config-item {
    display: flex;
    flex-direction: column;
}

.wings-config-item label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.wings-config-item input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.wings-config-item input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
    outline: none;
}

.wings-actions {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.wings-actions .button {
    margin: 0 5px;
}

/* Progress Section */
.wings-progress-section {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
    display: block !important; /* Always visible */
}

.wings-status {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
}

.status-indicator.status-starting {
    background: #ffc107;
}

.status-indicator.status-processing {
    background: #007cba;
    animation: pulse 1s infinite;
}

.status-indicator.status-completed {
    background: #28a745;
}

.status-indicator.status-error {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Progress Bar */
.wings-progress-bar {
    width: 100%;
    height: 30px;
    background: #e0e0e0;
    border-radius: 15px;
    overflow: hidden;
    margin: 15px 0;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
    border-radius: 15px;
}

.wings-progress-text {
    text-align: center;
    margin: 10px 0;
    font-weight: 500;
    color: #555;
}

/* Statistics Cards */
.wings-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #007cba;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #007cba;
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Error Details */
.wings-error-details {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
    border: 1px solid #f5c6cb;
}

.wings-error-details h3 {
    margin-top: 0;
    color: #721c24;
}

.wings-error-details ul {
    margin: 10px 0;
    padding-left: 20px;
}

/* Log Section */
.wings-log-section {
    margin-top: 30px;
}

.wings-log-container {
    height: 300px;
    overflow-y: auto;
    background: #1e1e1e;
    color: #00ff00;
    font-family: 'Courier New', Monaco, monospace;
    padding: 15px;
    border-radius: 5px;
    font-size: 12px;
    line-height: 1.4;
    border: 1px solid #333;
}

.log-entry {
    margin: 2px 0;
    word-wrap: break-word;
}

.log-time {
    color: #888;
    margin-right: 5px;
}

/* Log Levels */
.log-level {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.log-level-info {
    background: #d1ecf1;
    color: #0c5460;
}

.log-level-error {
    background: #f8d7da;
    color: #721c24;
}

.log-level-warning {
    background: #fff3cd;
    color: #856404;
}

.log-level-success {
    background: #d4edda;
    color: #155724;
}

/* Test Results */
#test-results {
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}

#test-results.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

#test-results.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wings-config-grid {
        grid-template-columns: 1fr;
    }
    
    .wings-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wings-actions .button {
        display: block;
        margin: 5px 0;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .wings-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 24px;
    }
}

/* Loading Animation */
.wings-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button States */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button.loading {
    position: relative;
    color: transparent;
}

.button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Settings Page Specific */
.wings-test-section {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

/* Customer Management Styles */
#wings-customers-manager {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

.wings-customers-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.wings-search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.wings-search-box input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 300px;
}

.wings-actions-box {
    display: flex;
    gap: 10px;
}

.wings-customers-table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

#wings-customers-table {
    width: 100%;
    border-collapse: collapse;
}

#wings-customers-table th,
#wings-customers-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

#wings-customers-table th {
    background: #f9f9f9;
    font-weight: 600;
    position: sticky;
    top: 0;
}

#wings-customers-table .check-column {
    width: 40px;
    text-align: center;
}

.customer-actions {
    display: flex;
    gap: 5px;
}

.customer-actions .button {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.2;
}

.loading-row {
    text-align: center;
    padding: 40px !important;
}

.wings-loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

/* Pagination */
.wings-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.pagination-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

#page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 3px;
    text-decoration: none;
    color: #333;
}

.page-number:hover {
    background: #f0f0f0;
}

.page-number.current {
    background: #007cba;
    color: #fff;
    border-color: #007cba;
}

/* Modal Styles */
.wings-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.wings-modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.wings-modal-small {
    max-width: 400px;
}

.wings-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.wings-modal-header h2 {
    margin: 0;
    color: #333;
}

.wings-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    line-height: 1;
}

.wings-modal-close:hover {
    color: #000;
}

.wings-modal-body {
    padding: 20px;
}

.wings-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.wings-form-group {
    display: flex;
    flex-direction: column;
}

.wings-form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.wings-form-group input,
.wings-form-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.wings-form-group input:focus,
.wings-form-group select:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
    outline: none;
}

.wings-form-group input[readonly] {
    background: #f9f9f9;
    color: #666;
}

.wings-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.wings-warning {
    color: #d63638;
    font-weight: 500;
}

.button-danger {
    background: #d63638 !important;
    border-color: #d63638 !important;
    color: #fff !important;
}

.button-danger:hover {
    background: #b32d2e !important;
    border-color: #b32d2e !important;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Responsive Design for Customer Management */
@media (max-width: 768px) {
    .wings-customers-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .wings-search-box {
        flex-direction: column;
        gap: 10px;
    }

    .wings-search-box input {
        width: 100%;
    }

    .wings-actions-box {
        justify-content: center;
    }

    .wings-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .wings-form-grid {
        grid-template-columns: 1fr;
    }

    .wings-modal-content {
        width: 95%;
        margin: 20px;
    }
}
