/**
 * Wings Customer Import - Admin JavaScript
 */

jQuery(document).ready(function($) {
    
    let importSession = null;
    let importInterval = null;
    let startTime = null;
    let isImporting = false;
    
    // DOM elements
    const $startBtn = $('#start-import');
    const $stopBtn = $('#stop-import');
    const $testBtn = $('#test-connection');
    const $progressSection = $('#progress-section');
    const $statusIndicator = $('#status-indicator');
    const $statusText = $('#status-text');
    const $progressFill = $('#progress-fill');
    const $progressText = $('#progress-text');
    const $logContainer = $('#log-container');
    const $errorDetails = $('#error-details');
    const $errorList = $('#error-list');
    
    // Stat elements
    const $statTotal = $('#stat-total');
    const $statProcessed = $('#stat-processed');
    const $statCreated = $('#stat-created');
    const $statUpdated = $('#stat-updated');
    const $statErrors = $('#stat-errors');
    const $statTime = $('#stat-time');
    
    // Event listeners
    $startBtn.on('click', startImport);
    $stopBtn.on('click', stopImport);
    $testBtn.on('click', testConnection);
    $('#test-api-connection').on('click', testApiConnection);
    $('#clear-logs').on('click', clearLogs);
    
    /**
     * Start import process
     */
    function startImport() {
        if (isImporting) return;
        
        // Update configuration from form
        updateConfiguration();
        
        isImporting = true;
        $startBtn.prop('disabled', true).hide();
        $stopBtn.show();
        $progressSection.show();
        
        updateStatus('starting', wingsImport.strings.starting);
        addLogEntry('🚀 Starting Wings API import...');
        
        startTime = Date.now();
        
        // Start import session
        $.post(wingsImport.ajaxUrl, {
            action: 'wings_import_start',
            nonce: wingsImport.nonce
        })
        .done(function(response) {
            if (response.success) {
                importSession = response.data.session_id;
                addLogEntry('✅ Import session created: ' + importSession);
                processNextBatch();
                
                // Start status polling
                importInterval = setInterval(updateProgress, 2000);
            } else {
                addLogEntry('❌ Failed to start import: ' + response.data);
                stopImport();
            }
        })
        .fail(function(xhr, status, error) {
            addLogEntry('❌ Network error: ' + error);
            stopImport();
        });
    }
    
    /**
     * Process next batch
     */
    function processNextBatch() {
        if (!isImporting || !importSession) return;
        
        $.post(wingsImport.ajaxUrl, {
            action: 'wings_import_batch',
            nonce: wingsImport.nonce,
            session_id: importSession
        })
        .done(function(response) {
            if (response.success) {
                const sessionData = response.data;
                updateUI(sessionData);
                
                if (sessionData.status === 'completed') {
                    addLogEntry('🎉 ' + wingsImport.strings.completed);
                    stopImport();
                } else if (sessionData.status === 'error') {
                    addLogEntry('❌ ' + wingsImport.strings.error);
                    stopImport();
                } else {
                    // Continue with next batch
                    setTimeout(processNextBatch, 1000);
                }
            } else {
                addLogEntry('❌ Batch processing error: ' + response.data);
                stopImport();
            }
        })
        .fail(function(xhr, status, error) {
            addLogEntry('❌ Network error during batch processing: ' + error);
            stopImport();
        });
    }
    
    /**
     * Update progress from server
     */
    function updateProgress() {
        if (!isImporting || !importSession) return;
        
        $.post(wingsImport.ajaxUrl, {
            action: 'wings_import_status',
            nonce: wingsImport.nonce,
            session_id: importSession
        })
        .done(function(response) {
            if (response.success) {
                updateUI(response.data);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('Status update error:', error);
        });
    }
    
    /**
     * Update UI with session data
     */
    function updateUI(sessionData) {
        // Update status
        updateStatus(sessionData.status, getStatusText(sessionData.status));
        
        // Update progress bar
        const percentage = sessionData.total_customers > 0 
            ? Math.round((sessionData.processed / sessionData.total_customers) * 100)
            : 0;
        $progressFill.css('width', percentage + '%');
        $progressText.text(`${sessionData.processed} / ${sessionData.total_customers} customers processed (${percentage}%)`);
        
        // Update stats
        $statTotal.text(sessionData.total_customers);
        $statProcessed.text(sessionData.processed);
        $statCreated.text(sessionData.created);
        $statUpdated.text(sessionData.updated);
        $statErrors.text(sessionData.errors);
        
        // Update elapsed time
        if (startTime) {
            const elapsed = Math.round((Date.now() - startTime) / 1000);
            $statTime.text(formatTime(elapsed));
        }
        
        // Update log
        if (sessionData.log && sessionData.log.length > 0) {
            const lastLogEntry = sessionData.log[sessionData.log.length - 1];
            if (lastLogEntry && lastLogEntry.message) {
                addLogEntry(`[${lastLogEntry.time}] ${lastLogEntry.message}`);
            }
        }
        
        // Show error details if any
        if (sessionData.errors > 0 && sessionData.error_details) {
            showErrorDetails(sessionData.error_details);
        }
    }
    
    /**
     * Update status indicator
     */
    function updateStatus(status, text) {
        $statusIndicator.removeClass().addClass('status-indicator status-' + status);
        $statusText.text(text);
    }
    
    /**
     * Get status text
     */
    function getStatusText(status) {
        switch (status) {
            case 'starting': return wingsImport.strings.starting;
            case 'processing': return wingsImport.strings.processing;
            case 'completed': return wingsImport.strings.completed;
            case 'error': return wingsImport.strings.error;
            default: return 'Unknown status';
        }
    }
    
    /**
     * Add log entry
     */
    function addLogEntry(message) {
        const $logEntry = $('<div class="log-entry"></div>').text(message);
        $logContainer.append($logEntry);
        $logContainer.scrollTop($logContainer[0].scrollHeight);
        
        // Keep only last 100 entries
        const entries = $logContainer.children();
        if (entries.length > 100) {
            entries.first().remove();
        }
    }
    
    /**
     * Show error details
     */
    function showErrorDetails(errors) {
        if (errors.length === 0) {
            $errorDetails.hide();
            return;
        }
        
        $errorList.empty();
        errors.forEach(function(error) {
            $errorList.append($('<li></li>').text(error));
        });
        
        $errorDetails.show();
    }
    
    /**
     * Format time
     */
    function formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    /**
     * Stop import
     */
    function stopImport() {
        if (!confirm(wingsImport.strings.confirm_stop) && isImporting) {
            return;
        }
        
        isImporting = false;
        
        if (importInterval) {
            clearInterval(importInterval);
            importInterval = null;
        }
        
        $startBtn.prop('disabled', false).show();
        $stopBtn.hide();
        
        addLogEntry('⏹️ Import stopped');
    }
    
    /**
     * Test connection
     */
    function testConnection() {
        updateConfiguration();
        
        $testBtn.addClass('loading').prop('disabled', true);
        
        $.post(wingsImport.ajaxUrl, {
            action: 'wings_test_connection',
            nonce: wingsImport.nonce
        })
        .done(function(response) {
            if (response.success) {
                addLogEntry('✅ Connection test successful');
                showNotice('Connection successful!', 'success');
            } else {
                addLogEntry('❌ Connection test failed: ' + response.data);
                showNotice('Connection failed: ' + response.data, 'error');
            }
        })
        .fail(function(xhr, status, error) {
            addLogEntry('❌ Connection test error: ' + error);
            showNotice('Connection error: ' + error, 'error');
        })
        .always(function() {
            $testBtn.removeClass('loading').prop('disabled', false);
        });
    }
    
    /**
     * Test API connection (settings page)
     */
    function testApiConnection() {
        const $btn = $(this);
        const $results = $('#test-results');
        
        $btn.addClass('loading').prop('disabled', true);
        $results.removeClass('success error').text('Testing connection...');
        
        $.post(wingsImport.ajaxUrl, {
            action: 'wings_test_connection',
            nonce: wingsImport.nonce
        })
        .done(function(response) {
            if (response.success) {
                $results.addClass('success').text('✅ ' + response.data.message);
            } else {
                $results.addClass('error').text('❌ ' + response.data);
            }
        })
        .fail(function(xhr, status, error) {
            $results.addClass('error').text('❌ Connection error: ' + error);
        })
        .always(function() {
            $btn.removeClass('loading').prop('disabled', false);
        });
    }
    
    /**
     * Clear logs
     */
    function clearLogs() {
        if (!confirm('Are you sure you want to clear all logs?')) {
            return;
        }
        
        $.post(wingsImport.ajaxUrl, {
            action: 'wings_clear_logs',
            nonce: wingsImport.nonce
        })
        .done(function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Failed to clear logs: ' + response.data);
            }
        });
    }
    
    /**
     * Update configuration from form
     */
    function updateConfiguration() {
        // This would typically save the configuration
        // For now, we'll just validate the fields
        const requiredFields = ['#api_alias', '#api_username', '#api_password'];
        let isValid = true;
        
        requiredFields.forEach(function(field) {
            const $field = $(field);
            if (!$field.val().trim()) {
                $field.css('border-color', '#dc3545');
                isValid = false;
            } else {
                $field.css('border-color', '');
            }
        });
        
        if (!isValid) {
            showNotice('Please fill in all required fields', 'error');
            return false;
        }
        
        return true;
    }
    
    /**
     * Show notice
     */
    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.wrap h1').after($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
    }
    
    // Auto-refresh page every 30 minutes to prevent session timeout
    setTimeout(function() {
        if (!isImporting) {
            location.reload();
        }
    }, 30 * 60 * 1000);
});
