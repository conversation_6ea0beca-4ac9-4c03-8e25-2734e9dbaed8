# Wings Customer Import - Installation Guide

## 🎯 Quick Installation

### Step 1: Upload Plugin
Upload the entire `wings-customer-import` folder to `/wp-content/plugins/`

### Step 2: Activate Plugin
1. Go to WordPress Admin → Plugins
2. Find "Wings Customer Import"
3. Click "Activate"

### Step 3: Configure Settings
1. Go to **Wings Import → Settings**
2. Enter your Wings API credentials:
   - **API URL**: `https://portal.wings.rs/api/v1/`
   - **API Alias**: Your Wings alias (e.g., `grosstest`)
   - **API Username**: Your Wings username (e.g., `aql`)
   - **API Password**: Your Wings password
   - **Batch Size**: `25` (recommended)
   - **Default Role**: `Professionals` (default)

### Step 4: Test Connection
1. Click "Test API Connection" in Settings
2. Verify you see "✅ Connection successful!"

### Step 5: Start Import
1. Go to **Wings Import** (main dashboard)
2. Verify configuration in the form
3. Click "🚀 Start Import"
4. **Monitor real-time progress** with:
   - Live progress bar
   - Statistics (Total, Processed, Created, Updated, Errors)
   - **Live log console** (always visible)
   - Error details if any issues occur

## 📊 What You'll See During Import

### Dashboard Layout
```
🚀 Wings Customer Import Dashboard

Configuration
┌─────────────────────────────────────────┐
│ API URL: https://portal.wings.rs/api/v1/│
│ Alias: grosstest                        │
│ Username: aql                           │
│ Password: ********                      │
│ Batch Size: 25                          │
│                                         │
│ [Test Connection] [🚀 Start Import]     │
└─────────────────────────────────────────┘

Import Progress (Always Visible)
┌─────────────────────────────────────────┐
│ ● Processing customers...               │
│ ████████████████████░░░░ 1,250/2,000 (62%)│
│                                         │
│ Total: 2,000  Created: 1,100           │
│ Processed: 1,250  Updated: 150         │
│ Errors: 0  Time: 15m 30s               │
│                                         │
│ Live Log                                │
│ ┌─────────────────────────────────────┐ │
│ │ [14:32:15] 🔄 Processing batch 51  │ │
│ │ [14:32:16] ✅ created: NOVA DOO    │ │
│ │ [14:32:16] ✅ updated: STARA FIRMA │ │
│ │ [14:32:17] 📥 Retrieved 25 customers│ │
│ │ [14:32:18] ✅ created: TEST COMP   │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## ✨ Key Changes Made

### 1. Default Role Changed
- **Old**: `customer`
- **New**: `Professionals`
- All imported users will have the "Professionals" role

### 2. Log Always Visible
- **Before**: Log only appeared during import
- **Now**: Log section is always visible on the dashboard
- Shows welcome messages when page loads
- Provides real-time feedback during import

### 3. Enhanced User Experience
- Progress section always visible
- Live log updates
- Clear status indicators
- Real-time statistics

## 🔧 Configuration Details

### API Settings
```php
'api_url' => 'https://portal.wings.rs/api/v1/'
'api_alias' => 'grosstest'           // Your Wings alias
'api_username' => 'aql'              // Your Wings username  
'api_password' => 'grossaql'         // Your Wings password
'batch_size' => 25                   // Customers per batch
'default_role' => 'Professionals'    // WordPress user role
```

### Expected Results
- **Processing Time**: 15-20 minutes for 2,000 customers
- **User Role**: All users created with "Professionals" role
- **Email Format**: `<EMAIL>` for missing emails
- **Address Parsing**: "36000 Kraljevo" → postcode: "36000", city: "Kraljevo"
- **Data Preservation**: All Wings metadata saved as user meta

## 📝 Live Log Features

The log console now shows:
- **Welcome messages** when dashboard loads
- **Real-time progress** during import
- **Detailed status updates** for each batch
- **Success/error messages** for individual customers
- **Completion statistics** when import finishes

### Sample Log Output
```
📋 Wings Customer Import Dashboard loaded
ℹ️ Configure your API settings and click "Start Import" to begin
🚀 Starting Wings API import...
✅ Import session created: wings_import_abc123
🔄 Processing batch 1
📊 Found 2000 total customers
📥 Retrieved 25 customers
✅ created: AB KOMPANIJA - OPREMA
✅ updated: ALFA OPTIK
✅ created: NOVA FIRMA DOO
🔄 Processing batch 2
...
🎉 Import completed in 1200 seconds
```

## 🚨 Troubleshooting

### Common Issues
1. **"Connection failed"**: Check API credentials
2. **"Import timeouts"**: Reduce batch size to 15-20
3. **"Memory errors"**: Increase PHP memory limit
4. **"No customers found"**: Verify Wings Portal access

### Support
- Check the **Import Logs** page for detailed error information
- Monitor the **live log console** for real-time feedback
- Test connection before importing
- Start with small batch sizes for testing

---

**Ready to import?** The plugin now provides complete visibility into the import process with always-visible progress tracking and live logging! 🚀
