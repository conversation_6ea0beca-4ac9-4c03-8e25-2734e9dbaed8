<?php
/**
 * Wings API Connection Debug Script
 * Run this script to debug connection issues
 */

// Include WordPress if not already loaded
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Test configuration
$config = array(
    'api_url' => 'https://portal.wings.rs/api/v1/',
    'api_alias' => 'grosstest',
    'api_username' => 'aql',
    'api_password' => 'grossaql'
);

echo "<h1>Wings API Connection Debug</h1>\n";
echo "<h2>Configuration:</h2>\n";
echo "<ul>\n";
foreach ($config as $key => $value) {
    $display_value = ($key === 'api_password') ? str_repeat('*', strlen($value)) : $value;
    echo "<li><strong>{$key}:</strong> {$display_value}</li>\n";
}
echo "</ul>\n";

$login_url = $config['api_url'] . $config['api_alias'] . '/system.user.log';
echo "<h2>Login URL:</h2>\n";
echo "<p>{$login_url}</p>\n";

echo "<h2>Testing Connection...</h2>\n";

// Test 1: Basic URL accessibility
echo "<h3>Test 1: URL Accessibility</h3>\n";
$headers = @get_headers($login_url);
if ($headers) {
    echo "<p>✅ URL is accessible</p>\n";
    echo "<p>Response: " . $headers[0] . "</p>\n";
} else {
    echo "<p>❌ URL is not accessible</p>\n";
}

// Test 2: WordPress HTTP API
echo "<h3>Test 2: WordPress HTTP API Test</h3>\n";

$response = wp_remote_post($login_url, array(
    'body' => array(
        'aUn' => $config['api_username'],
        'aUp' => $config['api_password']
    ),
    'timeout' => 30,
    'headers' => array(
        'Content-Type' => 'application/x-www-form-urlencoded'
    )
));

if (is_wp_error($response)) {
    echo "<p>❌ WordPress HTTP API Error:</p>\n";
    echo "<p><strong>Error Code:</strong> " . $response->get_error_code() . "</p>\n";
    echo "<p><strong>Error Message:</strong> " . $response->get_error_message() . "</p>\n";
    
    // Check for common issues
    echo "<h3>Possible Issues:</h3>\n";
    echo "<ul>\n";
    echo "<li>Server firewall blocking external requests</li>\n";
    echo "<li>SSL certificate issues</li>\n";
    echo "<li>DNS resolution problems</li>\n";
    echo "<li>Wings Portal server is down</li>\n";
    echo "</ul>\n";
} else {
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    $response_headers = wp_remote_retrieve_headers($response);
    
    echo "<p>✅ HTTP Request Successful</p>\n";
    echo "<p><strong>Response Code:</strong> {$response_code}</p>\n";
    echo "<p><strong>Response Body (first 500 chars):</strong></p>\n";
    echo "<pre>" . htmlspecialchars(substr($response_body, 0, 500)) . "</pre>\n";
    
    // Check for cookies
    $cookies = wp_remote_retrieve_cookies($response);
    echo "<p><strong>Cookies Received:</strong></p>\n";
    if (empty($cookies)) {
        echo "<p>❌ No cookies received</p>\n";
    } else {
        echo "<ul>\n";
        foreach ($cookies as $cookie) {
            echo "<li><strong>{$cookie->name}:</strong> " . substr($cookie->value, 0, 20) . "...</li>\n";
        }
        echo "</ul>\n";
        
        // Check for PHPSESSID specifically
        $session_found = false;
        foreach ($cookies as $cookie) {
            if ($cookie->name === 'PHPSESSID') {
                echo "<p>✅ PHPSESSID cookie found: " . substr($cookie->value, 0, 10) . "...</p>\n";
                $session_found = true;
                break;
            }
        }
        
        if (!$session_found) {
            echo "<p>❌ PHPSESSID cookie not found</p>\n";
        }
    }
    
    // Analyze response
    echo "<h3>Response Analysis:</h3>\n";
    if ($response_code === 200) {
        echo "<p>✅ HTTP 200 OK - Request successful</p>\n";
        
        if (strpos($response_body, 'error') !== false) {
            echo "<p>⚠️ Response contains 'error' - possible authentication failure</p>\n";
        }
        
        if (strpos($response_body, 'success') !== false) {
            echo "<p>✅ Response contains 'success' - likely authenticated</p>\n";
        }
        
        if (empty($cookies)) {
            echo "<p>❌ No session cookie - authentication may have failed</p>\n";
            echo "<p><strong>Possible reasons:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Incorrect username or password</li>\n";
            echo "<li>Account locked or disabled</li>\n";
            echo "<li>Wings Portal API endpoint changed</li>\n";
            echo "<li>Server-side session handling issues</li>\n";
            echo "</ul>\n";
        }
    } else {
        echo "<p>❌ HTTP {$response_code} - Request failed</p>\n";
        
        switch ($response_code) {
            case 404:
                echo "<p>The API endpoint was not found. Check the URL and alias.</p>\n";
                break;
            case 401:
                echo "<p>Unauthorized. Check your credentials.</p>\n";
                break;
            case 403:
                echo "<p>Forbidden. Your account may not have API access.</p>\n";
                break;
            case 500:
                echo "<p>Server error. Wings Portal may be experiencing issues.</p>\n";
                break;
            default:
                echo "<p>Unexpected response code. Check Wings Portal status.</p>\n";
        }
    }
}

// Test 3: Alternative method using cURL
echo "<h3>Test 3: cURL Test (if available)</h3>\n";

if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
        'aUn' => $config['api_username'],
        'aUp' => $config['api_password']
    )));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $curl_response = curl_exec($ch);
    $curl_error = curl_error($ch);
    $curl_info = curl_getinfo($ch);
    curl_close($ch);
    
    if ($curl_error) {
        echo "<p>❌ cURL Error: {$curl_error}</p>\n";
    } else {
        echo "<p>✅ cURL Request Successful</p>\n";
        echo "<p><strong>HTTP Code:</strong> {$curl_info['http_code']}</p>\n";
        echo "<p><strong>Response (first 300 chars):</strong></p>\n";
        echo "<pre>" . htmlspecialchars(substr($curl_response, 0, 300)) . "</pre>\n";
    }
} else {
    echo "<p>❌ cURL not available</p>\n";
}

echo "<h2>Recommendations:</h2>\n";
echo "<ol>\n";
echo "<li><strong>Verify credentials:</strong> Double-check username, password, and alias with Wings Portal admin</li>\n";
echo "<li><strong>Test manually:</strong> Try logging into Wings Portal web interface with same credentials</li>\n";
echo "<li><strong>Check server:</strong> Ensure your WordPress server can make external HTTP requests</li>\n";
echo "<li><strong>Contact Wings support:</strong> If credentials are correct, contact Wings Portal support</li>\n";
echo "</ol>\n";

echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
