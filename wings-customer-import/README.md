# Wings Customer Import Plugin

A comprehensive WordPress plugin for importing customers from Wings Portal API with real-time progress tracking.

## 📁 Plugin Structure

```
wings-customer-import/
├── wings-customer-import.php          # Main plugin file
├── readme.txt                         # WordPress plugin readme
├── README.md                          # This file
├── includes/                          # Core functionality
│   ├── class-wings-api.php           # Wings API communication
│   └── class-wings-importer.php      # Import processing logic
├── admin/                             # Admin interface
│   └── class-wings-admin.php         # Admin pages and forms
├── assets/                            # Frontend assets
│   ├── css/
│   │   └── admin.css                 # Admin styles
│   └── js/
│       └── admin.js                  # Admin JavaScript
└── languages/                        # Translation files (future)
    └── wings-customer-import.pot     # Translation template
```

## 🚀 Installation

### Method 1: Upload to Plugins Directory

1. **Upload the entire folder** to `/wp-content/plugins/`:
   ```
   /wp-content/plugins/wings-customer-import/
   ```

2. **Activate the plugin** in WordPress Admin:
   - Go to `Plugins → Installed Plugins`
   - Find "Wings Customer Import"
   - Click "Activate"

3. **Configure settings**:
   - Go to `Wings Import → Settings`
   - Enter your Wings API credentials
   - Test the connection

### Method 2: ZIP Installation

1. **Create a ZIP file** of the entire `wings-customer-import` folder
2. **Upload via WordPress Admin**:
   - Go to `Plugins → Add New → Upload Plugin`
   - Choose the ZIP file
   - Click "Install Now" then "Activate"

## ⚙️ Configuration

### Required Settings

| Setting | Description | Example |
|---------|-------------|---------|
| **API URL** | Wings Portal API base URL | `https://portal.wings.rs/api/v1/` |
| **API Alias** | Your Wings Portal alias | `grosstest` |
| **API Username** | Wings API username | `aql` |
| **API Password** | Wings API password | `grossaql` |
| **Batch Size** | Customers per batch (recommended: 25) | `25` |
| **Default Role** | WordPress role for imported users | `Professionals` |

### Configuration Steps

1. **Navigate to Settings**:
   ```
   WordPress Admin → Wings Import → Settings
   ```

2. **Fill in API credentials**:
   - Get these from your Wings Portal administrator
   - Test connection before importing

3. **Adjust batch size**:
   - Smaller batches = more stable (recommended: 25)
   - Larger batches = faster but may timeout

## 🎯 Usage

### Starting an Import

1. **Go to Import Dashboard**:
   ```
   WordPress Admin → Wings Import
   ```

2. **Verify configuration** in the form fields

3. **Click "Start Import"** button

4. **Monitor progress** in real-time:
   - Progress bar shows completion percentage
   - Statistics show created/updated/errors
   - Live log shows detailed progress
   - Error details appear if issues occur

### Import Process

The plugin follows this process:

1. **Authentication** - Connects to Wings API
2. **Customer Count** - Gets total number of customers
3. **Batch Processing** - Processes customers in small batches
4. **Data Mapping** - Maps Wings data to WordPress fields
5. **User Creation/Update** - Creates new users or updates existing
6. **Completion** - Shows final statistics and logs

## 📊 Data Mapping

### Wings → WordPress User Fields

| Wings Field | WordPress Field | Description |
|-------------|-----------------|-------------|
| `naziv` | `display_name`, `nickname` | Company/customer name |
| `email` | `user_email` | Email (generated if missing) |
| `kontakt` | `first_name`, `last_name` | Contact person name |
| `adresa` | `billing_address_1` | Street address |
| `mesto` | `billing_city`, `billing_postcode` | City and postal code |
| `telefon`/`mobilni` | `billing_phone` | Phone number |

### Wings → User Meta Fields

| Wings Field | Meta Key | Description |
|-------------|----------|-------------|
| `id` | `wings_customer_id` | Wings customer ID |
| `sifra` | `wings_customer_code` | Customer code |
| `pib` | `wings_pib` | Tax ID |
| `status` | `wings_status` | Customer status |
| `komercijalista` | `wings_sales_rep` | Sales representative |
| `klasa` | `wings_customer_class` | Customer class |
| `rabat` | `wings_discount` | Discount percentage |
| `limit` | `wings_credit_limit` | Credit limit |

## 🔧 Technical Details

### System Requirements

- **WordPress**: 5.0+
- **PHP**: 7.4+
- **Memory**: 256MB+ recommended
- **Execution Time**: 300+ seconds for large imports
- **Network**: External API access required

### Performance Specifications

- **Batch Size**: 25 customers per API call (configurable)
- **Processing Speed**: ~100-150 customers per minute
- **Memory Usage**: Optimized with log rotation
- **Session Timeout**: 2 hours (sufficient for large imports)
- **Estimated Time**: 15-20 minutes for 2,000 customers

### Security Features

- **WordPress Authentication** - Requires admin privileges
- **Nonce Verification** - CSRF protection
- **Input Sanitization** - All data sanitized
- **Capability Checks** - `manage_options` required
- **Session Management** - Secure session handling

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Failed**:
   - Verify API credentials
   - Check Wings Portal access
   - Test connection in settings

2. **Import Timeouts**:
   - Reduce batch size to 10-15
   - Check server PHP limits
   - Monitor server resources

3. **Memory Issues**:
   - Increase PHP memory limit
   - Reduce batch size
   - Clear old import sessions

4. **API Connection Errors**:
   - Check server firewall settings
   - Verify external URL access
   - Test Wings Portal availability

### Debug Information

Check these locations for debugging:

1. **Plugin Logs**:
   ```
   Wings Import → Import Logs
   ```

2. **WordPress Debug Log**:
   ```
   /wp-content/debug.log
   ```

3. **Server Error Logs**:
   ```
   Check your hosting control panel
   ```

## 📝 Logging

The plugin maintains detailed logs:

- **Session Logs** - Stored in WordPress transients
- **Database Logs** - Permanent logs in custom table
- **Live Logs** - Real-time display during import
- **Error Details** - Specific error information

### Log Levels

- **Info** - General information
- **Error** - Import errors
- **Warning** - Non-critical issues
- **Success** - Successful operations

## 🔄 Updates

### Version History

- **1.0.0** - Initial release with full functionality

### Future Enhancements

- Multi-language support
- Export functionality
- Advanced filtering options
- Scheduled imports
- Email notifications

## 📞 Support

For technical support:

1. **Check Documentation** - Review this README
2. **Check Logs** - Look at import logs for errors
3. **Test Connection** - Verify API connectivity
4. **Contact Support** - Reach out with specific error messages

## 🤝 Contributing

To contribute to this plugin:

1. Follow WordPress coding standards
2. Test with various data sets
3. Document any changes
4. Submit pull requests with clear descriptions

## 📄 License

This plugin is licensed under GPL v2 or later.

---

**Ready to import your Wings customers?** 🚀

1. Install the plugin
2. Configure your API settings
3. Test the connection
4. Start importing!

The plugin will handle the rest with real-time progress tracking and comprehensive error handling.
