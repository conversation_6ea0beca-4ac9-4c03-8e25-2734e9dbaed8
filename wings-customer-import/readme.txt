=== Wings Customer Import ===
Contributors: yourname
Tags: import, customers, wings, api, bulk-import
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Import customers from Wings Portal API to WordPress users with real-time progress tracking. Handles 2000+ customers efficiently.

== Description ==

Wings Customer Import is a powerful WordPress plugin that allows you to import customers directly from the Wings Portal API into your WordPress site as users. Perfect for businesses that need to synchronize their Wings customer database with their WordPress website.

**Key Features:**

* **Real-time Progress Tracking** - Watch the import progress with live updates
* **Batch Processing** - Efficiently handles large datasets (2000+ customers)
* **Smart Data Mapping** - Automatically maps Wings customer data to WordPress user fields
* **Email Generation** - Creates emails for customers without email addresses (<EMAIL>)
* **Address Parsing** - Intelligently parses addresses like "36000 Kraljevo" into postcode and city
* **Duplicate Prevention** - Checks for existing users and updates instead of creating duplicates
* **Error Handling** - Comprehensive error reporting and recovery
* **Detailed Logging** - Complete import logs for troubleshooting
* **Configurable Settings** - Customize API credentials, batch sizes, and user roles (default: Professionals)

**Perfect For:**

* E-commerce sites using Wings Portal
* B2B businesses with existing Wings customer databases
* Companies migrating from Wings to WordPress
* Businesses needing customer data synchronization

**Data Mapping:**

The plugin maps Wings customer data to WordPress user fields:

* `naziv` → Display Name & Nickname
* `email` → User Email (generated if missing)
* `kontakt` → First Name & Last Name
* `adresa` → Billing Address
* `mesto` → City & Postcode (parsed from "36000 Kraljevo" format)
* `telefon/mobilni` → Phone Number
* All Wings-specific data preserved as user meta

**Requirements:**

* WordPress 5.0 or higher
* PHP 7.4 or higher
* Access to Wings Portal API
* Valid Wings API credentials

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/wings-customer-import/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to 'Wings Import' in your admin menu
4. Configure your Wings API credentials in Settings
5. Test the connection
6. Start importing customers!

**Manual Installation:**

1. Download the plugin zip file
2. Go to WordPress Admin → Plugins → Add New → Upload Plugin
3. Choose the zip file and click Install Now
4. Activate the plugin
5. Configure settings and start importing

== Frequently Asked Questions ==

= What is Wings Portal? =

Wings Portal is a business management system used by many companies for customer and inventory management. This plugin allows you to import customer data from Wings into WordPress.

= How many customers can I import? =

The plugin is designed to handle large datasets efficiently. It has been tested with 2000+ customers and uses batch processing to prevent timeouts.

= What happens if a customer already exists? =

The plugin checks for existing users by Wings customer ID and email address. If found, it updates the existing user instead of creating a duplicate.

= What if customers don't have email addresses? =

The plugin automatically generates email addresses using the format: <EMAIL> (based on the customer's company name).

= Can I customize the user role for imported customers? =

Yes, you can set the default user role in the plugin settings. All imported customers will be assigned this role.

= Is the import process safe? =

Yes, the plugin includes comprehensive error handling and logging. It's recommended to backup your database before running large imports.

== Screenshots ==

1. Main import dashboard with real-time progress
2. Configuration settings page
3. Import logs and error reporting
4. Live progress tracking during import
5. Statistics and completion summary

== Changelog ==

= 1.0.0 =
* Initial release
* Real-time import progress tracking
* Batch processing for large datasets
* Smart data mapping from Wings to WordPress
* Email generation for missing addresses
* Address parsing (postcode + city)
* Comprehensive error handling and logging
* Configurable settings
* User role assignment
* Duplicate prevention

== Upgrade Notice ==

= 1.0.0 =
Initial release of Wings Customer Import plugin.

== Support ==

For support, please contact [<EMAIL>] or visit [your-support-url].

== Technical Details ==

**API Endpoints Used:**
* `system.user.log` - Authentication
* `local.kupac.svi` - Customer listing with pagination

**WordPress Integration:**
* Uses WordPress user system
* Stores Wings data as user meta
* Compatible with WooCommerce billing fields
* Follows WordPress coding standards

**Performance:**
* Batch processing (configurable batch size)
* Session-based import tracking
* Memory-efficient logging
* Timeout prevention

**Security:**
* WordPress nonce verification
* Capability checks (manage_options)
* Input sanitization
* SQL injection prevention
