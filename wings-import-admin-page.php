<?php
/**
 * Wings Import Admin Page
 * Integrates the Wings import into WordPress admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'wings_import_admin_menu');

function wings_import_admin_menu() {
    add_management_page(
        'Wings Customer Import',
        'Wings Import',
        'manage_options',
        'wings-import',
        'wings_import_admin_page'
    );
}

function wings_import_admin_page() {
    ?>
    <div class="wrap">
        <h1>Wings Customer Import</h1>
        <p>Import customers from Wings Portal API to WordPress users.</p>
        
        <div style="background: #fff; padding: 20px; border: 1px solid #ccd0d4; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
            <h2>🚀 Launch Import Dashboard</h2>
            <p>Click the button below to open the real-time import dashboard:</p>
            
            <a href="<?php echo home_url('/wings_api_import_realtime.php'); ?>" 
               target="_blank" 
               class="button button-primary button-hero">
                🚀 Open Import Dashboard
            </a>
            
            <h3 style="margin-top: 30px;">📋 Before You Start:</h3>
            <ul>
                <li>✅ Make sure you have your Wings API credentials ready</li>
                <li>✅ Ensure your server can handle long-running processes</li>
                <li>✅ Backup your database before importing</li>
                <li>✅ The import will create ~2000 new WordPress users</li>
            </ul>
            
            <h3>⚙️ Configuration:</h3>
            <ul>
                <li><strong>API URL:</strong> https://portal.wings.rs/api/v1/</li>
                <li><strong>Alias:</strong> Your Wings alias (e.g., grosstest)</li>
                <li><strong>Username:</strong> Your Wings API username</li>
                <li><strong>Password:</strong> Your Wings API password</li>
                <li><strong>Batch Size:</strong> 25 (recommended for large imports)</li>
            </ul>
            
            <h3>📊 Expected Results:</h3>
            <ul>
                <li><strong>Processing Time:</strong> 15-20 minutes for 2000 customers</li>
                <li><strong>User Role:</strong> All imported users will have 'customer' role</li>
                <li><strong>Email Generation:</strong> Missing <NAME_EMAIL></li>
                <li><strong>Address Parsing:</strong> "36000 Kraljevo" → postcode + city</li>
                <li><strong>Data Mapping:</strong> All Wings data preserved as user meta</li>
            </ul>
        </div>
    </div>
    <?php
}

// Include the import functionality
require_once(ABSPATH . 'wings_api_import_realtime.php');
?>
