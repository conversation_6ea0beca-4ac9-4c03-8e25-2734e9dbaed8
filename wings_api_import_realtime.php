<?php
/**
 * Wings API Direct Customer Import Script with Real-time Progress
 * Imports customers directly from Wings Portal API to WordPress users
 * Features: Real-time progress, AJAX processing, live logging for 2000+ users
 * 
 * Usage: Access via browser for GUI interface
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    // If running standalone, include WordPress
    require_once('../../../wp-config.php');
}

// Handle AJAX requests
add_action('wp_ajax_wings_import_start', 'handle_wings_import_start');
add_action('wp_ajax_wings_import_batch', 'handle_wings_import_batch');
add_action('wp_ajax_wings_import_status', 'handle_wings_import_status');

function handle_wings_import_start() {
    check_ajax_referer('wings_import_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    // Initialize import session
    $session_id = uniqid('wings_import_');
    set_transient('wings_import_session_' . $session_id, array(
        'status' => 'starting',
        'total_customers' => 0,
        'processed' => 0,
        'created' => 0,
        'updated' => 0,
        'errors' => 0,
        'current_batch' => 0,
        'batch_size' => 25, // Smaller batches for 2000+ users
        'start_time' => time(),
        'log' => array(),
        'error_details' => array()
    ), 7200); // 2 hour expiry for large imports
    
    wp_send_json_success(array('session_id' => $session_id));
}

function handle_wings_import_batch() {
    check_ajax_referer('wings_import_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    $session_id = sanitize_text_field($_POST['session_id']);
    $session_data = get_transient('wings_import_session_' . $session_id);
    
    if (!$session_data) {
        wp_send_json_error('Session expired or invalid');
    }
    
    $importer = new Wings_API_Customer_Importer();
    $result = $importer->process_batch($session_data);
    
    // Update session data
    set_transient('wings_import_session_' . $session_id, $result, 7200);
    
    wp_send_json_success($result);
}

function handle_wings_import_status() {
    check_ajax_referer('wings_import_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    $session_id = sanitize_text_field($_POST['session_id']);
    $session_data = get_transient('wings_import_session_' . $session_id);
    
    if (!$session_data) {
        wp_send_json_error('Session expired or invalid');
    }
    
    wp_send_json_success($session_data);
}

class Wings_API_Customer_Importer {
    
    private $api_url;
    private $api_alias;
    private $session_token = null;
    private $authenticated = false;
    
    // API credentials
    private $username;
    private $password;
    
    /**
     * Constructor
     */
    public function __construct($api_url = 'https://portal.wings.rs/api/v1/', $alias = 'grosstest', $username = 'aql', $password = 'grossaql') {
        $this->api_url = trailingslashit($api_url);
        $this->api_alias = $alias;
        $this->username = $username;
        $this->password = $password;
    }
    
    /**
     * Process a batch of customers
     */
    public function process_batch($session_data) {
        $this->add_log($session_data, "🔄 Processing batch " . ($session_data['current_batch'] + 1));
        
        // Authenticate if needed
        if (!$this->authenticate()) {
            $session_data['status'] = 'error';
            $this->add_log($session_data, "❌ Authentication failed");
            return $session_data;
        }
        
        // First batch - get total count
        if ($session_data['current_batch'] === 0) {
            $total_count = $this->get_total_customer_count();
            if ($total_count > 0) {
                $session_data['total_customers'] = $total_count;
                $this->add_log($session_data, "📊 Found {$total_count} total customers");
            }
        }
        
        // Get batch of customers
        $start = $session_data['current_batch'] * $session_data['batch_size'];
        $batch_data = $this->get_customers_batch($start, $session_data['batch_size']);
        
        if (!$batch_data || !isset($batch_data['data'])) {
            $session_data['status'] = 'error';
            $this->add_log($session_data, "❌ Failed to fetch customer batch");
            return $session_data;
        }
        
        $customers = $batch_data['data'];
        $batch_count = count($customers);
        
        $this->add_log($session_data, "📥 Retrieved {$batch_count} customers");
        
        // Process each customer in the batch
        foreach ($customers as $customer) {
            $result = $this->process_single_customer($customer, $session_data);
            
            if ($result['success']) {
                if ($result['action'] === 'created') {
                    $session_data['created']++;
                } else {
                    $session_data['updated']++;
                }
                $this->add_log($session_data, "✅ {$result['action']}: {$result['name']}");
            } else {
                $session_data['errors']++;
                $session_data['error_details'][] = $result['error'];
                $this->add_log($session_data, "❌ Error: {$result['error']}");
            }
            
            $session_data['processed']++;
        }
        
        $session_data['current_batch']++;
        
        // Check if we're done
        if ($batch_count < $session_data['batch_size'] || 
            ($session_data['total_customers'] > 0 && $session_data['processed'] >= $session_data['total_customers'])) {
            $session_data['status'] = 'completed';
            $elapsed = time() - $session_data['start_time'];
            $this->add_log($session_data, "🎉 Import completed in {$elapsed} seconds");
        } else {
            $session_data['status'] = 'processing';
        }
        
        return $session_data;
    }
    
    /**
     * Add log entry
     */
    private function add_log(&$session_data, $message) {
        $session_data['log'][] = array(
            'time' => current_time('H:i:s'),
            'message' => $message
        );
        
        // Keep only last 50 log entries to prevent memory issues
        if (count($session_data['log']) > 50) {
            $session_data['log'] = array_slice($session_data['log'], -50);
        }
    }
    
    /**
     * Authenticate with Wings Portal API
     */
    private function authenticate() {
        if ($this->authenticated) {
            return true;
        }
        
        $login_url = $this->api_url . $this->api_alias . '/system.user.log';
        
        $response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $this->username,
                'aUp' => $this->password
            ),
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded'
            )
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            // Extract session cookie
            $cookies = wp_remote_retrieve_cookies($response);
            foreach ($cookies as $cookie) {
                if ($cookie->name === 'PHPSESSID') {
                    $this->session_token = $cookie->value;
                    $this->authenticated = true;
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get total customer count
     */
    private function get_total_customer_count() {
        // Try to get a small batch to check total
        $response = $this->make_api_request('local.kupac.svi', array(
            'dStart' => 0,
            'dLength' => 1,
            'output' => 'jsonapi'
        ), 'GET');
        
        if (is_wp_error($response) || !isset($response['meta']['total'])) {
            return 0;
        }
        
        return intval($response['meta']['total'] ?? 0);
    }
    
    /**
     * Make authenticated API request
     */
    private function make_api_request($endpoint, $params = array(), $method = 'GET') {
        if (!$this->authenticated) {
            return new WP_Error('not_authenticated', 'Not authenticated with Wings API');
        }
        
        $url = $this->api_url . $this->api_alias . '/' . $endpoint;
        
        $args = array(
            'timeout' => 60,
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $this->session_token
            )
        );
        
        if ($method === 'GET' && !empty($params)) {
            $url = add_query_arg($params, $url);
            $response = wp_remote_get($url, $args);
        } else {
            $args['method'] = 'POST';
            $args['body'] = $params;
            $response = wp_remote_post($url, $args);
        }
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_error', 'Invalid JSON response: ' . json_last_error_msg());
        }
        
        return $data;
    }
    
    /**
     * Get customers from Wings API with pagination
     */
    private function get_customers_batch($start = 0, $length = 25) {
        $params = array(
            'dStart' => $start,
            'dLength' => $length,
            'output' => 'jsonapi'
        );

        return $this->make_api_request('local.kupac.svi', $params, 'GET');
    }

    /**
     * Process single customer
     */
    private function process_single_customer($customer, &$session_data) {
        if (!isset($customer['attributes'])) {
            return array('success' => false, 'error' => 'Missing customer attributes');
        }

        $attrs = $customer['attributes'];
        $wings_id = $customer['id'] ?? '';

        // Extract and clean data
        $customer_data = $this->extract_customer_data($attrs, $wings_id);

        if (!$customer_data) {
            return array('success' => false, 'error' => 'Could not extract valid customer data');
        }

        // Check if user already exists
        $existing_user = $this->find_existing_user($customer_data);

        if ($existing_user) {
            // Update existing user
            $result = $this->update_existing_user($existing_user, $customer_data);
            if ($result) {
                return array(
                    'success' => true,
                    'action' => 'updated',
                    'name' => $customer_data['display_name'],
                    'user_id' => $existing_user->ID
                );
            } else {
                return array('success' => false, 'error' => 'Failed to update user: ' . $customer_data['display_name']);
            }
        } else {
            // Create new user
            $user_id = $this->create_new_user($customer_data);
            if ($user_id && !is_wp_error($user_id)) {
                return array(
                    'success' => true,
                    'action' => 'created',
                    'name' => $customer_data['display_name'],
                    'user_id' => $user_id
                );
            } else {
                $error_msg = is_wp_error($user_id) ? $user_id->get_error_message() : 'Unknown error';
                return array('success' => false, 'error' => 'Failed to create user: ' . $customer_data['display_name'] . ' - ' . $error_msg);
            }
        }
    }

    /**
     * Extract and clean customer data from Wings attributes
     */
    private function extract_customer_data($attrs, $wings_id) {
        // Basic validation
        $naziv = trim($attrs['naziv'] ?? '');
        if (empty($naziv)) {
            return false;
        }

        // Generate email if missing
        $email = trim($attrs['email'] ?? '');
        if (empty($email)) {
            $email = $this->generate_email_from_naziv($naziv);
        }

        // Parse address to extract postcode and city
        $address_info = $this->parse_address($attrs['mesto'] ?? '');

        // Parse contact name
        $contact_info = $this->parse_contact_name($attrs['kontakt'] ?? '');

        // Generate username from email
        $username = $this->generate_username($email, $wings_id);

        return array(
            // WordPress user fields
            'user_login' => $username,
            'user_email' => $email,
            'user_pass' => wp_generate_password(12, false),
            'display_name' => $naziv,
            'nickname' => $naziv, // As requested
            'first_name' => $contact_info['first_name'],
            'last_name' => $contact_info['last_name'],
            'role' => 'customer',

            // Billing information
            'billing_company' => $naziv,
            'billing_first_name' => $contact_info['first_name'],
            'billing_last_name' => $contact_info['last_name'],
            'billing_address_1' => trim($attrs['adresa'] ?? ''),
            'billing_city' => $address_info['city'],
            'billing_postcode' => $address_info['postcode'],
            'billing_country' => 'RS',
            'billing_phone' => $this->get_primary_phone($attrs),
            'billing_email' => $email,

            // Wings-specific metadata
            'wings_customer_id' => $wings_id,
            'wings_customer_code' => trim($attrs['sifra'] ?? ''),
            'wings_pib' => trim($attrs['pib'] ?? ''),
            'wings_mb' => trim($attrs['mb'] ?? ''),
            'wings_status' => trim($attrs['status'] ?? 'K'),
            'wings_sales_rep' => trim($attrs['komercijalista'] ?? ''),
            'wings_customer_class' => intval($attrs['klasa'] ?? 0),
            'wings_discount' => floatval($attrs['rabat'] ?? 0.0),
            'wings_credit_limit' => floatval($attrs['limit'] ?? 0.0),
            'wings_payment_terms' => intval($attrs['rokplacanja'] ?? 0),
            'wings_tolerance' => intval($attrs['tolerancija'] ?? 0),
            'wings_working_hours' => trim($attrs['radnovreme'] ?? ''),
            'wings_phone_2' => trim($attrs['fax'] ?? ''),
            'wings_phone_3' => trim($attrs['mobilni'] ?? ''),
            'wings_last_sync' => current_time('mysql')
        );
    }

    /**
     * Generate email from naziv (company name)
     */
    private function generate_email_from_naziv($naziv) {
        // Clean the naziv for email
        $clean_naziv = strtolower($naziv);

        // Remove special characters and spaces
        $clean_naziv = preg_replace('/[^a-z0-9]/', '', $clean_naziv);

        // Limit length
        $clean_naziv = substr($clean_naziv, 0, 30);

        // Ensure it's not empty
        if (empty($clean_naziv)) {
            $clean_naziv = 'customer' . uniqid();
        }

        return $clean_naziv . '@mail.com';
    }

    /**
     * Parse address in format "36000 Kraljevo" to extract postcode and city
     */
    private function parse_address($mesto) {
        $mesto = trim($mesto);

        // Match pattern: number followed by space and city name
        if (preg_match('/^(\d{5})\s+(.+)$/', $mesto, $matches)) {
            return array(
                'postcode' => $matches[1],
                'city' => trim($matches[2])
            );
        }

        // If no postcode found, treat entire string as city
        return array(
            'postcode' => '',
            'city' => $mesto
        );
    }

    /**
     * Parse contact name into first and last name
     */
    private function parse_contact_name($kontakt) {
        $kontakt = trim($kontakt);

        if (empty($kontakt)) {
            return array('first_name' => '', 'last_name' => '');
        }

        $parts = explode(' ', $kontakt, 2);

        return array(
            'first_name' => $parts[0] ?? '',
            'last_name' => $parts[1] ?? ''
        );
    }

    /**
     * Get primary phone number (prefer telefon, fallback to mobilni)
     */
    private function get_primary_phone($attrs) {
        $telefon = trim($attrs['telefon'] ?? '');
        $mobilni = trim($attrs['mobilni'] ?? '');

        return !empty($telefon) ? $telefon : $mobilni;
    }

    /**
     * Generate unique username
     */
    private function generate_username($email, $wings_id) {
        $base_username = strstr($email, '@', true); // Get part before @

        // If username exists, append wings_id
        if (username_exists($base_username)) {
            $base_username = $base_username . '_' . $wings_id;
        }

        // If still exists, append random number
        $counter = 1;
        $username = $base_username;
        while (username_exists($username)) {
            $username = $base_username . '_' . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Find existing user by Wings ID or email
     */
    private function find_existing_user($customer_data) {
        // First try to find by Wings customer ID
        if (!empty($customer_data['wings_customer_id'])) {
            $users = get_users(array(
                'meta_key' => 'wings_customer_id',
                'meta_value' => $customer_data['wings_customer_id'],
                'number' => 1
            ));

            if (!empty($users)) {
                return $users[0];
            }
        }

        // Then try by email
        $user = get_user_by('email', $customer_data['user_email']);
        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Create new WordPress user
     */
    private function create_new_user($customer_data) {
        // Prepare user data for wp_insert_user
        $user_data = array(
            'user_login' => $customer_data['user_login'],
            'user_email' => $customer_data['user_email'],
            'user_pass' => $customer_data['user_pass'],
            'display_name' => $customer_data['display_name'],
            'nickname' => $customer_data['nickname'],
            'first_name' => $customer_data['first_name'],
            'last_name' => $customer_data['last_name'],
            'role' => $customer_data['role']
        );

        // Create user
        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            return $user_id;
        }

        // Add all metadata
        $this->add_user_metadata($user_id, $customer_data);

        return $user_id;
    }

    /**
     * Update existing user
     */
    private function update_existing_user($user, $customer_data) {
        // Update basic user info
        $user_data = array(
            'ID' => $user->ID,
            'display_name' => $customer_data['display_name'],
            'nickname' => $customer_data['nickname'],
            'first_name' => $customer_data['first_name'],
            'last_name' => $customer_data['last_name']
        );

        $result = wp_update_user($user_data);

        if (is_wp_error($result)) {
            return false;
        }

        // Update metadata
        $this->add_user_metadata($user->ID, $customer_data);

        return true;
    }

    /**
     * Add user metadata
     */
    private function add_user_metadata($user_id, $customer_data) {
        // Billing information
        $billing_fields = array(
            'billing_company', 'billing_first_name', 'billing_last_name',
            'billing_address_1', 'billing_city', 'billing_postcode',
            'billing_country', 'billing_phone', 'billing_email'
        );

        foreach ($billing_fields as $field) {
            if (isset($customer_data[$field])) {
                update_user_meta($user_id, $field, $customer_data[$field]);
            }
        }

        // Wings-specific metadata
        $wings_fields = array(
            'wings_customer_id', 'wings_customer_code', 'wings_pib', 'wings_mb',
            'wings_status', 'wings_sales_rep', 'wings_customer_class',
            'wings_discount', 'wings_credit_limit', 'wings_payment_terms',
            'wings_tolerance', 'wings_working_hours', 'wings_phone_2',
            'wings_phone_3', 'wings_last_sync'
        );

        foreach ($wings_fields as $field) {
            if (isset($customer_data[$field]) && $customer_data[$field] !== '') {
                update_user_meta($user_id, $field, $customer_data[$field]);
            }
        }
    }
}

// Display HTML interface if not AJAX request
if (!wp_doing_ajax() && !defined('WP_CLI')) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Wings API Customer Import - Real-time Progress</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .config-section { background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .progress-section { display: none; }
            .progress-bar { width: 100%; height: 30px; background: #e0e0e0; border-radius: 15px; overflow: hidden; margin: 10px 0; }
            .progress-fill { height: 100%; background: linear-gradient(90deg, #4CAF50, #45a049); transition: width 0.3s ease; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
            .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #007cba; }
            .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
            .stat-label { font-size: 12px; color: #666; text-transform: uppercase; }
            .log-container { height: 300px; overflow-y: auto; background: #1e1e1e; color: #00ff00; font-family: 'Courier New', monospace; padding: 15px; border-radius: 5px; font-size: 12px; }
            .log-entry { margin: 2px 0; }
            .log-time { color: #888; }
            .btn { padding: 12px 24px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn-primary { background: #007cba; color: white; }
            .btn-primary:hover { background: #005a87; }
            .btn-primary:disabled { background: #ccc; cursor: not-allowed; }
            .btn-danger { background: #dc3545; color: white; }
            .btn-success { background: #28a745; color: white; }
            .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
            .status-starting { background: #ffc107; }
            .status-processing { background: #007cba; animation: pulse 1s infinite; }
            .status-completed { background: #28a745; }
            .status-error { background: #dc3545; }
            @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
            .error-details { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
            .config-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
            .config-item { display: flex; flex-direction: column; }
            .config-item label { font-weight: bold; margin-bottom: 5px; }
            .config-item input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Wings API Customer Import</h1>
                <p>Real-time import of ~2000 customers from Wings Portal API to WordPress</p>
            </div>

            <div class="config-section">
                <h3>📋 Configuration</h3>
                <div class="config-grid">
                    <div class="config-item">
                        <label>API URL:</label>
                        <input type="text" id="api_url" value="https://portal.wings.rs/api/v1/" readonly>
                    </div>
                    <div class="config-item">
                        <label>Alias:</label>
                        <input type="text" id="api_alias" value="grosstest">
                    </div>
                    <div class="config-item">
                        <label>Username:</label>
                        <input type="text" id="api_username" value="aql">
                    </div>
                    <div class="config-item">
                        <label>Password:</label>
                        <input type="password" id="api_password" value="grossaql">
                    </div>
                    <div class="config-item">
                        <label>Batch Size:</label>
                        <input type="number" id="batch_size" value="25" min="10" max="100">
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button id="start-import" class="btn btn-primary">🚀 Start Import</button>
                    <button id="stop-import" class="btn btn-danger" style="display: none;">⏹️ Stop Import</button>
                </div>
            </div>

            <div class="progress-section" id="progress-section">
                <h3>📊 Import Progress</h3>

                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <span class="status-indicator" id="status-indicator"></span>
                    <span id="status-text">Ready to start</span>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
                </div>
                <div style="text-align: center; margin-top: 5px;">
                    <span id="progress-text">0 / 0 customers processed (0%)</span>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="stat-total">0</div>
                        <div class="stat-label">Total Found</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="stat-processed">0</div>
                        <div class="stat-label">Processed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="stat-created">0</div>
                        <div class="stat-label">Created</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="stat-updated">0</div>
                        <div class="stat-label">Updated</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="stat-errors">0</div>
                        <div class="stat-label">Errors</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="stat-time">0s</div>
                        <div class="stat-label">Elapsed Time</div>
                    </div>
                </div>

                <div id="error-details" class="error-details" style="display: none;">
                    <h4>❌ Error Details:</h4>
                    <ul id="error-list"></ul>
                </div>

                <h4>📝 Live Log</h4>
                <div class="log-container" id="log-container">
                    <div class="log-entry">Ready to start import...</div>
                </div>
            </div>
        </div>

        <script>
            let importSession = null;
            let importInterval = null;
            let startTime = null;
            let isImporting = false;

            // DOM elements
            const startBtn = document.getElementById('start-import');
            const stopBtn = document.getElementById('stop-import');
            const progressSection = document.getElementById('progress-section');
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            const logContainer = document.getElementById('log-container');
            const errorDetails = document.getElementById('error-details');
            const errorList = document.getElementById('error-list');

            // Stat elements
            const statTotal = document.getElementById('stat-total');
            const statProcessed = document.getElementById('stat-processed');
            const statCreated = document.getElementById('stat-created');
            const statUpdated = document.getElementById('stat-updated');
            const statErrors = document.getElementById('stat-errors');
            const statTime = document.getElementById('stat-time');

            // Event listeners
            startBtn.addEventListener('click', startImport);
            stopBtn.addEventListener('click', stopImport);

            function startImport() {
                if (isImporting) return;

                isImporting = true;
                startBtn.disabled = true;
                startBtn.style.display = 'none';
                stopBtn.style.display = 'inline-block';
                progressSection.style.display = 'block';

                updateStatus('starting', 'Initializing import...');
                addLogEntry('🚀 Starting Wings API import...');

                startTime = Date.now();

                // Start import session
                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'wings_import_start',
                        nonce: '<?php echo wp_create_nonce('wings_import_nonce'); ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        importSession = data.data.session_id;
                        addLogEntry('✅ Import session created: ' + importSession);
                        processNextBatch();

                        // Start status polling
                        importInterval = setInterval(updateProgress, 2000);
                    } else {
                        addLogEntry('❌ Failed to start import: ' + data.data);
                        stopImport();
                    }
                })
                .catch(error => {
                    addLogEntry('❌ Network error: ' + error.message);
                    stopImport();
                });
            }

            function processNextBatch() {
                if (!isImporting || !importSession) return;

                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'wings_import_batch',
                        nonce: '<?php echo wp_create_nonce('wings_import_nonce'); ?>',
                        session_id: importSession
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const sessionData = data.data;
                        updateUI(sessionData);

                        if (sessionData.status === 'completed') {
                            addLogEntry('🎉 Import completed successfully!');
                            stopImport();
                        } else if (sessionData.status === 'error') {
                            addLogEntry('❌ Import failed with errors');
                            stopImport();
                        } else {
                            // Continue with next batch
                            setTimeout(processNextBatch, 1000);
                        }
                    } else {
                        addLogEntry('❌ Batch processing error: ' + data.data);
                        stopImport();
                    }
                })
                .catch(error => {
                    addLogEntry('❌ Network error during batch processing: ' + error.message);
                    stopImport();
                });
            }

            function updateProgress() {
                if (!isImporting || !importSession) return;

                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'wings_import_status',
                        nonce: '<?php echo wp_create_nonce('wings_import_nonce'); ?>',
                        session_id: importSession
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateUI(data.data);
                    }
                })
                .catch(error => {
                    console.error('Status update error:', error);
                });
            }

            function updateUI(sessionData) {
                // Update status
                updateStatus(sessionData.status, getStatusText(sessionData.status));

                // Update progress bar
                const percentage = sessionData.total_customers > 0
                    ? Math.round((sessionData.processed / sessionData.total_customers) * 100)
                    : 0;
                progressFill.style.width = percentage + '%';
                progressText.textContent = `${sessionData.processed} / ${sessionData.total_customers} customers processed (${percentage}%)`;

                // Update stats
                statTotal.textContent = sessionData.total_customers;
                statProcessed.textContent = sessionData.processed;
                statCreated.textContent = sessionData.created;
                statUpdated.textContent = sessionData.updated;
                statErrors.textContent = sessionData.errors;

                // Update elapsed time
                if (startTime) {
                    const elapsed = Math.round((Date.now() - startTime) / 1000);
                    statTime.textContent = formatTime(elapsed);
                }

                // Update log
                if (sessionData.log && sessionData.log.length > 0) {
                    const lastLogEntry = sessionData.log[sessionData.log.length - 1];
                    if (lastLogEntry && lastLogEntry.message) {
                        addLogEntry(`[${lastLogEntry.time}] ${lastLogEntry.message}`);
                    }
                }

                // Show error details if any
                if (sessionData.errors > 0 && sessionData.error_details) {
                    showErrorDetails(sessionData.error_details);
                }
            }

            function updateStatus(status, text) {
                statusIndicator.className = 'status-indicator status-' + status;
                statusText.textContent = text;
            }

            function getStatusText(status) {
                switch (status) {
                    case 'starting': return 'Initializing...';
                    case 'processing': return 'Processing customers...';
                    case 'completed': return 'Import completed successfully!';
                    case 'error': return 'Import failed with errors';
                    default: return 'Unknown status';
                }
            }

            function addLogEntry(message) {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.textContent = message;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;

                // Keep only last 100 entries
                while (logContainer.children.length > 100) {
                    logContainer.removeChild(logContainer.firstChild);
                }
            }

            function showErrorDetails(errors) {
                if (errors.length === 0) {
                    errorDetails.style.display = 'none';
                    return;
                }

                errorList.innerHTML = '';
                errors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });

                errorDetails.style.display = 'block';
            }

            function formatTime(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;

                if (hours > 0) {
                    return `${hours}h ${minutes}m ${secs}s`;
                } else if (minutes > 0) {
                    return `${minutes}m ${secs}s`;
                } else {
                    return `${secs}s`;
                }
            }

            function stopImport() {
                isImporting = false;

                if (importInterval) {
                    clearInterval(importInterval);
                    importInterval = null;
                }

                startBtn.disabled = false;
                startBtn.style.display = 'inline-block';
                stopBtn.style.display = 'none';

                addLogEntry('⏹️ Import stopped');
            }

            // Auto-refresh page every 30 minutes to prevent session timeout
            setTimeout(() => {
                if (!isImporting) {
                    location.reload();
                }
            }, 30 * 60 * 1000);
        </script>
    </body>
    </html>
    <?php
}
