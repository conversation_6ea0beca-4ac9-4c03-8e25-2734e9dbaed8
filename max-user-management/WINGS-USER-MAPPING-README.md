# Wings User Mapping - Mapiranje Korisnika

## Pregled

Ova funkcionalnost omogućava import korisnika iz Wings Portal-a u privremenu staging tabelu, gde možete pregledati i mapirati podatke pre kreiranja WordPress korisnika.

## Mapiranje Polja

Sledeća polja se mapiraju iz Wings Portal-a u WooCommerce format:

| Wings Polje | WooCommerce Polje | Opis |
|-------------|-------------------|------|
| Šifra | ID | Jedinstveni identifikator korisnika |
| Naziv | username | Korisničko ime |
| Adresa | address | Adresa korisnika |
| Mesto | city | Grad |
| Kontakt | first_name | Ime kontakt osobe |
| Telefon | phone | Glavni telefon |
| Fax | phone_2 | Dodatni telefon (fax) |
| Mobilni | phone_3 | Mobilni telefon |
| E-mail | email | Email adresa |
| Radno vreme | working_hours | Radno vreme (null) |
| Status | status | Status korisnika (null) |
| PIB | pib | PIB broj (null) |
| Rabat | discount | Rabat (null) |

## Nova Tabela: max_wings_users_staging

Kreirana je nova tabela `wp_max_wings_users_staging` sa sledećim poljima:

```sql
CREATE TABLE wp_max_wings_users_staging (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    wings_id varchar(50) NOT NULL COMMENT 'Šifra iz Wings-a',
    username varchar(100) NOT NULL COMMENT 'Naziv iz Wings-a',
    address text COMMENT 'Adresa iz Wings-a',
    city varchar(100) COMMENT 'Mesto iz Wings-a',
    first_name varchar(100) COMMENT 'Kontakt iz Wings-a',
    phone varchar(50) COMMENT 'Telefon iz Wings-a',
    phone_2 varchar(50) COMMENT 'Fax iz Wings-a',
    phone_3 varchar(50) COMMENT 'Mobilni iz Wings-a',
    email varchar(100) COMMENT 'E-mail iz Wings-a',
    working_hours text COMMENT 'Radno vreme iz Wings-a',
    status varchar(50) COMMENT 'Status iz Wings-a',
    pib varchar(50) COMMENT 'PIB iz Wings-a',
    discount varchar(50) COMMENT 'Rabat iz Wings-a',
    wp_user_id bigint(20) DEFAULT NULL COMMENT 'ID WordPress korisnika kada se kreira',
    sync_status varchar(20) DEFAULT 'pending' COMMENT 'Status sinhronizacije',
    created_date datetime DEFAULT CURRENT_TIMESTAMP,
    updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY wings_id (wings_id),
    KEY wp_user_id (wp_user_id),
    KEY sync_status (sync_status),
    KEY email (email),
    KEY username (username)
);
```

## Nova Klasa: MAX_Wings_User_Mapper

### Glavne Metode

#### `import_wings_customers_to_staging($start = 0, $limit = 100)`
- Importuje korisnike iz Wings Portal-a u staging tabelu
- Mapira polja prema definisanoj shemi
- Vraća statistike importa (imported, updated, errors)

#### `get_staging_users($status = null, $limit = 50, $offset = 0)`
- Dobija korisnike iz staging tabele
- Može filtrirati po statusu (pending, synced, error)
- Podržava paginaciju

#### `create_wp_user_from_staging($staging_id)`
- Kreira WordPress korisnika iz staging podataka
- Generiše placeholder email ako ne postoji
- Dodaje Wings meta podatke
- Ažurira staging status na 'synced'

#### `update_staging_user_status($staging_id, $status)`
- Ažurira status korisnika u staging tabeli

#### `delete_staging_user($staging_id)`
- Briše korisnika iz staging tabele

## Nova Admin Stranica: Wings Staging

Dostupna na: **MAX Users → Wings Staging**

### Funkcionalnosti

1. **Statistike**
   - Ukupno korisnika u staging tabeli
   - Broj korisnika na čekanju
   - Broj sinhronizovanih korisnika
   - Broj grešaka

2. **Import Kontrole**
   - Import iz Wings-a u staging tabelu
   - Osveži listu korisnika
   - Kreiraj sve korisnike na čekanju
   - Filter po statusu

3. **Lista Korisnika**
   - Prikaz svih korisnika iz staging tabele
   - Filtriranje po statusu
   - Paginacija
   - Akcije za pojedinačne korisnike

4. **Akcije po Korisniku**
   - Kreiraj WordPress korisnika
   - Obriši iz staging tabele

## Novi AJAX Handler-i

### `max_import_to_staging`
- Importuje korisnike iz Wings-a u staging tabelu
- Parametri: start, limit

### `max_get_staging_users`
- Dobija korisnike iz staging tabele
- Parametri: status, limit, offset

### `max_create_wp_user_from_staging`
- Kreira WordPress korisnika iz staging podataka
- Parametri: staging_id

### `max_delete_staging_user`
- Briše korisnika iz staging tabele
- Parametri: staging_id

### `max_update_staging_status`
- Ažurira status korisnika
- Parametri: staging_id, status

## Workflow Korišćenja

1. **Import u Staging**
   - Idite na MAX Users → Wings Staging
   - Kliknite "Import iz Wings-a u Staging"
   - Sistem će importovati korisnike u staging tabelu

2. **Pregled i Validacija**
   - Pregledajte importovane korisnike
   - Proverite mapiranje podataka
   - Filtrirajte po statusu ako je potrebno

3. **Kreiranje WordPress Korisnika**
   - Za pojedinačne korisnike: kliknite "Kreiraj WP korisnika"
   - Za sve na čekanju: kliknite "Kreiraj sve na čekanju"

4. **Upravljanje**
   - Obrišite nepotrebne korisnike iz staging tabele
   - Pratite statistike sinhronizacije

## Prednosti Ovog Pristupa

1. **Sigurnost** - Podaci se prvo importuju u staging tabelu
2. **Kontrola** - Možete pregledati podatke pre kreiranja korisnika
3. **Fleksibilnost** - Možete birati koje korisnike da kreirate
4. **Transparentnost** - Jasno mapiranje polja
5. **Praćenje** - Statistike i status sinhronizacije

## Napomene

- Korisnici bez email adrese dobijaju placeholder email: `wings_{ID}@placeholder.local`
- Korisnici bez username-a dobijaju automatski username: `wings_user_{ID}`
- Svi kreirani korisnici imaju ulogu 'customer'
- Wings specifični podaci se čuvaju kao user meta
- Staging tabela se ne briše automatski - omogućava ponovnu sinhronizaciju

## Test Fajl

Kreiran je test fajl `test-staging-functionality.php` koji testira sve funkcionalnosti bez potrebe za aktivnim WordPress okruženjem.
