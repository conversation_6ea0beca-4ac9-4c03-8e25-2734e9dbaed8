<?php
/**
 * MAX User Management Configuration
 * Centralized configuration and constants
 */

if (!defined('ABSPATH')) {
    exit;
}

class MAX_Config {
    
    // Plugin constants
    const PLUGIN_VERSION = '1.0.0';
    const PLUGIN_SLUG = 'max-user-management';
    const PLUGIN_TEXT_DOMAIN = 'max-user-management';
    
    // Database option keys
    const OPTION_SETTINGS = 'max_user_mgmt_settings';
    const OPTION_LOG = 'max_user_mgmt_log';
    const OPTION_VERSION = 'max_user_mgmt_version';
    
    // Wings API constants
    const WINGS_API_DEFAULT_URL = 'https://portal.wings.rs/api/v1/';
    const WINGS_API_DEFAULT_TIMEOUT = 60;
    const WINGS_API_DEFAULT_BATCH_SIZE = 50;
    
    // User management constants
    const DEFAULT_PASSWORD_LENGTH = 12;
    const DEFAULT_SESSION_TIMEOUT = 24; // hours
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_DURATION = 30; // minutes
    
    // Log constants
    const MAX_LOG_ENTRIES = 1000;
    const LOG_RETENTION_DAYS = 30;
    
    /**
     * Get default settings array
     *
     * @return array Default settings
     */
    public static function get_default_settings() {
        return array(
            'wings_api_url' => self::WINGS_API_DEFAULT_URL,
            'wings_api_alias' => '',
            'wings_api_username' => '',
            'wings_api_password' => '',
            'wings_api_timeout' => self::WINGS_API_DEFAULT_TIMEOUT,
            'auto_send_credentials' => true,
            'password_length' => self::DEFAULT_PASSWORD_LENGTH,
            'session_timeout' => self::DEFAULT_SESSION_TIMEOUT,
            'max_login_attempts' => self::MAX_LOGIN_ATTEMPTS,
            'login_lockout_duration' => self::LOGIN_LOCKOUT_DURATION,
            'enable_audit_log' => true,
            'log_retention_days' => self::LOG_RETENTION_DAYS,
            'auto_sync_users' => false,
            'sync_interval' => 'daily',
            'batch_size' => self::WINGS_API_DEFAULT_BATCH_SIZE
        );
    }
    
    /**
     * Get plugin setting
     *
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed Setting value
     */
    public static function get_setting($key, $default = null) {
        $default_settings = self::get_default_settings();
        $settings = get_option(self::OPTION_SETTINGS, $default_settings);

        if (isset($settings[$key])) {
            return $settings[$key];
        }

        // Return default from default_settings if available
        if ($default === null && isset($default_settings[$key])) {
            return $default_settings[$key];
        }

        return $default;
    }
    
    /**
     * Update plugin setting
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool Success
     */
    public static function update_setting($key, $value) {
        $default_settings = self::get_default_settings();
        $settings = get_option(self::OPTION_SETTINGS, $default_settings);
        $settings[$key] = $value;

        return update_option(self::OPTION_SETTINGS, $settings);
    }

    /**
     * Update multiple settings
     *
     * @param array $new_settings Array of settings to update
     * @return bool Success
     */
    public static function update_settings($new_settings) {
        $default_settings = self::get_default_settings();
        $settings = get_option(self::OPTION_SETTINGS, $default_settings);
        $settings = array_merge($settings, $new_settings);

        return update_option(self::OPTION_SETTINGS, $settings);
    }

    /**
     * Get all settings
     *
     * @return array All settings
     */
    public static function get_all_settings() {
        $default_settings = self::get_default_settings();
        return get_option(self::OPTION_SETTINGS, $default_settings);
    }

    /**
     * Reset settings to defaults
     *
     * @return bool Success
     */
    public static function reset_settings() {
        $default_settings = self::get_default_settings();
        return update_option(self::OPTION_SETTINGS, $default_settings);
    }
    
    /**
     * Get Wings API configuration
     * 
     * @return array Wings API config
     */
    public static function get_wings_api_config() {
        return array(
            'url' => self::get_setting('wings_api_url'),
            'alias' => self::get_setting('wings_api_alias'),
            'username' => self::get_setting('wings_api_username'),
            'password' => self::get_setting('wings_api_password'),
            'timeout' => self::get_setting('wings_api_timeout'),
            'batch_size' => self::get_setting('batch_size')
        );
    }
    
    /**
     * Validate Wings API configuration
     * 
     * @return bool|array True if valid, array of errors if invalid
     */
    public static function validate_wings_api_config() {
        $config = self::get_wings_api_config();
        $errors = array();
        
        if (empty($config['url']) || !filter_var($config['url'], FILTER_VALIDATE_URL)) {
            $errors[] = __('Valjan Wings API URL je obavezan.', self::PLUGIN_TEXT_DOMAIN);
        }
        
        if (empty($config['alias'])) {
            $errors[] = __('Wings API alias je obavezan.', self::PLUGIN_TEXT_DOMAIN);
        }
        
        if (empty($config['username'])) {
            $errors[] = __('Wings API korisničko ime je obavezno.', self::PLUGIN_TEXT_DOMAIN);
        }
        
        if (empty($config['password'])) {
            $errors[] = __('Wings API lozinka je obavezna.', self::PLUGIN_TEXT_DOMAIN);
        }
        
        if (!is_numeric($config['timeout']) || $config['timeout'] < 10 || $config['timeout'] > 300) {
            $errors[] = __('Timeout mora biti između 10 i 300 sekundi.', self::PLUGIN_TEXT_DOMAIN);
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * Get security configuration
     * 
     * @return array Security config
     */
    public static function get_security_config() {
        return array(
            'password_length' => self::get_setting('password_length'),
            'max_login_attempts' => self::get_setting('max_login_attempts'),
            'login_lockout_duration' => self::get_setting('login_lockout_duration'),
            'session_timeout' => self::get_setting('session_timeout')
        );
    }
    
    /**
     * Get logging configuration
     * 
     * @return array Logging config
     */
    public static function get_logging_config() {
        return array(
            'enable_audit_log' => self::get_setting('enable_audit_log'),
            'log_retention_days' => self::get_setting('log_retention_days'),
            'max_log_entries' => self::MAX_LOG_ENTRIES
        );
    }
    
    /**
     * Get sync configuration
     * 
     * @return array Sync config
     */
    public static function get_sync_config() {
        return array(
            'auto_sync_users' => self::get_setting('auto_sync_users'),
            'sync_interval' => self::get_setting('sync_interval'),
            'batch_size' => self::get_setting('batch_size')
        );
    }
    
    /**
     * Check if configuration is complete
     * 
     * @return bool True if configuration is complete
     */
    public static function is_configured() {
        $wings_config = self::get_wings_api_config();
        
        return !empty($wings_config['url']) && 
               !empty($wings_config['alias']) && 
               !empty($wings_config['username']) && 
               !empty($wings_config['password']);
    }
    
    /**
     * Get plugin capabilities
     * 
     * @return array Plugin capabilities
     */
    public static function get_plugin_capabilities() {
        return array(
            'manage_max_users' => __('Upravljanje MAX korisnicima', self::PLUGIN_TEXT_DOMAIN),
            'view_max_reports' => __('Pregled MAX izveštaja', self::PLUGIN_TEXT_DOMAIN),
            'configure_max_settings' => __('Konfiguracija MAX podešavanja', self::PLUGIN_TEXT_DOMAIN),
            'sync_max_users' => __('Sinhronizacija MAX korisnika', self::PLUGIN_TEXT_DOMAIN)
        );
    }
    
    /**
     * Get environment information
     * 
     * @return array Environment info
     */
    public static function get_environment_info() {
        return array(
            'plugin_version' => self::PLUGIN_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'php_version' => PHP_VERSION,
            'mysql_version' => $GLOBALS['wpdb']->db_version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'is_multisite' => is_multisite(),
            'active_plugins' => get_option('active_plugins', array()),
            'current_theme' => wp_get_theme()->get('Name')
        );
    }
    
    /**
     * Export configuration for backup
     * 
     * @return array Configuration backup
     */
    public static function export_configuration() {
        return array(
            'version' => self::PLUGIN_VERSION,
            'exported_at' => current_time('mysql'),
            'settings' => self::get_all_settings(),
            'environment' => self::get_environment_info()
        );
    }
    
    /**
     * Import configuration from backup
     * 
     * @param array $config Configuration to import
     * @return bool|WP_Error Success or error
     */
    public static function import_configuration($config) {
        if (!is_array($config) || !isset($config['settings'])) {
            return new WP_Error('invalid_config', __('Nevaljan format konfiguracije.', self::PLUGIN_TEXT_DOMAIN));
        }
        
        // Validate imported settings
        $settings = $config['settings'];
        $default_settings = self::get_default_settings();
        foreach ($default_settings as $key => $default_value) {
            if (!isset($settings[$key])) {
                $settings[$key] = $default_value;
            }
        }
        
        $result = update_option(self::OPTION_SETTINGS, $settings);
        
        if ($result) {
            MAX_User_Management::log('Configuration imported successfully', 'info');
            return true;
        } else {
            return new WP_Error('import_failed', __('Neuspešan uvoz konfiguracije.', self::PLUGIN_TEXT_DOMAIN));
        }
    }
    
    /**
     * Get configuration validation rules
     * 
     * @return array Validation rules
     */
    public static function get_validation_rules() {
        return array(
            'wings_api_url' => array(
                'required' => true,
                'type' => 'url',
                'message' => __('Valjan Wings API URL je obavezan.', self::PLUGIN_TEXT_DOMAIN)
            ),
            'wings_api_alias' => array(
                'required' => true,
                'type' => 'string',
                'min_length' => 2,
                'message' => __('Wings API alias je obavezan (minimum 2 karaktera).', self::PLUGIN_TEXT_DOMAIN)
            ),
            'wings_api_username' => array(
                'required' => true,
                'type' => 'string',
                'min_length' => 3,
                'message' => __('Wings API korisničko ime je obavezno (minimum 3 karaktera).', self::PLUGIN_TEXT_DOMAIN)
            ),
            'wings_api_password' => array(
                'required' => true,
                'type' => 'string',
                'min_length' => 6,
                'message' => __('Wings API lozinka je obavezna (minimum 6 karaktera).', self::PLUGIN_TEXT_DOMAIN)
            ),
            'wings_api_timeout' => array(
                'required' => true,
                'type' => 'integer',
                'min' => 10,
                'max' => 300,
                'message' => __('Timeout mora biti između 10 i 300 sekundi.', self::PLUGIN_TEXT_DOMAIN)
            ),
            'password_length' => array(
                'required' => true,
                'type' => 'integer',
                'min' => 8,
                'max' => 50,
                'message' => __('Dužina lozinke mora biti između 8 i 50 karaktera.', self::PLUGIN_TEXT_DOMAIN)
            ),
            'max_login_attempts' => array(
                'required' => true,
                'type' => 'integer',
                'min' => 3,
                'max' => 10,
                'message' => __('Maksimalni broj pokušaja prijave mora biti između 3 i 10.', self::PLUGIN_TEXT_DOMAIN)
            ),
            'batch_size' => array(
                'required' => true,
                'type' => 'integer',
                'min' => 10,
                'max' => 200,
                'message' => __('Veličina batch-a mora biti između 10 i 200.', self::PLUGIN_TEXT_DOMAIN)
            )
        );
    }
}
?>
