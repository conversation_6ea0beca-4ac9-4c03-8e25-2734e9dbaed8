<?php
/**
 * MAX User Roles Management
 * Simplified and optimized version with constants, validation, and DRY principles
 */

if (!defined('ABSPATH')) {
    exit;
}

class MAX_User_Roles {

    // Constants for role management
    const ROLE_NAME = 'max_professionals';
    const ROLE_DISPLAY_NAME = 'MAX Professionals';

    // Custom capabilities constants
    const CAP_VIEW_PROFESSIONAL_PRICES = 'view_professional_prices';
    const CAP_ACCESS_PROFESSIONAL_CONTENT = 'access_professional_content';
    const CAP_PLACE_PROFESSIONAL_ORDERS = 'place_professional_orders';
    const CAP_VIEW_ORDER_HISTORY = 'view_order_history';
    const CAP_DOWNLOAD_INVOICES = 'download_invoices';

    // User status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_PENDING = 'pending';
    const STATUS_SUSPENDED = 'suspended';

    // Meta keys constants
    const META_ACCOUNT_STATUS = 'max_account_status';
    const META_LAST_LOGIN = 'last_login';
    const META_LOGIN_COUNT = 'login_count';
    const META_WINGS_SYNC_STATUS = 'wings_sync_status';

    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', array($this, 'init_hooks'));
    }

    public function init_hooks() {
        // Prevent self-registration
        add_filter('wp_authenticate_user', array($this, 'check_user_status'), 10, 2);
        add_action('wp_login', array($this, 'log_user_login'), 10, 2);

        // Disable registration for non-admins
        if (!is_admin() || !current_user_can('manage_options')) {
            add_filter('pre_option_users_can_register', '__return_false');
        }
    }

    /**
     * Get custom capabilities for professionals
     *
     * @return array Custom capabilities
     */
    private static function get_custom_capabilities() {
        return array(
            self::CAP_VIEW_PROFESSIONAL_PRICES,
            self::CAP_ACCESS_PROFESSIONAL_CONTENT,
            self::CAP_PLACE_PROFESSIONAL_ORDERS,
            self::CAP_VIEW_ORDER_HISTORY,
            self::CAP_DOWNLOAD_INVOICES
        );
    }

    /**
     * Get base capabilities for professionals
     *
     * @return array Base capabilities
     */
    private static function get_base_capabilities() {
        return array(
            'read' => true,
            'read_product' => true
        );
    }

    /**
     * Create professionals role with minimal required capabilities
     */
    public static function create_professionals_role() {
        // Remove role if exists to recreate with updated capabilities
        remove_role(self::ROLE_NAME);

        // Combine base and custom capabilities
        $capabilities = array_merge(
            self::get_base_capabilities(),
            array_fill_keys(self::get_custom_capabilities(), true)
        );

        $result = add_role(
            self::ROLE_NAME,
            __(self::ROLE_DISPLAY_NAME, 'max-user-management'),
            $capabilities
        );

        if ($result) {
            MAX_User_Management::log('MAX Professionals role created successfully', 'info');
        } else {
            MAX_User_Management::log('Failed to create MAX Professionals role', 'error');
        }

        return $result;
    }

    /**
     * Create a new professional user
     *
     * @param array $user_data User data array
     * @return int|WP_Error User ID on success, WP_Error on failure
     */
    public static function create_professional_user($user_data) {
        // Validate required fields
        if (empty($user_data['user_login']) || empty($user_data['user_email'])) {
            return new WP_Error('missing_data', __('Username and email are required.', 'max-user-management'));
        }

        // Check if username already exists
        if (username_exists($user_data['user_login'])) {
            return new WP_Error('username_exists', __('Username already exists.', 'max-user-management'));
        }

        // Check if email already exists
        if (email_exists($user_data['user_email'])) {
            return new WP_Error('email_exists', __('Email address already exists.', 'max-user-management'));
        }

        // Generate password if not provided
        if (empty($user_data['user_pass'])) {
            $settings = get_option('max_user_mgmt_settings', array());
            $password_length = !empty($settings['password_length']) ? $settings['password_length'] : 12;
            $user_data['user_pass'] = wp_generate_password($password_length, false);
        }

        // Set role
        $user_data['role'] = self::ROLE_NAME;

        // Create user
        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            return $user_id;
        }

        // Add custom meta
        update_user_meta($user_id, 'max_account_status', 'active');
        update_user_meta($user_id, 'max_created_date', current_time('mysql'));
        update_user_meta($user_id, 'max_created_by', get_current_user_id());

        // Send credentials if enabled
        $settings = get_option('max_user_mgmt_settings', array());
        if (!empty($settings['auto_send_credentials'])) {
            self::send_user_credentials($user_id, $user_data['user_pass']);
        }

        // Log the creation
        MAX_User_Management::log(
            sprintf('Professional user created: %s (ID: %d)', $user_data['user_login'], $user_id),
            'info'
        );

        return $user_id;
    }

    /**
     * Send user credentials via email
     *
     * @param int $user_id User ID
     * @param string $password Plain text password
     * @return bool Success status
     */
    public static function send_user_credentials($user_id, $password) {
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }

        $subject = sprintf(__('Your account credentials for %s', 'max-user-management'), get_bloginfo('name'));

        $message = sprintf(
            __("Hello %s,\n\nYour professional account has been created.\n\nLogin details:\nUsername: %s\nPassword: %s\nLogin URL: %s\n\nBest regards,\n%s", 'max-user-management'),
            $user->display_name,
            $user->user_login,
            $password,
            wp_login_url(),
            get_bloginfo('name')
        );

        $sent = wp_mail($user->user_email, $subject, $message);

        if ($sent) {
            update_user_meta($user_id, 'max_credentials_sent', current_time('mysql'));
            MAX_User_Management::log(
                sprintf('Credentials sent to user: %s', $user->user_login),
                'info'
            );
        } else {
            MAX_User_Management::log(
                sprintf('Failed to send credentials to user: %s', $user->user_login),
                'error'
            );
        }

        return $sent;
    }

    /**
     * Get status error messages (DRY principle)
     *
     * @return array Status error configurations
     */
    private static function get_status_errors() {
        return array(
            self::STATUS_INACTIVE => array(
                'code' => 'inactive_account',
                'message' => __('Vaš nalog je deaktiviran. Molimo kontaktirajte administratora.', 'max-user-management')
            ),
            self::STATUS_PENDING => array(
                'code' => 'pending_account',
                'message' => __('Vaš nalog čeka odobrenje administratora.', 'max-user-management')
            ),
            self::STATUS_SUSPENDED => array(
                'code' => 'suspended_account',
                'message' => __('Vaš nalog je privremeno suspendovan.', 'max-user-management')
            )
        );
    }

    /**
     * Check if user has MAX professionals role
     *
     * @param WP_User $user User object
     * @return bool True if user has MAX professionals role
     */
    public static function is_max_professional($user) {
        return $user && in_array(self::ROLE_NAME, $user->roles);
    }

    /**
     * Get user account status
     *
     * @param int $user_id User ID
     * @return string User status
     */
    public static function get_user_status($user_id) {
        return get_user_meta($user_id, self::META_ACCOUNT_STATUS, true) ?: self::STATUS_ACTIVE;
    }

    /**
     * Set user account status with validation
     *
     * @param int $user_id User ID
     * @param string $status New status
     * @return bool Success
     */
    public static function set_user_status($user_id, $status) {
        $valid_statuses = array(self::STATUS_ACTIVE, self::STATUS_INACTIVE, self::STATUS_PENDING, self::STATUS_SUSPENDED);

        if (!in_array($status, $valid_statuses)) {
            return false;
        }

        $result = update_user_meta($user_id, self::META_ACCOUNT_STATUS, $status);

        if ($result) {
            MAX_User_Management::log("User {$user_id} status changed to {$status}", 'info');
        }

        return $result;
    }

    /**
     * Check user status on login (simplified with DRY principle)
     */
    public function check_user_status($user, $password = null) {
        if (is_wp_error($user) || !self::is_max_professional($user)) {
            return $user;
        }

        $status = self::get_user_status($user->ID);

        // Set default status if empty
        if (empty(get_user_meta($user->ID, self::META_ACCOUNT_STATUS, true))) {
            update_user_meta($user->ID, self::META_ACCOUNT_STATUS, self::STATUS_ACTIVE);
        }

        if ($status !== self::STATUS_ACTIVE) {
            $errors = self::get_status_errors();
            if (isset($errors[$status])) {
                return new WP_Error($errors[$status]['code'], $errors[$status]['message']);
            }
        }

        // Check Wings sync status
        $wings_status = get_user_meta($user->ID, self::META_WINGS_SYNC_STATUS, true);
        if ($wings_status === 'error') {
            MAX_User_Management::log('User login with Wings sync error: ' . $user->user_login, 'warning');
        }

        return $user;
    }

    /**
     * Log user login (using constants and DRY principle)
     */
    public function log_user_login($user_login, $user) {
        if (!self::is_max_professional($user)) {
            return;
        }

        // Update login metadata
        update_user_meta($user->ID, self::META_LAST_LOGIN, current_time('mysql'));

        $login_count = (int)get_user_meta($user->ID, self::META_LOGIN_COUNT, true) + 1;
        update_user_meta($user->ID, self::META_LOGIN_COUNT, $login_count);

        MAX_User_Management::log("Professional user logged in: {$user_login} (login #{$login_count})", 'info');
    }

    /**
     * Get user status options (using constants)
     */
    public static function get_status_options() {
        return array(
            self::STATUS_ACTIVE => __('Aktivan', 'max-user-management'),
            self::STATUS_INACTIVE => __('Neaktivan', 'max-user-management'),
            self::STATUS_PENDING => __('Na čekanju', 'max-user-management'),
            self::STATUS_SUSPENDED => __('Suspendovan', 'max-user-management')
        );
    }

    /**
     * Check if user can access professional features (using helper methods)
     */
    public static function can_access_professional_features($user_id) {
        $user = get_user_by('ID', $user_id);
        return self::is_max_professional($user) && self::get_user_status($user_id) === self::STATUS_ACTIVE;
    }

    /**
     * Validation layer for user data
     */
    public static function validate_user_data($user_data) {
        $errors = array();

        // Validate email
        if (empty($user_data['user_email']) || !is_email($user_data['user_email'])) {
            $errors[] = __('Valjan email je obavezan.', 'max-user-management');
        }

        // Validate name
        if (empty($user_data['first_name']) || empty($user_data['last_name'])) {
            $errors[] = __('Ime i prezime su obavezni.', 'max-user-management');
        }

        // Validate username
        if (empty($user_data['user_login'])) {
            $errors[] = __('Korisničko ime je obavezno.', 'max-user-management');
        } elseif (username_exists($user_data['user_login'])) {
            $errors[] = __('Korisničko ime već postoji.', 'max-user-management');
        }

        // Validate email uniqueness
        if (!empty($user_data['user_email']) && email_exists($user_data['user_email'])) {
            $errors[] = __('Email adresa već postoji.', 'max-user-management');
        }

        return empty($errors) ? true : $errors;
    }

    /**
     * Sanitize user data
     */
    public static function sanitize_user_data($user_data) {
        return array(
            'user_login' => sanitize_user($user_data['user_login'] ?? ''),
            'user_email' => sanitize_email($user_data['user_email'] ?? ''),
            'first_name' => sanitize_text_field($user_data['first_name'] ?? ''),
            'last_name' => sanitize_text_field($user_data['last_name'] ?? ''),
            'display_name' => sanitize_text_field($user_data['display_name'] ?? ''),
            'user_pass' => $user_data['user_pass'] ?? wp_generate_password(),
            'role' => self::ROLE_NAME
        );
    }



    /**
     * Remove role on plugin deactivation (using constants)
     */
    public static function remove_professionals_role() {
        remove_role(self::ROLE_NAME);
        MAX_User_Management::log('MAX Professionals role removed', 'info');
    }
}