<?php
/**
 * MAX User Management Admin Panel
 * Handles all admin interface functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class MAX_Admin_Panel {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // AJAX handlers
        add_action('wp_ajax_max_test_wings_connection', array($this, 'ajax_test_wings_connection'));
        add_action('wp_ajax_max_sync_users', array($this, 'ajax_sync_users'));
        add_action('wp_ajax_max_clear_log', array($this, 'ajax_clear_log'));
        add_action('wp_ajax_max_create_user', array($this, 'ajax_create_user'));
        add_action('wp_ajax_max_toggle_debug', array($this, 'ajax_toggle_debug'));
        add_action('wp_ajax_max_change_user_status', array($this, 'ajax_change_user_status'));
        add_action('wp_ajax_max_sync_single_user', array($this, 'ajax_sync_single_user'));
        add_action('wp_ajax_max_export_logs', array($this, 'ajax_export_logs'));
        add_action('wp_ajax_max_export_users', array($this, 'ajax_export_users'));
        add_action('wp_ajax_max_import_wings_customers', array($this, 'ajax_import_wings_customers'));
        add_action('wp_ajax_max_export_to_wings', array($this, 'ajax_export_to_wings'));
        add_action('wp_ajax_max_update_customer_email', array($this, 'ajax_update_customer_email'));

        // Novi AJAX handler-i za staging tabelu
        add_action('wp_ajax_max_import_to_staging', array($this, 'ajax_import_to_staging'));
        add_action('wp_ajax_max_get_staging_users', array($this, 'ajax_get_staging_users'));
        add_action('wp_ajax_max_create_wp_user_from_staging', array($this, 'ajax_create_wp_user_from_staging'));
        add_action('wp_ajax_max_delete_staging_user', array($this, 'ajax_delete_staging_user'));
        add_action('wp_ajax_max_update_staging_status', array($this, 'ajax_update_staging_status'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('MAX User Management', 'max-user-management'),
            __('MAX Users', 'max-user-management'),
            'manage_options',
            'max-user-management',
            array($this, 'admin_page'),
            'dashicons-admin-users',
            30
        );
        
        // Add submenus
        add_submenu_page(
            'max-user-management',
            __('Dashboard', 'max-user-management'),
            __('Dashboard', 'max-user-management'),
            'manage_options',
            'max-user-management',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'max-user-management',
            __('Settings', 'max-user-management'),
            __('Settings', 'max-user-management'),
            'manage_options',
            'max-user-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'max-user-management',
            __('User Management', 'max-user-management'),
            __('User Management', 'max-user-management'),
            'manage_options',
            'max-user-creator',
            array($this, 'user_management_page')
        );
        
        add_submenu_page(
            'max-user-management',
            __('Wings Sync', 'max-user-management'),
            __('Wings Sync', 'max-user-management'),
            'manage_options',
            'max-wings-sync',
            array($this, 'wings_sync_page')
        );

        add_submenu_page(
            'max-user-management',
            __('Wings Staging', 'max-user-management'),
            __('Wings Staging', 'max-user-management'),
            'manage_options',
            'max-wings-staging',
            array($this, 'wings_staging_page')
        );
        
        add_submenu_page(
            'max-user-management',
            __('System Info', 'max-user-management'),
            __('System Info', 'max-user-management'),
            'manage_options',
            'max-system-info',
            array($this, 'system_info_page')
        );

        // Add debug page
        add_submenu_page(
            'max-user-management',
            __('API Debug Test', 'max-user-management'),
            __('API Debug Test', 'max-user-management'),
            'manage_options',
            'max-debug-api-test',
            array($this, 'debug_api_test_page')
        );

        // Add connection test page
        add_submenu_page(
            'max-user-management',
            __('Connection Test', 'max-user-management'),
            __('Connection Test', 'max-user-management'),
            'manage_options',
            'max-connection-test',
            array($this, 'connection_test_page')
        );

        // Add AJAX test page
        add_submenu_page(
            'max-user-management',
            __('AJAX Import Test', 'max-user-management'),
            __('AJAX Import Test', 'max-user-management'),
            'manage_options',
            'max-test-ajax-import',
            array($this, 'ajax_test_page')
        );

        // Add Error Handling test page
        add_submenu_page(
            'max-user-management',
            __('Error Handling Test', 'max-user-management'),
            __('Error Handling Test', 'max-user-management'),
            'manage_options',
            'max-test-error-handling',
            array($this, 'error_handling_test_page')
        );

        // Add Debug Customer Data page
        add_submenu_page(
            'max-user-management',
            __('Debug Customer Data', 'max-user-management'),
            __('Debug Customer Data', 'max-user-management'),
            'manage_options',
            'max-debug-customer-data',
            array($this, 'debug_customer_data_page')
        );

        // Add email management page
        add_submenu_page(
            'max-user-management',
            __('Email Management', 'max-user-management'),
            __('Email Management', 'max-user-management'),
            'manage_options',
            'max-email-management',
            array($this, 'email_management_page')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        register_setting('max_user_mgmt_settings', 'max_user_mgmt_settings', array($this, 'sanitize_settings'));
        
        // Register settings sections
        add_settings_section(
            'max_wings_api_settings',
            __('Wings API Settings', 'max-user-management'),
            array($this, 'wings_api_section_callback'),
            'max_user_mgmt_settings'
        );
        
        add_settings_section(
            'max_user_settings',
            __('User Management Settings', 'max-user-management'),
            array($this, 'user_settings_section_callback'),
            'max_user_mgmt_settings'
        );
        
        add_settings_section(
            'max_debug_settings',
            __('Debug Settings', 'max-user-management'),
            array($this, 'debug_settings_section_callback'),
            'max_user_mgmt_settings'
        );
        
        // Register settings fields
        $this->register_settings_fields();
    }
    
    /**
     * Register settings fields
     */
    private function register_settings_fields() {
        // Wings API fields
        add_settings_field(
            'wings_api_url',
            __('Wings API URL', 'max-user-management'),
            array($this, 'text_field_callback'),
            'max_user_mgmt_settings',
            'max_wings_api_settings',
            array('field' => 'wings_api_url', 'placeholder' => 'https://portal.wings.rs/api/v1/')
        );
        
        add_settings_field(
            'wings_api_alias',
            __('Wings API Alias', 'max-user-management'),
            array($this, 'text_field_callback'),
            'max_user_mgmt_settings',
            'max_wings_api_settings',
            array('field' => 'wings_api_alias', 'placeholder' => 'grosstest')
        );
        
        add_settings_field(
            'wings_api_username',
            __('Wings API Username', 'max-user-management'),
            array($this, 'text_field_callback'),
            'max_user_mgmt_settings',
            'max_wings_api_settings',
            array('field' => 'wings_api_username')
        );
        
        add_settings_field(
            'wings_api_password',
            __('Wings API Password', 'max-user-management'),
            array($this, 'password_field_callback'),
            'max_user_mgmt_settings',
            'max_wings_api_settings',
            array('field' => 'wings_api_password')
        );
        
        // User management fields
        add_settings_field(
            'auto_send_credentials',
            __('Auto Send Credentials', 'max-user-management'),
            array($this, 'checkbox_field_callback'),
            'max_user_mgmt_settings',
            'max_user_settings',
            array('field' => 'auto_send_credentials', 'description' => __('Automatically send login credentials to new users', 'max-user-management'))
        );
        
        add_settings_field(
            'password_length',
            __('Password Length', 'max-user-management'),
            array($this, 'number_field_callback'),
            'max_user_mgmt_settings',
            'max_user_settings',
            array('field' => 'password_length', 'min' => 8, 'max' => 50)
        );
        
        // Debug settings
        add_settings_field(
            'enable_debug',
            __('Enable Debug Mode', 'max-user-management'),
            array($this, 'checkbox_field_callback'),
            'max_user_mgmt_settings',
            'max_debug_settings',
            array('field' => 'enable_debug', 'description' => __('Enable detailed logging and debug information', 'max-user-management'))
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'max-user') === false) {
            return;
        }
        
        wp_enqueue_style(
            'max-admin-style',
            MAX_USER_MGMT_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            MAX_USER_MGMT_VERSION
        );
        
        wp_enqueue_script(
            'max-admin-script',
            MAX_USER_MGMT_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            MAX_USER_MGMT_VERSION,
            true
        );
        
        wp_localize_script('max-admin-script', 'max_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('max_admin_nonce'),
            'strings' => array(
                'testing' => __('Testing...', 'max-user-management'),
                'success' => __('Success!', 'max-user-management'),
                'error' => __('Error!', 'max-user-management'),
                'loading' => __('Loading...', 'max-user-management'),
                'confirm_action' => __('Are you sure?', 'max-user-management'),
                'confirm_clear_log' => __('Are you sure you want to clear the log?', 'max-user-management'),
                'syncing' => __('Syncing...', 'max-user-management'),
                'importing' => __('Importing...', 'max-user-management'),
                'exporting' => __('Exporting...', 'max-user-management'),
                'confirm_delete' => __('Are you sure you want to delete this user?', 'max-user-management'),
                'import_progress' => __('Import in progress...', 'max-user-management'),
                'import_complete' => __('Import completed!', 'max-user-management')
            )
        ));
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'dashboard';
        include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/admin-display.php';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/settings-page.php';
    }
    
    /**
     * User management page
     */
    public function user_management_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/user-management-page.php';
    }
    
    /**
     * Wings sync page
     */
    public function wings_sync_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/wings-sync-page.php';
    }

    /**
     * Wings staging page
     */
    public function wings_staging_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/wings-staging-page.php';
    }

    /**
     * System info page
     */
    public function system_info_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/system-info-page.php';
    }

    /**
     * Debug API test page
     */
    public function debug_api_test_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'debug-api-test.php';
    }

    /**
     * Connection test page
     */
    public function connection_test_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'test-api-connection.php';
    }

    /**
     * AJAX test page
     */
    public function ajax_test_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'test-ajax-import.php';
    }

    /**
     * Error handling test page
     */
    public function error_handling_test_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'test-error-handling.php';
    }

    /**
     * Debug customer data page
     */
    public function debug_customer_data_page() {
        include MAX_USER_MGMT_PLUGIN_DIR . 'debug-customer-data.php';
    }

    /**
     * Email management page
     */
    public function email_management_page() {
        // Handle form submissions
        if (isset($_POST['update_emails']) && wp_verify_nonce($_POST['_wpnonce'], 'max_update_emails')) {
            $this->handle_email_updates();
        }

        // Get users with placeholder emails
        $users_needing_email = get_users(array(
            'meta_key' => 'wings_needs_email_update',
            'meta_value' => true,
            'number' => 50 // Limit for performance
        ));

        echo '<div class="wrap">';
        echo '<h1>' . __('Wings Email Management', 'max-user-management') . '</h1>';

        if (empty($users_needing_email)) {
            echo '<div class="notice notice-success"><p>' . __('All users have valid email addresses!', 'max-user-management') . '</p></div>';
        } else {
            echo '<div class="notice notice-info"><p>' . sprintf(__('Found %d users with placeholder emails that need updating.', 'max-user-management'), count($users_needing_email)) . '</p></div>';

            echo '<form method="post" action="">';
            wp_nonce_field('max_update_emails');

            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . __('User', 'max-user-management') . '</th>';
            echo '<th>' . __('Current Email (Placeholder)', 'max-user-management') . '</th>';
            echo '<th>' . __('Wings ID', 'max-user-management') . '</th>';
            echo '<th>' . __('Company/Name', 'max-user-management') . '</th>';
            echo '<th>' . __('New Email Address', 'max-user-management') . '</th>';
            echo '<th>' . __('Action', 'max-user-management') . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';

            foreach ($users_needing_email as $user) {
                $wings_id = get_user_meta($user->ID, 'wings_customer_id', true);
                $company = get_user_meta($user->ID, 'billing_company', true);

                echo '<tr>';
                echo '<td><strong>' . esc_html($user->display_name) . '</strong><br><small>ID: ' . $user->ID . '</small></td>';
                echo '<td><code>' . esc_html($user->user_email) . '</code></td>';
                echo '<td>' . esc_html($wings_id) . '</td>';
                echo '<td>' . esc_html($company ?: $user->display_name) . '</td>';
                echo '<td><input type="email" name="new_emails[' . $user->ID . ']" class="regular-text" placeholder="Enter new email..." /></td>';
                echo '<td><label><input type="checkbox" name="update_user[' . $user->ID . ']" value="1" /> ' . __('Update', 'max-user-management') . '</label></td>';
                echo '</tr>';
            }

            echo '</tbody>';
            echo '</table>';

            echo '<p class="submit">';
            echo '<input type="submit" name="update_emails" class="button-primary" value="' . __('Update Selected Emails', 'max-user-management') . '" />';
            echo '</p>';

            echo '</form>';
        }

        echo '</div>';
    }

    /**
     * Handle email updates from the management page
     */
    private function handle_email_updates() {
        $updated_count = 0;
        $error_count = 0;
        $errors = array();

        if (!empty($_POST['update_user']) && !empty($_POST['new_emails'])) {
            foreach ($_POST['update_user'] as $user_id => $update) {
                if ($update && !empty($_POST['new_emails'][$user_id])) {
                    $new_email = sanitize_email($_POST['new_emails'][$user_id]);
                    $result = $this->update_customer_email($user_id, $new_email);

                    if (is_wp_error($result)) {
                        $error_count++;
                        $errors[] = sprintf(__('User ID %d: %s', 'max-user-management'), $user_id, $result->get_error_message());
                    } else {
                        $updated_count++;
                    }
                }
            }
        }

        // Show results
        if ($updated_count > 0) {
            echo '<div class="notice notice-success"><p>' . sprintf(__('Successfully updated %d email addresses.', 'max-user-management'), $updated_count) . '</p></div>';
        }

        if ($error_count > 0) {
            echo '<div class="notice notice-error"><p>' . sprintf(__('Failed to update %d email addresses:', 'max-user-management'), $error_count) . '</p>';
            echo '<ul>';
            foreach ($errors as $error) {
                echo '<li>' . esc_html($error) . '</li>';
            }
            echo '</ul></div>';
        }
    }

    /**
     * Update customer email address
     */
    private function update_customer_email($user_id, $new_email) {
        // Validate email
        if (!is_email($new_email)) {
            return new WP_Error('invalid_email', __('Invalid email address format.', 'max-user-management'));
        }

        // Check if email already exists (excluding current user)
        $existing_user = get_user_by('email', $new_email);
        if ($existing_user && $existing_user->ID != $user_id) {
            return new WP_Error('email_exists', __('Email address already exists for another user.', 'max-user-management'));
        }

        // Get current user data
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return new WP_Error('user_not_found', __('User not found.', 'max-user-management'));
        }

        $old_email = $user->user_email;

        // Update user email
        $result = wp_update_user(array(
            'ID' => $user_id,
            'user_email' => $new_email
        ));

        if (is_wp_error($result)) {
            return $result;
        }

        // Update WooCommerce customer email if WooCommerce is active
        if (class_exists('WC_Customer')) {
            $wc_customer = new WC_Customer($user_id);
            $wc_customer->set_email($new_email);
            $wc_customer->set_billing_email($new_email);
            $wc_customer->save();
        }

        // Remove placeholder flags
        delete_user_meta($user_id, 'wings_placeholder_email');
        delete_user_meta($user_id, 'wings_needs_email_update');

        // Add update tracking
        update_user_meta($user_id, 'wings_email_updated', current_time('mysql'));
        update_user_meta($user_id, 'wings_email_updated_from', $old_email);
        update_user_meta($user_id, 'wings_email_updated_to', $new_email);

        // Log the update
        MAX_User_Management::log('Email updated for user', 'info', array(
            'user_id' => $user_id,
            'old_email' => $old_email,
            'new_email' => $new_email,
            'display_name' => $user->display_name
        ));

        return true;
    }

    /**
     * Get statistics about placeholder emails
     */
    public function get_placeholder_email_stats() {
        global $wpdb;

        $total_placeholder = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = 'wings_placeholder_email' AND meta_value = '1'"
        );

        $total_updated = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = 'wings_email_updated'"
        );

        return array(
            'total_placeholder' => (int) $total_placeholder,
            'total_updated' => (int) $total_updated,
            'remaining' => (int) $total_placeholder - (int) $total_updated
        );
    }
    
    /**
     * Settings section callbacks
     */
    public function wings_api_section_callback() {
        echo '<p>' . __('Configure Wings Portal API connection settings.', 'max-user-management') . '</p>';
    }
    
    public function user_settings_section_callback() {
        echo '<p>' . __('Configure user management and creation settings.', 'max-user-management') . '</p>';
    }
    
    public function debug_settings_section_callback() {
        echo '<p>' . __('Debug and logging settings for troubleshooting.', 'max-user-management') . '</p>';
    }
    
    /**
     * Field callbacks
     */
    public function text_field_callback($args) {
        $settings = get_option('max_user_mgmt_settings', array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';
        
        echo "<input type='text' name='max_user_mgmt_settings[{$args['field']}]' value='" . esc_attr($value) . "' placeholder='" . esc_attr($placeholder) . "' class='regular-text' />";
    }
    
    public function password_field_callback($args) {
        $settings = get_option('max_user_mgmt_settings', array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';
        
        echo "<input type='password' name='max_user_mgmt_settings[{$args['field']}]' value='" . esc_attr($value) . "' class='regular-text' />";
    }
    
    public function checkbox_field_callback($args) {
        $settings = get_option('max_user_mgmt_settings', array());
        $checked = isset($settings[$args['field']]) ? $settings[$args['field']] : false;
        $description = isset($args['description']) ? $args['description'] : '';
        
        echo "<input type='checkbox' name='max_user_mgmt_settings[{$args['field']}]' value='1' " . checked(1, $checked, false) . " />";
        if ($description) {
            echo "<p class='description'>{$description}</p>";
        }
    }
    
    public function number_field_callback($args) {
        $settings = get_option('max_user_mgmt_settings', array());
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';
        $min = isset($args['min']) ? $args['min'] : '';
        $max = isset($args['max']) ? $args['max'] : '';
        
        echo "<input type='number' name='max_user_mgmt_settings[{$args['field']}]' value='" . esc_attr($value) . "' min='" . esc_attr($min) . "' max='" . esc_attr($max) . "' class='small-text' />";
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        if (isset($input['wings_api_url'])) {
            $sanitized['wings_api_url'] = esc_url_raw($input['wings_api_url']);
        }
        
        if (isset($input['wings_api_alias'])) {
            $sanitized['wings_api_alias'] = sanitize_text_field($input['wings_api_alias']);
        }
        
        if (isset($input['wings_api_username'])) {
            $sanitized['wings_api_username'] = sanitize_text_field($input['wings_api_username']);
        }
        
        if (isset($input['wings_api_password'])) {
            $sanitized['wings_api_password'] = sanitize_text_field($input['wings_api_password']);
        }
        
        if (isset($input['auto_send_credentials'])) {
            $sanitized['auto_send_credentials'] = (bool) $input['auto_send_credentials'];
        }
        
        if (isset($input['password_length'])) {
            $sanitized['password_length'] = absint($input['password_length']);
        }
        
        if (isset($input['enable_debug'])) {
            $sanitized['enable_debug'] = (bool) $input['enable_debug'];
        }
        
        return $sanitized;
    }
    
    /**
     * AJAX: Test Wings connection
     */
    public function ajax_test_wings_connection() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }

        if (class_exists('MAX_Wings_API')) {
            $api = MAX_Wings_API::get_instance();
            $result = $api->test_connection();

            if (is_wp_error($result)) {
                wp_send_json_error(array(
                    'message' => $result->get_error_message()
                ));
            } else {
                wp_send_json_success(array(
                    'message' => __('Connection successful!', 'max-user-management'),
                    'data' => $result
                ));
            }
        } else {
            wp_send_json_error(array(
                'message' => __('Wings API class not found.', 'max-user-management')
            ));
        }
    }
    
    /**
     * AJAX: Sync users
     */
    public function ajax_sync_users() {
        check_ajax_referer('max_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }
        
        // Implementation for user sync
        wp_send_json_success(array(
            'message' => __('User sync completed!', 'max-user-management')
        ));
    }
    
    /**
     * AJAX: Clear log
     */
    public function ajax_clear_log() {
        check_ajax_referer('max_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }
        
        delete_option('max_user_mgmt_log');
        
        wp_send_json_success(array(
            'message' => __('Log cleared successfully!', 'max-user-management')
        ));
    }
    
    /**
     * AJAX: Create user
     */
    public function ajax_create_user() {
        check_ajax_referer('max_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }
        
        $user_data = array(
            'user_login' => sanitize_text_field($_POST['username']),
            'user_email' => sanitize_email($_POST['email']),
            'first_name' => sanitize_text_field($_POST['first_name']),
            'last_name' => sanitize_text_field($_POST['last_name'])
        );
        
        if (class_exists('MAX_User_Roles')) {
            $result = MAX_User_Roles::create_professional_user($user_data);
            
            if (is_wp_error($result)) {
                wp_send_json_error(array(
                    'message' => $result->get_error_message()
                ));
            } else {
                wp_send_json_success(array(
                    'message' => __('User created successfully!', 'max-user-management'),
                    'user_id' => $result
                ));
            }
        } else {
            wp_send_json_error(array(
                'message' => __('User Roles class not found.', 'max-user-management')
            ));
        }
    }
    
    /**
     * AJAX: Toggle debug mode
     */
    public function ajax_toggle_debug() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }

        $settings = get_option('max_user_mgmt_settings', array());
        $settings['enable_debug'] = !empty($_POST['enable_debug']);
        update_option('max_user_mgmt_settings', $settings);

        wp_send_json_success(array(
            'message' => __('Debug mode updated!', 'max-user-management'),
            'debug_enabled' => $settings['enable_debug']
        ));
    }

    /**
     * AJAX: Change user status
     */
    public function ajax_change_user_status() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }

        $user_id = intval($_POST['user_id']);
        $status = sanitize_text_field($_POST['status']);

        if (!$user_id || !$status) {
            wp_send_json_error(array(
                'message' => __('Invalid user ID or status.', 'max-user-management')
            ));
        }

        update_user_meta($user_id, 'max_account_status', $status);

        wp_send_json_success(array(
            'message' => __('User status updated successfully!', 'max-user-management')
        ));
    }

    /**
     * AJAX: Sync single user
     */
    public function ajax_sync_single_user() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }

        $user_id = intval($_POST['user_id']);

        if (!$user_id) {
            wp_send_json_error(array(
                'message' => __('Invalid user ID.', 'max-user-management')
            ));
        }

        // Implementation for single user sync would go here
        update_user_meta($user_id, 'last_wings_sync', current_time('mysql'));
        update_user_meta($user_id, 'wings_sync_status', 'synced');

        wp_send_json_success(array(
            'message' => __('User synced successfully!', 'max-user-management')
        ));
    }

    /**
     * AJAX: Export logs
     */
    public function ajax_export_logs() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }

        $logs = get_option('max_user_mgmt_log', array());

        // Create CSV content
        $csv_content = "Timestamp,Level,Message,Context\n";
        foreach ($logs as $log) {
            $context = !empty($log['context']) ? json_encode($log['context']) : '';
            $csv_content .= sprintf(
                '"%s","%s","%s","%s"' . "\n",
                $log['timestamp'],
                $log['level'],
                str_replace('"', '""', $log['message']),
                str_replace('"', '""', $context)
            );
        }

        // Set headers for download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="max-user-management-logs-' . date('Y-m-d-H-i-s') . '.csv"');
        header('Content-Length: ' . strlen($csv_content));

        echo $csv_content;
        exit;
    }

    /**
     * AJAX: Export users
     */
    public function ajax_export_users() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'max-user-management'));
        }

        $users = get_users(array('role' => 'max_professionals'));

        // Create CSV content
        $csv_content = "ID,Username,Email,First Name,Last Name,Status,Wings ID,Last Login,Registered\n";
        foreach ($users as $user) {
            $status = get_user_meta($user->ID, 'max_account_status', true) ?: 'active';
            $wings_id = get_user_meta($user->ID, 'wings_customer_id', true);
            $last_login = get_user_meta($user->ID, 'last_login', true);

            $csv_content .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $user->ID,
                $user->user_login,
                $user->user_email,
                $user->first_name,
                $user->last_name,
                $status,
                $wings_id,
                $last_login ?: 'Never',
                $user->user_registered
            );
        }

        // Set headers for download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="max-professional-users-' . date('Y-m-d-H-i-s') . '.csv"');
        header('Content-Length: ' . strlen($csv_content));

        echo $csv_content;
        exit;
    }

    /**
     * AJAX: Import customers from Wings Portal
     */
    public function ajax_import_wings_customers() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        MAX_User_Management::log('Starting Wings customer import', 'info');

        try {
            // Get Wings API instance
            if (!class_exists('MAX_Wings_API')) {
                throw new Exception(__('Wings API class not found.', 'max-user-management'));
            }

            $api = MAX_Wings_API::get_instance();

            // Test connection first
            $connection_test = $api->test_connection();
            if (is_wp_error($connection_test)) {
                throw new Exception($connection_test->get_error_message());
            }

            MAX_User_Management::log('Wings API connection successful, starting customer import', 'info');

            // Get all customers from Wings
            $customers = $api->get_all_customers(0, 100); // Start with first 100

            if (is_wp_error($customers)) {
                throw new Exception($customers->get_error_message());
            }

            if (empty($customers) || !is_array($customers)) {
                throw new Exception(__('No customers found in Wings Portal.', 'max-user-management'));
            }

            MAX_User_Management::log('Retrieved ' . count($customers) . ' customers from Wings', 'info');

            $imported_count = 0;
            $skipped_count = 0;
            $error_count = 0;
            $import_results = array();

            foreach ($customers as $customer) {
                $result = $this->import_single_customer($customer);

                if ($result['success']) {
                    $imported_count++;
                    MAX_User_Management::log('Imported customer: ' . $result['email'], 'info');
                } elseif ($result['skipped']) {
                    $skipped_count++;
                    MAX_User_Management::log('Skipped existing customer: ' . $result['email'], 'debug');
                } else {
                    $error_count++;
                    MAX_User_Management::log('Failed to import customer: ' . $result['error'], 'error');
                }

                $import_results[] = $result;
            }

            // Update sync statistics
            $sync_stats = get_option('max_sync_stats', array());
            $sync_stats['last_sync'] = current_time('mysql');
            $sync_stats['total_synced'] = ($sync_stats['total_synced'] ?? 0) + $imported_count;
            if ($error_count > 0) {
                $sync_stats['sync_errors'] = ($sync_stats['sync_errors'] ?? 0) + $error_count;
                $sync_stats['last_error'] = sprintf(__('%d errors during import', 'max-user-management'), $error_count);
            }
            update_option('max_sync_stats', $sync_stats);

            $message = sprintf(
                __('Import completed: %d imported, %d skipped, %d errors', 'max-user-management'),
                $imported_count,
                $skipped_count,
                $error_count
            );

            MAX_User_Management::log('Wings customer import completed: ' . $message, 'info');

            wp_send_json_success(array(
                'message' => $message,
                'imported' => $imported_count,
                'skipped' => $skipped_count,
                'errors' => $error_count,
                'details' => $import_results
            ));

        } catch (Exception $e) {
            MAX_User_Management::log('Wings customer import failed: ' . $e->getMessage(), 'error');

            wp_send_json_error(array(
                'message' => sprintf(__('Import failed: %s', 'max-user-management'), $e->getMessage())
            ));
        }
    }

    /**
     * Import a single customer from Wings to WordPress/WooCommerce
     */
    private function import_single_customer($customer_data) {
        try {
            // Extract customer information from Wings data
            $email = $this->extract_customer_email($customer_data);
            $name = $this->extract_customer_name($customer_data);
            $company = $this->extract_customer_company($customer_data);
            $phone = $this->extract_customer_phone($customer_data);
            $address = $this->extract_customer_address($customer_data);
            $wings_id = $this->extract_customer_id($customer_data);

            $is_placeholder_email = false;
            if (empty($email)) {
                // Generate placeholder email instead of failing
                $wings_id = $this->extract_customer_id($customer_data);
                $company = $this->extract_customer_company($customer_data);
                $name = $this->extract_customer_name($customer_data);

                if (!empty($wings_id)) {
                    $email = "wings-customer-{$wings_id}@placeholder.local";
                } elseif (!empty($company)) {
                    $clean_company = sanitize_title($company);
                    $email = "{$clean_company}-" . time() . "@placeholder.local";
                } elseif (!empty($name)) {
                    $clean_name = sanitize_title($name);
                    $email = "{$clean_name}-" . time() . "@placeholder.local";
                } else {
                    $email = "wings-customer-" . time() . "@placeholder.local";
                }

                $is_placeholder_email = true;

                MAX_User_Management::log('Generated placeholder email for customer: ' . $email, 'info', array(
                    'wings_id' => $wings_id,
                    'company' => $company,
                    'name' => $name
                ));
            }

            // Check if user already exists
            $existing_user = get_user_by('email', $email);
            if ($existing_user) {
                // Update Wings ID if not set
                if ($wings_id && !get_user_meta($existing_user->ID, 'wings_customer_id', true)) {
                    update_user_meta($existing_user->ID, 'wings_customer_id', $wings_id);
                    update_user_meta($existing_user->ID, 'last_wings_sync', current_time('mysql'));
                    update_user_meta($existing_user->ID, 'wings_sync_status', 'synced');
                }

                return array(
                    'success' => false,
                    'skipped' => true,
                    'email' => $email,
                    'user_id' => $existing_user->ID,
                    'message' => __('User already exists', 'max-user-management')
                );
            }

            // Generate username from email
            $username = $this->generate_username_from_email($email);

            // Generate random password
            $password = wp_generate_password(12, false);

            // Create WordPress user
            $user_data = array(
                'user_login' => $username,
                'user_email' => $email,
                'user_pass' => $password,
                'display_name' => $name ?: $company ?: $email,
                'first_name' => $this->extract_first_name($name),
                'last_name' => $this->extract_last_name($name),
                'role' => 'max_professionals' // Assign to professional role
            );

            $user_id = wp_insert_user($user_data);

            if (is_wp_error($user_id)) {
                return array(
                    'success' => false,
                    'skipped' => false,
                    'email' => $email,
                    'error' => $user_id->get_error_message()
                );
            }

            // Add Wings-specific metadata
            if ($wings_id) {
                update_user_meta($user_id, 'wings_customer_id', $wings_id);
            }
            update_user_meta($user_id, 'last_wings_sync', current_time('mysql'));
            update_user_meta($user_id, 'wings_sync_status', 'synced');
            update_user_meta($user_id, 'wings_import_date', current_time('mysql'));

            // Add placeholder email tracking
            if ($is_placeholder_email) {
                update_user_meta($user_id, 'wings_placeholder_email', true);
                update_user_meta($user_id, 'wings_needs_email_update', true);
                update_user_meta($user_id, 'wings_original_data', json_encode($customer_data));
                update_user_meta($user_id, 'wings_placeholder_generated', current_time('mysql'));
            }

            // Add additional customer data
            if ($company) {
                update_user_meta($user_id, 'billing_company', $company);
            }
            if ($phone) {
                update_user_meta($user_id, 'billing_phone', $phone);
            }
            if ($address) {
                if (isset($address['street'])) {
                    update_user_meta($user_id, 'billing_address_1', $address['street']);
                }
                if (isset($address['city'])) {
                    update_user_meta($user_id, 'billing_city', $address['city']);
                }
                if (isset($address['postal_code'])) {
                    update_user_meta($user_id, 'billing_postcode', $address['postal_code']);
                }
                if (isset($address['country'])) {
                    update_user_meta($user_id, 'billing_country', $address['country']);
                }
            }

            // Create WooCommerce customer if WooCommerce is active
            if (class_exists('WC_Customer')) {
                $wc_customer = new WC_Customer($user_id);
                if ($company) {
                    $wc_customer->set_billing_company($company);
                }
                if ($phone) {
                    $wc_customer->set_billing_phone($phone);
                }
                $wc_customer->save();
            }

            return array(
                'success' => true,
                'skipped' => false,
                'email' => $email,
                'user_id' => $user_id,
                'username' => $username,
                'wings_id' => $wings_id,
                'message' => __('User created successfully', 'max-user-management')
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'skipped' => false,
                'email' => $email ?? 'unknown',
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Helper methods for customer data extraction
     */
    private function extract_customer_email($customer_data) {
        // Try different possible email field names from Wings API
        $email_fields = array('email', 'e_mail', 'mail', 'email_adresa', 'kontakt_email');

        foreach ($email_fields as $field) {
            if (!empty($customer_data[$field]) && is_email($customer_data[$field])) {
                return sanitize_email($customer_data[$field]);
            }
        }

        return '';
    }

    private function extract_customer_name($customer_data) {
        $name_fields = array('naziv', 'name', 'ime', 'full_name', 'display_name');

        foreach ($name_fields as $field) {
            if (!empty($customer_data[$field])) {
                return sanitize_text_field($customer_data[$field]);
            }
        }

        return '';
    }

    private function extract_customer_company($customer_data) {
        $company_fields = array('kompanija', 'company', 'firma', 'naziv_firme', 'naziv');

        foreach ($company_fields as $field) {
            if (!empty($customer_data[$field])) {
                return sanitize_text_field($customer_data[$field]);
            }
        }

        return '';
    }

    private function extract_customer_phone($customer_data) {
        $phone_fields = array('telefon', 'phone', 'tel', 'kontakt_telefon', 'mobile');

        foreach ($phone_fields as $field) {
            if (!empty($customer_data[$field])) {
                return sanitize_text_field($customer_data[$field]);
            }
        }

        return '';
    }

    private function extract_customer_address($customer_data) {
        $address = array();

        // Street address
        $street_fields = array('adresa', 'address', 'ulica', 'street');
        foreach ($street_fields as $field) {
            if (!empty($customer_data[$field])) {
                $address['street'] = sanitize_text_field($customer_data[$field]);
                break;
            }
        }

        // City
        $city_fields = array('mesto', 'city', 'grad', 'town');
        foreach ($city_fields as $field) {
            if (!empty($customer_data[$field])) {
                $address['city'] = sanitize_text_field($customer_data[$field]);
                break;
            }
        }

        // Postal code
        $postal_fields = array('postanski_broj', 'postal_code', 'zip', 'postcode');
        foreach ($postal_fields as $field) {
            if (!empty($customer_data[$field])) {
                $address['postal_code'] = sanitize_text_field($customer_data[$field]);
                break;
            }
        }

        // Country
        $country_fields = array('zemlja', 'country', 'drzava');
        foreach ($country_fields as $field) {
            if (!empty($customer_data[$field])) {
                $address['country'] = sanitize_text_field($customer_data[$field]);
                break;
            }
        }

        return !empty($address) ? $address : null;
    }

    private function extract_customer_id($customer_data) {
        $id_fields = array('id', 'customer_id', 'kupac_id', 'wings_id');

        foreach ($id_fields as $field) {
            if (!empty($customer_data[$field])) {
                return sanitize_text_field($customer_data[$field]);
            }
        }

        return '';
    }

    private function extract_first_name($full_name) {
        if (empty($full_name)) {
            return '';
        }

        $parts = explode(' ', trim($full_name));
        return sanitize_text_field($parts[0]);
    }

    private function extract_last_name($full_name) {
        if (empty($full_name)) {
            return '';
        }

        $parts = explode(' ', trim($full_name));
        if (count($parts) > 1) {
            array_shift($parts); // Remove first name
            return sanitize_text_field(implode(' ', $parts));
        }

        return '';
    }

    private function generate_username_from_email($email) {
        $username = sanitize_user(substr($email, 0, strpos($email, '@')));

        // Ensure username is unique
        $original_username = $username;
        $counter = 1;

        while (username_exists($username)) {
            $username = $original_username . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * AJAX: Export users to Wings Portal
     */
    public function ajax_export_to_wings() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        // This is a placeholder for export functionality
        // Implementation would depend on Wings Portal API for creating customers

        wp_send_json_error(array(
            'message' => __('Export to Wings functionality is not yet implemented.', 'max-user-management')
        ));
    }

    /**
     * AJAX: Update customer email
     */
    public function ajax_update_customer_email() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        $user_id = intval($_POST['user_id']);
        $new_email = sanitize_email($_POST['new_email']);

        if (!$user_id || !$new_email) {
            wp_send_json_error(array(
                'message' => __('Invalid user ID or email address.', 'max-user-management')
            ));
        }

        $result = $this->update_customer_email($user_id, $new_email);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        }

        wp_send_json_success(array(
            'message' => __('Email address updated successfully!', 'max-user-management'),
            'user_id' => $user_id,
            'new_email' => $new_email
        ));
    }

    /**
     * AJAX: Import Wings customers to staging table
     */
    public function ajax_import_to_staging() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        $start = intval($_POST['start'] ?? 0);
        $limit = intval($_POST['limit'] ?? 100);

        $mapper = MAX_Wings_User_Mapper::get_instance();
        $result = $mapper->import_wings_customers_to_staging($start, $limit);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        }

        wp_send_json_success($result);
    }

    /**
     * AJAX: Get staging users
     */
    public function ajax_get_staging_users() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        $status = sanitize_text_field($_POST['status'] ?? '');
        $limit = intval($_POST['limit'] ?? 50);
        $offset = intval($_POST['offset'] ?? 0);

        $mapper = MAX_Wings_User_Mapper::get_instance();
        $users = $mapper->get_staging_users($status ?: null, $limit, $offset);
        $total = $mapper->get_staging_users_count($status ?: null);

        wp_send_json_success(array(
            'users' => $users,
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset
        ));
    }

    /**
     * AJAX: Create WordPress user from staging data
     */
    public function ajax_create_wp_user_from_staging() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        $staging_id = intval($_POST['staging_id'] ?? 0);

        if (!$staging_id) {
            wp_send_json_error(array(
                'message' => __('Invalid staging ID.', 'max-user-management')
            ));
        }

        $mapper = MAX_Wings_User_Mapper::get_instance();
        $result = $mapper->create_wp_user_from_staging($staging_id);

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message()
            ));
        }

        wp_send_json_success(array(
            'message' => __('WordPress user created successfully!', 'max-user-management'),
            'wp_user_id' => $result
        ));
    }

    /**
     * AJAX: Delete staging user
     */
    public function ajax_delete_staging_user() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        $staging_id = intval($_POST['staging_id'] ?? 0);

        if (!$staging_id) {
            wp_send_json_error(array(
                'message' => __('Invalid staging ID.', 'max-user-management')
            ));
        }

        $mapper = MAX_Wings_User_Mapper::get_instance();
        $result = $mapper->delete_staging_user($staging_id);

        if ($result === false) {
            wp_send_json_error(array(
                'message' => __('Failed to delete staging user.', 'max-user-management')
            ));
        }

        wp_send_json_success(array(
            'message' => __('Staging user deleted successfully!', 'max-user-management')
        ));
    }

    /**
     * AJAX: Update staging user status
     */
    public function ajax_update_staging_status() {
        check_ajax_referer('max_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'max-user-management')
            ));
        }

        $staging_id = intval($_POST['staging_id'] ?? 0);
        $status = sanitize_text_field($_POST['status'] ?? '');

        if (!$staging_id || !$status) {
            wp_send_json_error(array(
                'message' => __('Invalid staging ID or status.', 'max-user-management')
            ));
        }

        $mapper = MAX_Wings_User_Mapper::get_instance();
        $result = $mapper->update_staging_user_status($staging_id, $status);

        if ($result === false) {
            wp_send_json_error(array(
                'message' => __('Failed to update staging user status.', 'max-user-management')
            ));
        }

        wp_send_json_success(array(
            'message' => __('Staging user status updated successfully!', 'max-user-management')
        ));
    }
}
?>
