<?php
/**
 * MAX Wings API Client
 * Handles communication with Wings Portal API
 */

if (!defined('ABSPATH')) {
    exit;
}

class MAX_Wings_API {
    
    private static $instance = null;
    private $api_url;
    private $api_alias;
    private $api_username;
    private $api_password;
    private $session_token;
    private $settings;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->load_settings();
    }

    private function load_settings() {
        // Get settings from the array option (used by settings page)
        $settings_array = get_option('max_user_mgmt_settings', array());

        // Also check individual options (for backward compatibility)
        $individual_settings = array(
            'api_url' => get_option('max_user_mgmt_wings_api_url', ''),
            'api_alias' => get_option('max_user_mgmt_wings_api_alias', ''),
            'api_username' => get_option('max_user_mgmt_wings_api_username', ''),
            'api_password' => get_option('max_user_mgmt_wings_api_password', ''),
        );

        // Merge settings, preferring array settings over individual ones
        $this->settings = array(
            'api_url' => $settings_array['wings_api_url'] ?? $individual_settings['api_url'] ?: 'https://portal.wings.rs/api/v1/',
            'api_alias' => $settings_array['wings_api_alias'] ?? $individual_settings['api_alias'] ?: '',
            'api_username' => $settings_array['wings_api_username'] ?? $individual_settings['api_username'] ?: '',
            'api_password' => $settings_array['wings_api_password'] ?? $individual_settings['api_password'] ?: '',
            'timeout' => $settings_array['wings_api_timeout'] ?? 60
        );

        $this->api_url = rtrim($this->settings['api_url'], '/') . '/';
        $this->api_alias = $this->settings['api_alias'];
        $this->api_username = $this->settings['api_username'];
        $this->api_password = $this->settings['api_password'];

        // Log loaded settings for debugging
        MAX_User_Management::log('Wings API settings loaded', 'debug', array(
            'api_url' => $this->api_url,
            'api_alias' => $this->api_alias,
            'username_set' => !empty($this->api_username),
            'password_set' => !empty($this->api_password),
            'settings_source' => !empty($settings_array) ? 'array' : 'individual'
        ));
    }



    /**
     * Login to Wings API with enhanced debugging
     */
    public function login() {
        MAX_User_Management::log('Starting Wings API login attempt', 'debug', array(
            'api_url' => $this->api_url,
            'api_alias' => $this->api_alias,
            'username_set' => !empty($this->api_username),
            'password_set' => !empty($this->api_password)
        ));

        // Validate required settings (matching Wings WooCommerce sync validation)
        if (empty($this->api_alias)) {
            MAX_User_Management::log('Missing API alias', 'error');
            return new WP_Error('missing_alias', __('API alias nije podešen.', 'max-user-management'));
        }

        if (empty($this->api_username) || empty($this->api_password)) {
            MAX_User_Management::log('Missing API credentials', 'error', array(
                'username_empty' => empty($this->api_username),
                'password_empty' => empty($this->api_password)
            ));
            return new WP_Error('missing_credentials', __('API kredencijali nisu podešeni.', 'max-user-management'));
        }

        // Construct login URL using alias (matching Wings WooCommerce sync pattern)
        $login_url = $this->api_url . $this->api_alias . '/system.user.log';
        MAX_User_Management::log('Constructed login URL', 'debug', array('login_url' => $login_url));

        $body = array(
            'aUn' => $this->api_username,
            'aUp' => $this->api_password
        );

        $request_args = array(
            'timeout' => $this->settings['timeout'],
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => wp_json_encode($body)
        );

        MAX_User_Management::log('Sending login request', 'debug', array(
            'url' => $login_url,
            'timeout' => $this->settings['timeout'],
            'body_size' => strlen($request_args['body'])
        ));

        $response = wp_remote_post($login_url, $request_args);

        if (is_wp_error($response)) {
            MAX_User_Management::log('Wings API login request failed', 'error', array(
                'error_code' => $response->get_error_code(),
                'error_message' => $response->get_error_message()
            ));
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_headers = wp_remote_retrieve_headers($response);

        MAX_User_Management::log('Received login response', 'debug', array(
            'status_code' => $response_code,
            'body_length' => strlen($response_body),
            'headers' => $response_headers
        ));

        if ($response_code !== 200) {
            MAX_User_Management::log('Login failed with non-200 status', 'error', array(
                'status_code' => $response_code,
                'response_body' => substr($response_body, 0, 500)
            ));
            return new WP_Error('login_failed', sprintf(__('Login neuspešan. Status kod: %d', 'max-user-management'), $response_code));
        }

        $data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            MAX_User_Management::log('Invalid JSON in login response', 'error', array(
                'json_error' => json_last_error_msg(),
                'response_body' => substr($response_body, 0, 500)
            ));
            return new WP_Error('invalid_response', __('Nevaljan JSON odgovor od Wings API', 'max-user-management'));
        }

        MAX_User_Management::log('Parsed login response JSON', 'debug', array(
            'data_structure' => array_keys($data),
            'has_data_array' => isset($data['data']),
            'data_count' => isset($data['data']) ? count($data['data']) : 0
        ));

        if (isset($data['data'][0]['attributes']['token'])) {
            $this->session_token = $data['data'][0]['attributes']['token'];
            MAX_User_Management::log('Uspešan Wings API login', 'info', array(
                'token_length' => strlen($this->session_token),
                'token_preview' => substr($this->session_token, 0, 10) . '...'
            ));
            return true;
        }

        MAX_User_Management::log('Token not found in response', 'error', array(
            'response_structure' => $data
        ));
        return new WP_Error('no_token', __('Token nije pronađen u odgovoru', 'max-user-management'));
    }

    /**
     * Get all customers from Wings
     */
    public function get_all_customers($start = 0, $limit = 100) {
        if (!$this->session_token && !$this->login()) {
            return new WP_Error('not_authenticated', __('Nije moguće autentifikovati se sa Wings API', 'max-user-management'));
        }

        $url = $this->api_url . $this->api_alias . '/local.kupac.svi';
        
        $body = array(
            'start' => $start,
            'limit' => $limit
        );

        $response = wp_remote_post($url, array(
            'timeout' => $this->settings['timeout'],
            'headers' => array(
                'Content-Type' => 'application/json',
                'Cookie' => 'PHPSESSID=' . $this->session_token
            ),
            'body' => wp_json_encode($body)
        ));

        if (is_wp_error($response)) {
            MAX_User_Management::log('Wings API kupac.lista greška: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            return new WP_Error('api_error', sprintf(__('API greška. Status kod: %d', 'max-user-management'), $response_code));
        }

        $data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('invalid_response', __('Nevaljan JSON odgovor', 'max-user-management'));
        }

        return isset($data['data']) ? $data['data'] : array();
    }

    /**
     * Create customer in Wings
     */
    public function create_customer($customer_data) {
        if (!$this->session_token && !$this->login()) {
            return new WP_Error('not_authenticated', __('Nije moguće autentifikovati se sa Wings API', 'max-user-management'));
        }

        $url = $this->api_url . $this->api_alias . '/local.kupac.nov';

        $response = wp_remote_post($url, array(
            'timeout' => $this->settings['timeout'],
            'headers' => array(
                'Content-Type' => 'application/json',
                'Cookie' => 'PHPSESSID=' . $this->session_token
            ),
            'body' => wp_json_encode($customer_data)
        ));

        if (is_wp_error($response)) {
            MAX_User_Management::log('Wings API kupac.dodaj greška: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            return new WP_Error('api_error', sprintf(__('API greška. Status kod: %d', 'max-user-management'), $response_code));
        }

        $data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('invalid_response', __('Nevaljan JSON odgovor', 'max-user-management'));
        }

        return $data;
    }

    /**
     * Get customer by ID from Wings
     */
    public function get_customer_by_id($customer_id) {
        if (!$this->session_token && !$this->login()) {
            return new WP_Error('not_authenticated', __('Nije moguće autentifikovati se sa Wings API', 'max-user-management'));
        }

        $url = $this->api_url . $this->api_alias . '/local.kupac.info';
        
        $body = array(
            'id' => $customer_id
        );

        $response = wp_remote_post($url, array(
            'timeout' => $this->settings['timeout'],
            'headers' => array(
                'Content-Type' => 'application/json',
                'Cookie' => 'PHPSESSID=' . $this->session_token
            ),
            'body' => wp_json_encode($body)
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            return new WP_Error('api_error', sprintf(__('API greška. Status kod: %d', 'max-user-management'), $response_code));
        }

        $data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('invalid_response', __('Nevaljan JSON odgovor', 'max-user-management'));
        }

        return isset($data['data'][0]) ? $data['data'][0] : null;
    }

    /**
     * Test API connection with comprehensive debugging
     */
    public function test_connection() {
        MAX_User_Management::log('=== Starting API Connection Test ===', 'info');

        // Log current settings (without sensitive data)
        MAX_User_Management::log('API Configuration', 'debug', array(
            'api_url' => $this->api_url,
            'api_alias' => $this->api_alias,
            'timeout' => $this->settings['timeout'],
            'username_configured' => !empty($this->api_username),
            'password_configured' => !empty($this->api_password)
        ));

        // Test login
        MAX_User_Management::log('Testing login...', 'debug');
        $result = $this->login();

        if (is_wp_error($result)) {
            MAX_User_Management::log('Login test failed', 'error', array(
                'error_code' => $result->get_error_code(),
                'error_message' => $result->get_error_message()
            ));
            return $result;
        }

        MAX_User_Management::log('Login successful, testing customer retrieval...', 'debug');

        // Connection successful - return success data (matching Wings WooCommerce sync format)
        $success_data = array(
            'success' => true,
            'message' => __('Konekcija uspešna!', 'max-user-management'),
            'token' => $this->session_token
        );

        MAX_User_Management::log('=== API Connection Test SUCCESSFUL ===', 'info', $success_data);

        return $success_data;
    }

    /**
     * Make API request with authentication (DRY principle implementation)
     */
    private function make_authenticated_request($endpoint, $body = array(), $method = 'POST') {
        // Ensure we're authenticated
        if (!$this->session_token && !$this->login()) {
            return new WP_Error('not_authenticated', __('Nije moguće autentifikovati se sa Wings API', 'max-user-management'));
        }

        return $this->make_api_request($endpoint, $body, $method);
    }

    /**
     * Make API request (base method with DRY principle)
     */
    private function make_api_request($endpoint, $body = array(), $method = 'POST') {
        $url = $this->api_url . $this->api_alias . '/' . $endpoint;

        $args = array(
            'timeout' => $this->settings['timeout'],
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'method' => $method
        );

        // Add session token if available (using Cookie format like Wings WooCommerce sync)
        if ($this->session_token) {
            $args['headers']['Cookie'] = 'PHPSESSID=' . $this->session_token;
        }

        if (!empty($body)) {
            $args['body'] = wp_json_encode($body);
        }

        $response = wp_remote_request($url, $args);

        return $this->process_api_response($response, $endpoint);
    }

    /**
     * Process API response (DRY principle)
     */
    private function process_api_response($response, $endpoint = '') {
        if (is_wp_error($response)) {
            MAX_User_Management::log("API request failed for {$endpoint}: " . $response->get_error_message(), 'error');
            return $response;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        if ($status_code !== 200) {
            MAX_User_Management::log("API returned status code {$status_code} for {$endpoint}", 'error');
            return new WP_Error('api_error', sprintf(__('API greška za %s: Status kod %d', 'max-user-management'), $endpoint, $status_code));
        }

        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            MAX_User_Management::log("Invalid JSON response from API for {$endpoint}: " . json_last_error_msg(), 'error');
            return new WP_Error('invalid_json', __('Nevaljan JSON odgovor od API-ja', 'max-user-management'));
        }

        return $data;
    }

    /**
     * Get API statistics
     */
    public function get_api_stats() {
        return array(
            'is_authenticated' => !empty($this->session_token),
            'api_url' => $this->api_url,
            'api_alias' => $this->api_alias,
            'timeout' => $this->settings['timeout'],
            'session_token_length' => $this->session_token ? strlen($this->session_token) : 0,
            'config_complete' => false // Simplified to prevent activation issues
        );
    }
}