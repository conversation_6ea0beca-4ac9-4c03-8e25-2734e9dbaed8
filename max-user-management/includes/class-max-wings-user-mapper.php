<?php
/**
 * MAX Wings User Mapper
 * Handles mapping of Wings Portal customers to WooCommerce users
 */

if (!defined('ABSPATH')) {
    exit;
}

class MAX_Wings_User_Mapper {
    
    private static $instance = null;
    private $wings_api;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->wings_api = MAX_Wings_API::get_instance();
    }
    
    /**
     * Mapiranje polja iz Wings-a u WooCommerce format
     * Šifra = ID, Naziv = username, Adresa = address, Mesto = city
     * Kontakt = first_name, Telefon = phone, Fax = phone_2, Mobilni = phone_3
     * E-mail = email, Radno vreme = null, Status = null, PIB = null, Rabat = null
     */
    private function map_wings_customer_to_staging($wings_customer) {
        $attributes = $wings_customer['attributes'] ?? array();
        
        return array(
            'wings_id' => $attributes['Šifra'] ?? $attributes['ID'] ?? '',
            'username' => $attributes['Naziv'] ?? '',
            'address' => $attributes['Adresa'] ?? '',
            'city' => $attributes['Mesto'] ?? '',
            'first_name' => $attributes['Kontakt'] ?? '',
            'phone' => $attributes['Telefon'] ?? '',
            'phone_2' => $attributes['Fax'] ?? '',
            'phone_3' => $attributes['Mobilni'] ?? '',
            'email' => $attributes['E-mail'] ?? '',
            'working_hours' => $attributes['Radno vreme'] ?? null,
            'status' => $attributes['Status'] ?? null,
            'pib' => $attributes['PIB'] ?? null,
            'discount' => $attributes['Rabat'] ?? null,
            'sync_status' => 'pending'
        );
    }
    
    /**
     * Import korisnika iz Wings-a u staging tabelu
     */
    public function import_wings_customers_to_staging($start = 0, $limit = 100) {
        global $wpdb;
        
        MAX_User_Management::log('Početak importa Wings korisnika u staging tabelu', 'info', array(
            'start' => $start,
            'limit' => $limit
        ));
        
        // Dobij korisnike iz Wings-a
        $wings_customers = $this->wings_api->get_all_customers($start, $limit);
        
        if (is_wp_error($wings_customers)) {
            MAX_User_Management::log('Greška pri dobijanju korisnika iz Wings-a', 'error', array(
                'error' => $wings_customers->get_error_message()
            ));
            return $wings_customers;
        }
        
        if (empty($wings_customers)) {
            MAX_User_Management::log('Nema korisnika za import', 'info');
            return array('imported' => 0, 'updated' => 0, 'errors' => 0);
        }
        
        $table_name = $wpdb->prefix . 'max_wings_users_staging';
        $imported = 0;
        $updated = 0;
        $errors = 0;
        
        foreach ($wings_customers as $wings_customer) {
            try {
                $mapped_data = $this->map_wings_customer_to_staging($wings_customer);
                
                // Proverava da li korisnik već postoji u staging tabeli
                $existing = $wpdb->get_row($wpdb->prepare(
                    "SELECT id FROM {$table_name} WHERE wings_id = %s",
                    $mapped_data['wings_id']
                ));
                
                if ($existing) {
                    // Ažuriraj postojeći zapis
                    $result = $wpdb->update(
                        $table_name,
                        $mapped_data,
                        array('wings_id' => $mapped_data['wings_id']),
                        array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'),
                        array('%s')
                    );
                    
                    if ($result !== false) {
                        $updated++;
                    } else {
                        $errors++;
                        MAX_User_Management::log('Greška pri ažuriranju korisnika u staging tabeli', 'error', array(
                            'wings_id' => $mapped_data['wings_id'],
                            'db_error' => $wpdb->last_error
                        ));
                    }
                } else {
                    // Dodaj novi zapis
                    $result = $wpdb->insert(
                        $table_name,
                        $mapped_data,
                        array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
                    );
                    
                    if ($result !== false) {
                        $imported++;
                    } else {
                        $errors++;
                        MAX_User_Management::log('Greška pri dodavanju korisnika u staging tabelu', 'error', array(
                            'wings_id' => $mapped_data['wings_id'],
                            'db_error' => $wpdb->last_error
                        ));
                    }
                }
                
            } catch (Exception $e) {
                $errors++;
                MAX_User_Management::log('Izuzetak pri mapiranju korisnika', 'error', array(
                    'exception' => $e->getMessage(),
                    'customer_data' => $wings_customer
                ));
            }
        }
        
        $result = array(
            'imported' => $imported,
            'updated' => $updated,
            'errors' => $errors,
            'total_processed' => count($wings_customers)
        );
        
        MAX_User_Management::log('Završen import Wings korisnika u staging tabelu', 'info', $result);
        
        return $result;
    }
    
    /**
     * Dobij sve korisnike iz staging tabele
     */
    public function get_staging_users($status = null, $limit = 50, $offset = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'max_wings_users_staging';
        $where_clause = '';
        $params = array();
        
        if ($status) {
            $where_clause = 'WHERE sync_status = %s';
            $params[] = $status;
        }
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} {$where_clause} ORDER BY created_date DESC LIMIT %d OFFSET %d",
            array_merge($params, array($limit, $offset))
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }
    
    /**
     * Dobij broj korisnika u staging tabeli po statusu
     */
    public function get_staging_users_count($status = null) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'max_wings_users_staging';
        
        if ($status) {
            return $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE sync_status = %s",
                $status
            ));
        } else {
            return $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        }
    }
    
    /**
     * Kreiraj WordPress korisnika iz staging podataka
     */
    public function create_wp_user_from_staging($staging_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'max_wings_users_staging';
        
        // Dobij podatke iz staging tabele
        $staging_user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d",
            $staging_id
        ), ARRAY_A);
        
        if (!$staging_user) {
            return new WP_Error('staging_user_not_found', 'Korisnik nije pronađen u staging tabeli');
        }
        
        // Generiši email ako ne postoji
        $email = $staging_user['email'];
        if (empty($email)) {
            $email = 'wings_' . $staging_user['wings_id'] . '@placeholder.local';
        }
        
        // Generiši username ako ne postoji ili je prazan
        $username = $staging_user['username'];
        if (empty($username)) {
            $username = 'wings_user_' . $staging_user['wings_id'];
        }
        
        // Proveri da li korisnik već postoji
        if (username_exists($username) || email_exists($email)) {
            return new WP_Error('user_exists', 'Korisnik sa tim username-om ili email-om već postoji');
        }
        
        // Kreiraj WordPress korisnika
        $user_data = array(
            'user_login' => $username,
            'user_email' => $email,
            'first_name' => $staging_user['first_name'],
            'user_pass' => wp_generate_password(12, true),
            'role' => 'customer'
        );
        
        $user_id = wp_insert_user($user_data);
        
        if (is_wp_error($user_id)) {
            // Ažuriraj staging status na error
            $wpdb->update(
                $table_name,
                array('sync_status' => 'error'),
                array('id' => $staging_id),
                array('%s'),
                array('%d')
            );
            
            return $user_id;
        }
        
        // Dodaj meta podatke
        update_user_meta($user_id, 'billing_first_name', $staging_user['first_name']);
        update_user_meta($user_id, 'billing_address_1', $staging_user['address']);
        update_user_meta($user_id, 'billing_city', $staging_user['city']);
        update_user_meta($user_id, 'billing_phone', $staging_user['phone']);
        
        // Dodaj dodatne telefone kao custom meta
        if (!empty($staging_user['phone_2'])) {
            update_user_meta($user_id, 'wings_phone_2', $staging_user['phone_2']);
        }
        if (!empty($staging_user['phone_3'])) {
            update_user_meta($user_id, 'wings_phone_3', $staging_user['phone_3']);
        }
        
        // Dodaj Wings specifične podatke
        update_user_meta($user_id, 'wings_customer_id', $staging_user['wings_id']);
        if (!empty($staging_user['pib'])) {
            update_user_meta($user_id, 'wings_pib', $staging_user['pib']);
        }
        if (!empty($staging_user['discount'])) {
            update_user_meta($user_id, 'wings_discount', $staging_user['discount']);
        }
        
        // Ažuriraj staging tabelu sa WordPress user ID
        $wpdb->update(
            $table_name,
            array(
                'wp_user_id' => $user_id,
                'sync_status' => 'synced'
            ),
            array('id' => $staging_id),
            array('%d', '%s'),
            array('%d')
        );
        
        MAX_User_Management::log('Kreiran WordPress korisnik iz staging podataka', 'info', array(
            'staging_id' => $staging_id,
            'wp_user_id' => $user_id,
            'wings_id' => $staging_user['wings_id'],
            'username' => $username,
            'email' => $email
        ));
        
        return $user_id;
    }
    
    /**
     * Ažuriraj status korisnika u staging tabeli
     */
    public function update_staging_user_status($staging_id, $status) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'max_wings_users_staging';
        
        return $wpdb->update(
            $table_name,
            array('sync_status' => $status),
            array('id' => $staging_id),
            array('%s'),
            array('%d')
        );
    }
    
    /**
     * Obriši korisnika iz staging tabele
     */
    public function delete_staging_user($staging_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'max_wings_users_staging';
        
        return $wpdb->delete(
            $table_name,
            array('id' => $staging_id),
            array('%d')
        );
    }
}
