<?php
/**
 * Standalone API Connection Test
 * Run this from WordPress admin or via WP-CLI to test Wings API connection
 */

// If running from WordPress admin
if (defined('ABSPATH')) {
    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_die('You do not have permission to run this test.');
    }
    
    echo '<div class="wrap">';
    echo '<h1>Wings API Connection Test</h1>';
    echo '<div style="font-family: monospace; background: #f1f1f1; padding: 20px; margin: 20px 0;">';
}

echo "=== Wings API Connection Test ===\n\n";

// Get API settings from both possible locations
$settings_array = get_option('max_user_mgmt_settings', array());
$individual_settings = array(
    'api_url' => get_option('max_user_mgmt_wings_api_url', ''),
    'api_alias' => get_option('max_user_mgmt_wings_api_alias', ''),
    'api_username' => get_option('max_user_mgmt_wings_api_username', ''),
    'api_password' => get_option('max_user_mgmt_wings_api_password', ''),
);

// Use array settings first, then individual settings as fallback
$api_url = $settings_array['wings_api_url'] ?? $individual_settings['api_url'] ?: '';
$api_alias = $settings_array['wings_api_alias'] ?? $individual_settings['api_alias'] ?: '';
$api_username = $settings_array['wings_api_username'] ?? $individual_settings['api_username'] ?: '';
$api_password = $settings_array['wings_api_password'] ?? $individual_settings['api_password'] ?: '';

echo "1. Configuration Check:\n";
echo "   API URL: " . ($api_url ?: 'NOT SET') . "\n";
echo "   API Alias: " . ($api_alias ?: 'NOT SET') . "\n";
echo "   Username: " . ($api_username ? 'SET' : 'NOT SET') . "\n";
echo "   Password: " . ($api_password ? 'SET' : 'NOT SET') . "\n";

// Debug: Show which settings source is being used
echo "   Settings Source: ";
if (!empty($settings_array) && is_array($settings_array)) {
    echo "Array (max_user_mgmt_settings)\n";
    echo "   Array contains: " . implode(', ', array_keys($settings_array)) . "\n";
} else {
    echo "Individual options (max_user_mgmt_*)\n";
}
echo "\n";

if (empty($api_url) || empty($api_alias) || empty($api_username) || empty($api_password)) {
    echo "❌ ERROR: Missing required API configuration!\n";
    echo "Please configure the API settings in MAX Users → Settings\n\n";
    
    if (defined('ABSPATH')) {
        echo '</div></div>';
    }
    return;
}

// Test 1: Basic URL construction
echo "2. URL Construction Test:\n";
$login_url = rtrim($api_url, '/') . '/' . $api_alias . '/system.user.log';
echo "   Login URL: {$login_url}\n\n";

// Test 2: DNS resolution
echo "3. DNS Resolution Test:\n";
$host = parse_url($api_url, PHP_URL_HOST);
$ip = gethostbyname($host);
if ($ip === $host) {
    echo "   ❌ DNS resolution failed for {$host}\n\n";
} else {
    echo "   ✅ DNS resolved: {$host} → {$ip}\n\n";
}

// Test 3: Basic connectivity
echo "4. Basic Connectivity Test:\n";
$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$test_url = rtrim($api_url, '/');
$response = @file_get_contents($test_url, false, $context);
if ($response === false) {
    echo "   ❌ Cannot reach {$test_url}\n";
    echo "   This might be normal if the API doesn't respond to GET requests\n\n";
} else {
    echo "   ✅ Basic connectivity to {$test_url} successful\n\n";
}

// Test 4: API Login attempt
echo "5. API Login Test:\n";

$body = json_encode([
    'aUn' => $api_username,
    'aUp' => $api_password
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n" .
                   "Content-Length: " . strlen($body) . "\r\n",
        'content' => $body,
        'timeout' => 30
    ]
]);

echo "   Attempting login to: {$login_url}\n";
echo "   Request body: " . json_encode(['aUn' => $api_username, 'aUp' => '***']) . "\n";

$response = @file_get_contents($login_url, false, $context);

if ($response === false) {
    echo "   ❌ Login request failed\n";
    $error = error_get_last();
    if ($error) {
        echo "   Error: " . $error['message'] . "\n";
    }
    echo "\n";
} else {
    echo "   ✅ Login request sent successfully\n";
    echo "   Response length: " . strlen($response) . " bytes\n";
    
    // Try to parse response
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "   ✅ Valid JSON response received\n";
        
        if (isset($data['data'][0]['attributes']['token'])) {
            $token = $data['data'][0]['attributes']['token'];
            echo "   ✅ Login successful! Token received: " . substr($token, 0, 10) . "...\n";
        } else {
            echo "   ❌ No token found in response\n";
            echo "   Response structure: " . print_r(array_keys($data), true) . "\n";
        }
    } else {
        echo "   ❌ Invalid JSON response\n";
        echo "   JSON Error: " . json_last_error_msg() . "\n";
        echo "   Response preview: " . substr($response, 0, 200) . "...\n";
    }
    echo "\n";
}

// Test 5: WordPress HTTP API test (if available)
if (function_exists('wp_remote_post')) {
    echo "6. WordPress HTTP API Test:\n";
    
    $wp_response = wp_remote_post($login_url, [
        'timeout' => 30,
        'headers' => [
            'Content-Type' => 'application/json'
        ],
        'body' => $body
    ]);
    
    if (is_wp_error($wp_response)) {
        echo "   ❌ WordPress HTTP API failed\n";
        echo "   Error: " . $wp_response->get_error_message() . "\n\n";
    } else {
        $status_code = wp_remote_retrieve_response_code($wp_response);
        $response_body = wp_remote_retrieve_body($wp_response);
        
        echo "   ✅ WordPress HTTP API request successful\n";
        echo "   Status Code: {$status_code}\n";
        echo "   Response Length: " . strlen($response_body) . " bytes\n";
        
        if ($status_code === 200) {
            $data = json_decode($response_body, true);
            if (isset($data['data'][0]['attributes']['token'])) {
                echo "   ✅ Token found in WordPress HTTP response!\n";
            } else {
                echo "   ❌ No token in WordPress HTTP response\n";
            }
        }
        echo "\n";
    }
}

echo "=== Test Complete ===\n";
echo "If you see errors above, check:\n";
echo "1. API credentials are correct\n";
echo "2. Server can reach portal.wings.rs\n";
echo "3. Firewall/proxy settings\n";
echo "4. API alias is correct (e.g., 'grosstest' for test environment)\n";

if (defined('ABSPATH')) {
    echo '</div>';
    echo '<p><a href="' . admin_url('admin.php?page=max-user-management') . '" class="button">Back to MAX Users</a></p>';
    echo '</div>';
}
?>
