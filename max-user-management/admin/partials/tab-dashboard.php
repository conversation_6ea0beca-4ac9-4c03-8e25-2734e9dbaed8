<?php
/**
 * MAX User Management Dashboard Tab
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get statistics
$total_professionals = count(get_users(array('role' => 'max_professionals')));
$settings = get_option('max_user_mgmt_settings', array());
$wings_configured = !empty($settings['wings_api_url']) && !empty($settings['wings_api_username']);
$recent_logs = get_option('max_user_mgmt_log', array());
$recent_logs = array_slice(array_reverse($recent_logs), 0, 5);
?>

<div class="max-dashboard">
    <div class="max-dashboard-widgets">
        
        <!-- Statistics Cards -->
        <div class="max-stats-grid">
            <div class="max-stat-card">
                <div class="max-stat-icon">
                    <span class="dashicons dashicons-admin-users"></span>
                </div>
                <div class="max-stat-content">
                    <h3><?php echo esc_html($total_professionals); ?></h3>
                    <p><?php _e('Professional Users', 'max-user-management'); ?></p>
                </div>
            </div>
            
            <div class="max-stat-card">
                <div class="max-stat-icon">
                    <span class="dashicons dashicons-<?php echo $wings_configured ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="max-stat-content">
                    <h3><?php echo $wings_configured ? __('Connected', 'max-user-management') : __('Not Configured', 'max-user-management'); ?></h3>
                    <p><?php _e('Wings API Status', 'max-user-management'); ?></p>
                </div>
            </div>
            
            <div class="max-stat-card">
                <div class="max-stat-icon">
                    <span class="dashicons dashicons-admin-tools"></span>
                </div>
                <div class="max-stat-content">
                    <h3><?php echo !empty($settings['enable_debug']) ? __('ON', 'max-user-management') : __('OFF', 'max-user-management'); ?></h3>
                    <p><?php _e('Debug Mode', 'max-user-management'); ?></p>
                </div>
            </div>
            
            <div class="max-stat-card">
                <div class="max-stat-icon">
                    <span class="dashicons dashicons-list-view"></span>
                </div>
                <div class="max-stat-content">
                    <h3><?php echo count(get_option('max_user_mgmt_log', array())); ?></h3>
                    <p><?php _e('Log Entries', 'max-user-management'); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="max-widget">
            <h3><?php _e('Quick Actions', 'max-user-management'); ?></h3>
            <div class="max-quick-actions">
                <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-creator')); ?>" class="button button-primary">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php _e('Create New User', 'max-user-management'); ?>
                </a>
                
                <button type="button" class="button" id="test-wings-connection">
                    <span class="dashicons dashicons-admin-links"></span>
                    <?php _e('Test Wings Connection', 'max-user-management'); ?>
                </button>
                
                <button type="button" class="button" id="sync-users">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Sync Users', 'max-user-management'); ?>
                </button>
                
                <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-management&tab=settings')); ?>" class="button">
                    <span class="dashicons dashicons-admin-settings"></span>
                    <?php _e('Settings', 'max-user-management'); ?>
                </a>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="max-widget">
            <h3><?php _e('System Status', 'max-user-management'); ?></h3>
            <div class="max-system-status">
                <div class="max-status-item">
                    <span class="max-status-label"><?php _e('WordPress Version:', 'max-user-management'); ?></span>
                    <span class="max-status-value"><?php echo esc_html(get_bloginfo('version')); ?></span>
                </div>
                
                <div class="max-status-item">
                    <span class="max-status-label"><?php _e('WooCommerce:', 'max-user-management'); ?></span>
                    <span class="max-status-value">
                        <?php if (class_exists('WooCommerce')) : ?>
                            <span class="max-status-success"><?php echo esc_html(WC()->version); ?></span>
                        <?php else : ?>
                            <span class="max-status-error"><?php _e('Not Active', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="max-status-item">
                    <span class="max-status-label"><?php _e('HPOS Compatibility:', 'max-user-management'); ?></span>
                    <span class="max-status-value">
                        <?php if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) : ?>
                            <span class="max-status-success"><?php _e('Compatible', 'max-user-management'); ?></span>
                        <?php else : ?>
                            <span class="max-status-warning"><?php _e('Unknown', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="max-status-item">
                    <span class="max-status-label"><?php _e('PHP Version:', 'max-user-management'); ?></span>
                    <span class="max-status-value"><?php echo esc_html(PHP_VERSION); ?></span>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="max-widget">
            <h3><?php _e('Recent Activity', 'max-user-management'); ?></h3>
            <div class="max-recent-activity">
                <?php if (!empty($recent_logs)) : ?>
                    <ul class="max-activity-list">
                        <?php foreach ($recent_logs as $log_entry) : ?>
                            <li class="max-activity-item max-activity-<?php echo esc_attr($log_entry['level']); ?>">
                                <span class="max-activity-time"><?php echo esc_html($log_entry['timestamp']); ?></span>
                                <span class="max-activity-message"><?php echo esc_html($log_entry['message']); ?></span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <p class="max-activity-more">
                        <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-management&tab=logs')); ?>">
                            <?php _e('View All Logs', 'max-user-management'); ?>
                        </a>
                    </p>
                <?php else : ?>
                    <p class="max-no-activity"><?php _e('No recent activity.', 'max-user-management'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Configuration Status -->
        <div class="max-widget">
            <h3><?php _e('Configuration Status', 'max-user-management'); ?></h3>
            <div class="max-config-status">
                <div class="max-config-item">
                    <span class="dashicons dashicons-<?php echo !empty($settings['wings_api_url']) ? 'yes' : 'no'; ?>"></span>
                    <span><?php _e('Wings API URL', 'max-user-management'); ?></span>
                </div>
                
                <div class="max-config-item">
                    <span class="dashicons dashicons-<?php echo !empty($settings['wings_api_username']) ? 'yes' : 'no'; ?>"></span>
                    <span><?php _e('Wings API Credentials', 'max-user-management'); ?></span>
                </div>
                
                <div class="max-config-item">
                    <span class="dashicons dashicons-<?php echo get_role('max_professionals') ? 'yes' : 'no'; ?>"></span>
                    <span><?php _e('Professional Role', 'max-user-management'); ?></span>
                </div>
                
                <div class="max-config-item">
                    <span class="dashicons dashicons-<?php echo !empty($settings['password_length']) ? 'yes' : 'no'; ?>"></span>
                    <span><?php _e('Password Settings', 'max-user-management'); ?></span>
                </div>
            </div>
            
            <?php if (!$wings_configured) : ?>
                <div class="max-config-notice">
                    <p><?php _e('Complete the configuration to start using MAX User Management.', 'max-user-management'); ?></p>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-management&tab=settings')); ?>" class="button button-primary">
                        <?php _e('Configure Now', 'max-user-management'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Test Wings Connection
    $('#test-wings-connection').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();
        
        $.ajax({
            url: max_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'max_test_wings_connection',
                nonce: max_ajax.nonce
            },
            beforeSend: function() {
                $btn.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> ' + max_ajax.strings.testing);
            },
            success: function(response) {
                if (response.success) {
                    $btn.html('<span class="dashicons dashicons-yes"></span> ' + max_ajax.strings.success);
                    setTimeout(function() {
                        $btn.html(originalText);
                    }, 3000);
                } else {
                    $btn.html('<span class="dashicons dashicons-no"></span> ' + max_ajax.strings.error);
                    alert('Error: ' + response.data.message);
                    setTimeout(function() {
                        $btn.html(originalText);
                    }, 3000);
                }
            },
            error: function() {
                $btn.html('<span class="dashicons dashicons-no"></span> ' + max_ajax.strings.error);
                setTimeout(function() {
                    $btn.html(originalText);
                }, 3000);
            },
            complete: function() {
                $btn.prop('disabled', false);
            }
        });
    });
    
    // Sync Users
    $('#sync-users').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();
        
        $.ajax({
            url: max_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'max_sync_users',
                nonce: max_ajax.nonce
            },
            beforeSend: function() {
                $btn.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> ' + max_ajax.strings.syncing);
            },
            success: function(response) {
                if (response.success) {
                    $btn.html('<span class="dashicons dashicons-yes"></span> ' + max_ajax.strings.success);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $btn.html('<span class="dashicons dashicons-no"></span> ' + max_ajax.strings.error);
                    alert('Error: ' + response.data.message);
                    setTimeout(function() {
                        $btn.html(originalText);
                    }, 3000);
                }
            },
            error: function() {
                $btn.html('<span class="dashicons dashicons-no"></span> ' + max_ajax.strings.error);
                setTimeout(function() {
                    $btn.html(originalText);
                }, 3000);
            },
            complete: function() {
                $btn.prop('disabled', false);
            }
        });
    });
});
</script>
?>
