<?php
/**
 * MAX User Management System Info Page
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get system information
$system_info = array(
    'wordpress' => array(
        'version' => get_bloginfo('version'),
        'multisite' => is_multisite(),
        'debug' => defined('WP_DEBUG') && WP_DEBUG,
        'debug_log' => defined('WP_DEBUG_LOG') && WP_DEBUG_LOG,
    ),
    'server' => array(
        'php_version' => PHP_VERSION,
        'mysql_version' => $GLOBALS['wpdb']->db_version(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'max_execution_time' => ini_get('max_execution_time'),
        'memory_limit' => ini_get('memory_limit'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
    ),
    'plugin' => array(
        'version' => MAX_USER_MGMT_VERSION,
        'plugin_dir' => MAX_USER_MGMT_PLUGIN_DIR,
        'plugin_url' => MAX_USER_MGMT_PLUGIN_URL,
    )
);

// Check WooCommerce
if (class_exists('WooCommerce')) {
    $system_info['woocommerce'] = array(
        'version' => WC()->version,
        'hpos_enabled' => class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') && 
                         method_exists('\Automattic\WooCommerce\Utilities\OrderUtil', 'custom_orders_table_usage_is_enabled') &&
                         \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled(),
        'hpos_compatible' => true, // Our plugin declares compatibility
    );
}

// Get plugin settings
$settings = get_option('max_user_mgmt_settings', array());
$professional_users = get_users(array('role' => 'max_professionals'));
$logs = get_option('max_user_mgmt_log', array());
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="max-system-info">
        
        <!-- Plugin Information -->
        <div class="max-info-section">
            <h2><?php _e('Plugin Information', 'max-user-management'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Plugin Version', 'max-user-management'); ?></th>
                    <td><?php echo esc_html($system_info['plugin']['version']); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Plugin Directory', 'max-user-management'); ?></th>
                    <td><code><?php echo esc_html($system_info['plugin']['plugin_dir']); ?></code></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Plugin URL', 'max-user-management'); ?></th>
                    <td><code><?php echo esc_html($system_info['plugin']['plugin_url']); ?></code></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Professional Users', 'max-user-management'); ?></th>
                    <td><?php echo count($professional_users); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Log Entries', 'max-user-management'); ?></th>
                    <td><?php echo count($logs); ?></td>
                </tr>
            </table>
        </div>
        
        <!-- WordPress Information -->
        <div class="max-info-section">
            <h2><?php _e('WordPress Information', 'max-user-management'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('WordPress Version', 'max-user-management'); ?></th>
                    <td><?php echo esc_html($system_info['wordpress']['version']); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Multisite', 'max-user-management'); ?></th>
                    <td>
                        <span class="max-status-<?php echo $system_info['wordpress']['multisite'] ? 'warning' : 'success'; ?>">
                            <?php echo $system_info['wordpress']['multisite'] ? __('Yes', 'max-user-management') : __('No', 'max-user-management'); ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Debug Mode', 'max-user-management'); ?></th>
                    <td>
                        <span class="max-status-<?php echo $system_info['wordpress']['debug'] ? 'warning' : 'success'; ?>">
                            <?php echo $system_info['wordpress']['debug'] ? __('Enabled', 'max-user-management') : __('Disabled', 'max-user-management'); ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Debug Logging', 'max-user-management'); ?></th>
                    <td>
                        <span class="max-status-<?php echo $system_info['wordpress']['debug_log'] ? 'success' : 'warning'; ?>">
                            <?php echo $system_info['wordpress']['debug_log'] ? __('Enabled', 'max-user-management') : __('Disabled', 'max-user-management'); ?>
                        </span>
                    </td>
                </tr>
            </table>
        </div>
        
        <!-- WooCommerce Information -->
        <?php if (isset($system_info['woocommerce'])) : ?>
            <div class="max-info-section">
                <h2><?php _e('WooCommerce Information', 'max-user-management'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('WooCommerce Version', 'max-user-management'); ?></th>
                        <td><?php echo esc_html($system_info['woocommerce']['version']); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('HPOS Enabled', 'max-user-management'); ?></th>
                        <td>
                            <span class="max-status-<?php echo $system_info['woocommerce']['hpos_enabled'] ? 'success' : 'info'; ?>">
                                <?php echo $system_info['woocommerce']['hpos_enabled'] ? __('Yes', 'max-user-management') : __('No', 'max-user-management'); ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('HPOS Compatibility', 'max-user-management'); ?></th>
                        <td>
                            <span class="max-status-success">
                                <?php _e('Compatible', 'max-user-management'); ?>
                            </span>
                            <p class="description"><?php _e('This plugin declares HPOS compatibility.', 'max-user-management'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
        <?php else : ?>
            <div class="max-info-section">
                <h2><?php _e('WooCommerce Information', 'max-user-management'); ?></h2>
                <p class="max-status-error"><?php _e('WooCommerce is not active.', 'max-user-management'); ?></p>
            </div>
        <?php endif; ?>
        
        <!-- Server Information -->
        <div class="max-info-section">
            <h2><?php _e('Server Information', 'max-user-management'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('PHP Version', 'max-user-management'); ?></th>
                    <td>
                        <?php echo esc_html($system_info['server']['php_version']); ?>
                        <?php if (version_compare($system_info['server']['php_version'], '7.4', '<')) : ?>
                            <span class="max-status-error"><?php _e('(Outdated)', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('MySQL Version', 'max-user-management'); ?></th>
                    <td><?php echo esc_html($system_info['server']['mysql_version']); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Server Software', 'max-user-management'); ?></th>
                    <td><?php echo esc_html($system_info['server']['server_software']); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Max Execution Time', 'max-user-management'); ?></th>
                    <td>
                        <?php echo esc_html($system_info['server']['max_execution_time']); ?> <?php _e('seconds', 'max-user-management'); ?>
                        <?php if ($system_info['server']['max_execution_time'] < 60) : ?>
                            <span class="max-status-warning"><?php _e('(Low)', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Memory Limit', 'max-user-management'); ?></th>
                    <td><?php echo esc_html($system_info['server']['memory_limit']); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Upload Max Filesize', 'max-user-management'); ?></th>
                    <td><?php echo esc_html($system_info['server']['upload_max_filesize']); ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Configuration Status -->
        <div class="max-info-section">
            <h2><?php _e('Configuration Status', 'max-user-management'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Wings API URL', 'max-user-management'); ?></th>
                    <td>
                        <?php if (!empty($settings['wings_api_url'])) : ?>
                            <span class="max-status-success"><?php _e('Configured', 'max-user-management'); ?></span>
                            <br><code><?php echo esc_html($settings['wings_api_url']); ?></code>
                        <?php else : ?>
                            <span class="max-status-error"><?php _e('Not configured', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Wings API Credentials', 'max-user-management'); ?></th>
                    <td>
                        <?php if (!empty($settings['wings_api_username']) && !empty($settings['wings_api_password'])) : ?>
                            <span class="max-status-success"><?php _e('Configured', 'max-user-management'); ?></span>
                        <?php else : ?>
                            <span class="max-status-error"><?php _e('Not configured', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Professional Role', 'max-user-management'); ?></th>
                    <td>
                        <?php if (get_role('max_professionals')) : ?>
                            <span class="max-status-success"><?php _e('Created', 'max-user-management'); ?></span>
                        <?php else : ?>
                            <span class="max-status-error"><?php _e('Missing', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Debug Mode', 'max-user-management'); ?></th>
                    <td>
                        <?php if (!empty($settings['enable_debug'])) : ?>
                            <span class="max-status-warning"><?php _e('Enabled', 'max-user-management'); ?></span>
                        <?php else : ?>
                            <span class="max-status-success"><?php _e('Disabled', 'max-user-management'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <!-- Export System Info -->
        <div class="max-info-section">
            <h2><?php _e('Export System Information', 'max-user-management'); ?></h2>
            <p class="description">
                <?php _e('Export system information for troubleshooting or support purposes.', 'max-user-management'); ?>
            </p>
            
            <button type="button" class="button" id="export-system-info">
                <span class="dashicons dashicons-download"></span>
                <?php _e('Export System Info', 'max-user-management'); ?>
            </button>
            
            <button type="button" class="button" id="copy-system-info">
                <span class="dashicons dashicons-admin-page"></span>
                <?php _e('Copy to Clipboard', 'max-user-management'); ?>
            </button>
        </div>
        
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Export system info
    $('#export-system-info').on('click', function() {
        var systemInfo = <?php echo json_encode($system_info, JSON_PRETTY_PRINT); ?>;
        var dataStr = JSON.stringify(systemInfo, null, 2);
        var dataBlob = new Blob([dataStr], {type: 'application/json'});
        var url = URL.createObjectURL(dataBlob);
        var link = document.createElement('a');
        link.href = url;
        link.download = 'max-user-management-system-info.json';
        link.click();
        URL.revokeObjectURL(url);
    });
    
    // Copy system info to clipboard
    $('#copy-system-info').on('click', function() {
        var systemInfo = <?php echo json_encode($system_info, JSON_PRETTY_PRINT); ?>;
        var dataStr = JSON.stringify(systemInfo, null, 2);
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(dataStr).then(function() {
                alert('<?php echo esc_js(__('System information copied to clipboard!', 'max-user-management')); ?>');
            });
        } else {
            // Fallback
            var textArea = document.createElement('textarea');
            textArea.value = dataStr;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('<?php echo esc_js(__('System information copied to clipboard!', 'max-user-management')); ?>');
        }
    });
});
</script>

<style>
.max-info-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.max-info-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 1px solid #f0f0f1;
    padding-bottom: 10px;
}

.max-info-section .form-table th {
    width: 250px;
    font-weight: 600;
}

.max-info-section .form-table td code {
    background: #f6f7f7;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}
</style>
?>
