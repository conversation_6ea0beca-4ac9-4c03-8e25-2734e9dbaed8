<?php
/**
 * MAX User Management Admin Display
 * Main admin interface with tabbed navigation
 */

if (!defined('ABSPATH')) {
    exit;
}

$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'dashboard';
$tabs = array(
    'dashboard' => __('Dashboard', 'max-user-management'),
    'users' => __('Users', 'max-user-management'),
    'sync' => __('Wings Sync', 'max-user-management'),
    'settings' => __('Settings', 'max-user-management'),
    'logs' => __('Logs', 'max-user-management')
);
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <!-- Debug Mode Indicator -->
    <?php
    $settings = get_option('max_user_mgmt_settings', array());
    $debug_enabled = !empty($settings['enable_debug']);
    if ($debug_enabled) {
        echo '<div class="notice notice-info"><p><strong>' . __('Debug Mode Enabled', 'max-user-management') . '</strong> - ' . __('Detailed logging is active', 'max-user-management') . '</p></div>';
    }
    ?>
    
    <!-- Tab Navigation -->
    <nav class="nav-tab-wrapper">
        <?php foreach ($tabs as $tab_key => $tab_label) : ?>
            <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-management&tab=' . $tab_key)); ?>" 
               class="nav-tab <?php echo $current_tab === $tab_key ? 'nav-tab-active' : ''; ?>">
                <?php echo esc_html($tab_label); ?>
            </a>
        <?php endforeach; ?>
    </nav>
    
    <!-- Tab Content -->
    <div class="max-admin-container">
        <?php
        switch ($current_tab) {
            case 'dashboard':
                include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/tab-dashboard.php';
                break;
            case 'users':
                include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/tab-users.php';
                break;
            case 'sync':
                include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/tab-sync.php';
                break;
            case 'settings':
                include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/tab-settings.php';
                break;
            case 'logs':
                include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/tab-logs.php';
                break;
            default:
                include MAX_USER_MGMT_PLUGIN_DIR . 'admin/partials/tab-dashboard.php';
                break;
        }
        ?>
    </div>
</div>

<!-- Debug Toggle Button (Fixed Position) -->
<div id="max-debug-toggle" class="max-debug-toggle">
    <button type="button" class="button" id="toggle-debug-btn" title="<?php esc_attr_e('Toggle Debug Mode', 'max-user-management'); ?>">
        <span class="dashicons dashicons-<?php echo $debug_enabled ? 'visibility' : 'hidden'; ?>"></span>
        <?php echo $debug_enabled ? __('Debug ON', 'max-user-management') : __('Debug OFF', 'max-user-management'); ?>
    </button>
</div>

<script>
jQuery(document).ready(function($) {
    // Debug toggle functionality
    $('#toggle-debug-btn').on('click', function() {
        var $btn = $(this);
        var $icon = $btn.find('.dashicons');
        var $text = $btn.contents().filter(function() {
            return this.nodeType === 3; // Text nodes
        });
        
        var currentlyEnabled = $icon.hasClass('dashicons-visibility');
        
        $.ajax({
            url: max_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'max_toggle_debug',
                nonce: max_ajax.nonce,
                enable_debug: !currentlyEnabled
            },
            beforeSend: function() {
                $btn.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.debug_enabled) {
                        $icon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
                        $text.replaceWith('<?php echo esc_js(__('Debug ON', 'max-user-management')); ?>');
                        // Show debug notice
                        if ($('.notice-info').length === 0) {
                            $('h1').after('<div class="notice notice-info"><p><strong><?php echo esc_js(__('Debug Mode Enabled', 'max-user-management')); ?></strong> - <?php echo esc_js(__('Detailed logging is active', 'max-user-management')); ?></p></div>');
                        }
                    } else {
                        $icon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
                        $text.replaceWith('<?php echo esc_js(__('Debug OFF', 'max-user-management')); ?>');
                        // Hide debug notice
                        $('.notice-info').fadeOut();
                    }
                } else {
                    alert('Error: ' + response.data.message);
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('Error toggling debug mode', 'max-user-management')); ?>');
            },
            complete: function() {
                $btn.prop('disabled', false);
            }
        });
    });
});
</script>
?>
