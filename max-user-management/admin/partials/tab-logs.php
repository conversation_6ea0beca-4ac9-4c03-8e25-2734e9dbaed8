<?php
/**
 * MAX User Management Logs Tab
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle log actions
if (isset($_POST['clear_logs']) && wp_verify_nonce($_POST['_wpnonce'], 'max_clear_logs')) {
    delete_option('max_user_mgmt_log');
    echo '<div class="notice notice-success"><p>' . __('Logs cleared successfully!', 'max-user-management') . '</p></div>';
}

// Get logs
$logs = get_option('max_user_mgmt_log', array());
$logs = array_reverse($logs); // Show newest first

// Pagination
$per_page = 50;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$total_logs = count($logs);
$total_pages = ceil($total_logs / $per_page);
$offset = ($current_page - 1) * $per_page;
$logs_page = array_slice($logs, $offset, $per_page);

// Filter by level
$filter_level = isset($_GET['level']) ? sanitize_text_field($_GET['level']) : '';
if ($filter_level) {
    $logs_page = array_filter($logs_page, function($log) use ($filter_level) {
        return $log['level'] === $filter_level;
    });
}
?>

<div class="max-logs">
    
    <!-- Log Controls -->
    <div class="max-log-controls">
        <div class="max-log-stats">
            <h3><?php _e('Log Statistics', 'max-user-management'); ?></h3>
            <div class="max-stats-row">
                <div class="max-stat-item">
                    <span class="max-stat-number"><?php echo count($logs); ?></span>
                    <span class="max-stat-label"><?php _e('Total Entries', 'max-user-management'); ?></span>
                </div>
                <div class="max-stat-item">
                    <span class="max-stat-number"><?php echo count(array_filter($logs, function($log) { return $log['level'] === 'error'; })); ?></span>
                    <span class="max-stat-label"><?php _e('Errors', 'max-user-management'); ?></span>
                </div>
                <div class="max-stat-item">
                    <span class="max-stat-number"><?php echo count(array_filter($logs, function($log) { return $log['level'] === 'warning'; })); ?></span>
                    <span class="max-stat-label"><?php _e('Warnings', 'max-user-management'); ?></span>
                </div>
                <div class="max-stat-item">
                    <span class="max-stat-number"><?php echo count(array_filter($logs, function($log) { return $log['level'] === 'info'; })); ?></span>
                    <span class="max-stat-label"><?php _e('Info', 'max-user-management'); ?></span>
                </div>
            </div>
        </div>
        
        <div class="max-log-actions">
            <h3><?php _e('Log Actions', 'max-user-management'); ?></h3>
            
            <!-- Filter Controls -->
            <div class="max-filter-controls">
                <form method="get" action="">
                    <input type="hidden" name="page" value="max-user-management" />
                    <input type="hidden" name="tab" value="logs" />
                    
                    <select name="level" onchange="this.form.submit()">
                        <option value=""><?php _e('All Levels', 'max-user-management'); ?></option>
                        <option value="error" <?php selected($filter_level, 'error'); ?>><?php _e('Errors Only', 'max-user-management'); ?></option>
                        <option value="warning" <?php selected($filter_level, 'warning'); ?>><?php _e('Warnings Only', 'max-user-management'); ?></option>
                        <option value="info" <?php selected($filter_level, 'info'); ?>><?php _e('Info Only', 'max-user-management'); ?></option>
                        <option value="success" <?php selected($filter_level, 'success'); ?>><?php _e('Success Only', 'max-user-management'); ?></option>
                    </select>
                </form>
            </div>
            
            <!-- Action Buttons -->
            <div class="max-action-buttons">
                <button type="button" class="button max-ajax-button" 
                        data-action="max_export_logs"
                        data-loading="<?php esc_attr_e('Exporting...', 'max-user-management'); ?>">
                    <span class="dashicons dashicons-download"></span>
                    <?php _e('Export Logs', 'max-user-management'); ?>
                </button>
                
                <button type="button" class="button max-ajax-button" 
                        data-action="max_clear_log"
                        data-confirm="<?php esc_attr_e('Are you sure you want to clear all logs? This action cannot be undone.', 'max-user-management'); ?>">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('Clear All Logs', 'max-user-management'); ?>
                </button>
                
                <button type="button" class="button" onclick="location.reload()">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh', 'max-user-management'); ?>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Log Viewer -->
    <div class="max-log-viewer">
        <h3><?php _e('Recent Log Entries', 'max-user-management'); ?></h3>
        
        <?php if (!empty($logs_page)) : ?>
            
            <!-- Pagination Top -->
            <?php if ($total_pages > 1) : ?>
                <div class="max-pagination">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'current' => $current_page,
                        'total' => $total_pages,
                        'prev_text' => '&laquo; ' . __('Previous', 'max-user-management'),
                        'next_text' => __('Next', 'max-user-management') . ' &raquo;'
                    );
                    echo paginate_links($pagination_args);
                    ?>
                </div>
            <?php endif; ?>
            
            <!-- Log Entries -->
            <div class="max-log-entries">
                <?php foreach ($logs_page as $index => $log_entry) : ?>
                    <div class="max-log-entry max-log-<?php echo esc_attr($log_entry['level']); ?>">
                        <div class="max-log-header">
                            <span class="max-log-timestamp"><?php echo esc_html($log_entry['timestamp']); ?></span>
                            <span class="max-log-level max-log-level-<?php echo esc_attr($log_entry['level']); ?>">
                                <?php echo esc_html(strtoupper($log_entry['level'])); ?>
                            </span>
                        </div>
                        <div class="max-log-message">
                            <?php echo esc_html($log_entry['message']); ?>
                        </div>
                        <?php if (!empty($log_entry['context'])) : ?>
                            <div class="max-log-context">
                                <details>
                                    <summary><?php _e('Context Data', 'max-user-management'); ?></summary>
                                    <pre><?php echo esc_html(print_r($log_entry['context'], true)); ?></pre>
                                </details>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination Bottom -->
            <?php if ($total_pages > 1) : ?>
                <div class="max-pagination">
                    <?php echo paginate_links($pagination_args); ?>
                </div>
            <?php endif; ?>
            
        <?php else : ?>
            <div class="max-no-logs">
                <p><?php _e('No log entries found.', 'max-user-management'); ?></p>
                <?php if ($filter_level) : ?>
                    <p>
                        <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-management&tab=logs')); ?>">
                            <?php _e('View all logs', 'max-user-management'); ?>
                        </a>
                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Debug Information -->
    <?php
    $settings = get_option('max_user_mgmt_settings', array());
    $debug_enabled = !empty($settings['enable_debug']);
    ?>
    
    <div class="max-debug-info">
        <h3><?php _e('Debug Information', 'max-user-management'); ?></h3>
        
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Debug Mode', 'max-user-management'); ?></th>
                <td>
                    <span class="max-status-<?php echo $debug_enabled ? 'success' : 'warning'; ?>">
                        <?php echo $debug_enabled ? __('Enabled', 'max-user-management') : __('Disabled', 'max-user-management'); ?>
                    </span>
                    <?php if (!$debug_enabled) : ?>
                        <p class="description">
                            <?php _e('Enable debug mode in settings to see detailed logs.', 'max-user-management'); ?>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=max-user-management&tab=settings')); ?>">
                                <?php _e('Go to Settings', 'max-user-management'); ?>
                            </a>
                        </p>
                    <?php endif; ?>
                </td>
            </tr>
            
            <tr>
                <th scope="row"><?php _e('Log Storage', 'max-user-management'); ?></th>
                <td>
                    <?php _e('WordPress Options Table', 'max-user-management'); ?>
                    <p class="description">
                        <?php printf(__('Current size: %s entries', 'max-user-management'), number_format(count($logs))); ?>
                    </p>
                </td>
            </tr>
            
            <tr>
                <th scope="row"><?php _e('Log Retention', 'max-user-management'); ?></th>
                <td>
                    <?php _e('No automatic cleanup (manual only)', 'max-user-management'); ?>
                    <p class="description">
                        <?php _e('Logs are kept until manually cleared. Consider clearing old logs periodically.', 'max-user-management'); ?>
                    </p>
                </td>
            </tr>
            
            <tr>
                <th scope="row"><?php _e('WordPress Debug Log', 'max-user-management'); ?></th>
                <td>
                    <?php if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) : ?>
                        <span class="max-status-success"><?php _e('Enabled', 'max-user-management'); ?></span>
                        <p class="description">
                            <?php _e('WordPress debug logging is enabled. Check wp-content/debug.log for additional information.', 'max-user-management'); ?>
                        </p>
                    <?php else : ?>
                        <span class="max-status-warning"><?php _e('Disabled', 'max-user-management'); ?></span>
                        <p class="description">
                            <?php _e('WordPress debug logging is disabled. Enable WP_DEBUG_LOG in wp-config.php for more detailed logging.', 'max-user-management'); ?>
                        </p>
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>
    
</div>

<style>
.max-log-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.max-stats-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.max-stat-item {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.max-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #2271b1;
}

.max-stat-label {
    display: block;
    font-size: 12px;
    color: #646970;
    margin-top: 5px;
}

.max-filter-controls {
    margin-bottom: 15px;
}

.max-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.max-action-buttons .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

.max-log-entries {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.max-log-entry {
    padding: 15px;
    border-bottom: 1px solid #f0f0f1;
    font-family: monospace;
    font-size: 13px;
}

.max-log-entry:last-child {
    border-bottom: none;
}

.max-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.max-log-timestamp {
    color: #646970;
    font-size: 12px;
}

.max-log-level {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
}

.max-log-level-error {
    background: #d63638;
    color: #fff;
}

.max-log-level-warning {
    background: #dba617;
    color: #fff;
}

.max-log-level-info {
    background: #2271b1;
    color: #fff;
}

.max-log-level-success {
    background: #00a32a;
    color: #fff;
}

.max-log-message {
    color: #1d2327;
    line-height: 1.4;
}

.max-log-context {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f1;
}

.max-log-context details {
    cursor: pointer;
}

.max-log-context pre {
    background: #f6f7f7;
    padding: 10px;
    border-radius: 3px;
    font-size: 11px;
    overflow-x: auto;
    margin-top: 5px;
}

.max-no-logs {
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

.max-pagination {
    text-align: center;
    margin: 20px 0;
}

.max-debug-info {
    margin-top: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .max-log-controls {
        grid-template-columns: 1fr;
    }
    
    .max-stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .max-action-buttons {
        flex-direction: column;
    }
    
    .max-log-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>
?>
