<?php
/**
 * Unit Tests for MAX User Roles
 * 
 * Run with: wp eval-file wp-content/plugins/max-user-management/tests/test-max-user-roles.php
 */

if (!defined('ABSPATH')) {
    // Include WordPress if running standalone
    require_once('../../../../wp-config.php');
}

class MAX_User_Roles_Test {
    
    private static $test_results = array();
    private static $test_user_id = null;
    
    /**
     * Run all tests
     */
    public static function run_all_tests() {
        echo "<h2>MAX User Roles - Unit Tests</h2>\n";
        echo "<style>
            .test-pass { color: green; }
            .test-fail { color: red; }
            .test-warning { color: orange; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
        </style>\n";
        
        // Setup
        self::setup_tests();
        
        // Run tests
        self::test_constants();
        self::test_role_creation();
        self::test_capabilities();
        self::test_user_status_management();
        self::test_validation_layer();
        self::test_user_creation();
        self::test_status_checks();
        
        // Cleanup
        self::cleanup_tests();
        
        // Display results
        self::display_results();
    }
    
    /**
     * Setup test environment
     */
    private static function setup_tests() {
        echo "<h3>🔧 Setting up test environment...</h3>\n";
        
        // Ensure MAX User Roles class is loaded
        if (!class_exists('MAX_User_Roles')) {
            echo "<p class='test-fail'>❌ MAX_User_Roles class not found!</p>\n";
            return false;
        }
        
        // Create test role
        MAX_User_Roles::create_professionals_role();
        
        echo "<p class='test-pass'>✅ Test environment setup complete</p>\n";
        return true;
    }
    
    /**
     * Test constants
     */
    private static function test_constants() {
        echo "<h3>📋 Testing Constants</h3>\n";
        
        $constants_to_test = array(
            'ROLE_NAME' => 'max_professionals',
            'ROLE_DISPLAY_NAME' => 'MAX Professionals',
            'STATUS_ACTIVE' => 'active',
            'STATUS_INACTIVE' => 'inactive',
            'STATUS_PENDING' => 'pending',
            'STATUS_SUSPENDED' => 'suspended',
            'CAP_VIEW_PROFESSIONAL_PRICES' => 'view_professional_prices',
            'CAP_ACCESS_PROFESSIONAL_CONTENT' => 'access_professional_content'
        );
        
        foreach ($constants_to_test as $constant => $expected_value) {
            $actual_value = constant('MAX_User_Roles::' . $constant);
            if ($actual_value === $expected_value) {
                self::$test_results[] = array('test' => "Constant {$constant}", 'result' => 'PASS', 'message' => "Value: {$actual_value}");
            } else {
                self::$test_results[] = array('test' => "Constant {$constant}", 'result' => 'FAIL', 'message' => "Expected: {$expected_value}, Got: {$actual_value}");
            }
        }
    }
    
    /**
     * Test role creation
     */
    private static function test_role_creation() {
        echo "<h3>👤 Testing Role Creation</h3>\n";
        
        // Test role exists
        $role = get_role(MAX_User_Roles::ROLE_NAME);
        if ($role) {
            self::$test_results[] = array('test' => 'Role Creation', 'result' => 'PASS', 'message' => 'MAX Professionals role exists');
        } else {
            self::$test_results[] = array('test' => 'Role Creation', 'result' => 'FAIL', 'message' => 'MAX Professionals role not found');
            return;
        }
        
        // Test role display name
        global $wp_roles;
        $role_names = $wp_roles->get_names();
        $display_name = $role_names[MAX_User_Roles::ROLE_NAME] ?? '';
        
        if (strpos($display_name, 'MAX Professionals') !== false) {
            self::$test_results[] = array('test' => 'Role Display Name', 'result' => 'PASS', 'message' => "Display name: {$display_name}");
        } else {
            self::$test_results[] = array('test' => 'Role Display Name', 'result' => 'FAIL', 'message' => "Expected 'MAX Professionals', got: {$display_name}");
        }
    }
    
    /**
     * Test capabilities
     */
    private static function test_capabilities() {
        echo "<h3>🔐 Testing Capabilities</h3>\n";
        
        $role = get_role(MAX_User_Roles::ROLE_NAME);
        if (!$role) {
            self::$test_results[] = array('test' => 'Capabilities Test', 'result' => 'FAIL', 'message' => 'Role not found');
            return;
        }
        
        // Test required capabilities
        $required_caps = array(
            'read' => true,
            'read_product' => true,
            MAX_User_Roles::CAP_VIEW_PROFESSIONAL_PRICES => true,
            MAX_User_Roles::CAP_ACCESS_PROFESSIONAL_CONTENT => true,
            MAX_User_Roles::CAP_PLACE_PROFESSIONAL_ORDERS => true
        );
        
        foreach ($required_caps as $cap => $expected) {
            $has_cap = $role->has_cap($cap);
            if ($has_cap === $expected) {
                self::$test_results[] = array('test' => "Capability: {$cap}", 'result' => 'PASS', 'message' => $expected ? 'Has capability' : 'Does not have capability');
            } else {
                self::$test_results[] = array('test' => "Capability: {$cap}", 'result' => 'FAIL', 'message' => "Expected: " . ($expected ? 'true' : 'false') . ", Got: " . ($has_cap ? 'true' : 'false'));
            }
        }
        
        // Test that role doesn't have admin capabilities
        $admin_caps = array('edit_posts', 'delete_posts', 'manage_options', 'edit_users');
        foreach ($admin_caps as $cap) {
            if (!$role->has_cap($cap)) {
                self::$test_results[] = array('test' => "No Admin Cap: {$cap}", 'result' => 'PASS', 'message' => 'Correctly does not have admin capability');
            } else {
                self::$test_results[] = array('test' => "No Admin Cap: {$cap}", 'result' => 'FAIL', 'message' => 'Should not have admin capability');
            }
        }
    }
    
    /**
     * Test user status management
     */
    private static function test_user_status_management() {
        echo "<h3>📊 Testing User Status Management</h3>\n";
        
        // Create test user
        $user_data = array(
            'user_login' => 'test_professional_' . time(),
            'user_email' => 'test' . time() . '@example.com',
            'user_pass' => 'test_password',
            'first_name' => 'Test',
            'last_name' => 'Professional',
            'role' => MAX_User_Roles::ROLE_NAME
        );
        
        $user_id = wp_insert_user($user_data);
        if (is_wp_error($user_id)) {
            self::$test_results[] = array('test' => 'Test User Creation', 'result' => 'FAIL', 'message' => $user_id->get_error_message());
            return;
        }
        
        self::$test_user_id = $user_id;
        
        // Test default status
        $default_status = MAX_User_Roles::get_user_status($user_id);
        if ($default_status === MAX_User_Roles::STATUS_ACTIVE) {
            self::$test_results[] = array('test' => 'Default Status', 'result' => 'PASS', 'message' => 'Default status is active');
        } else {
            self::$test_results[] = array('test' => 'Default Status', 'result' => 'FAIL', 'message' => "Expected active, got: {$default_status}");
        }
        
        // Test status setting
        $statuses_to_test = array(
            MAX_User_Roles::STATUS_PENDING,
            MAX_User_Roles::STATUS_SUSPENDED,
            MAX_User_Roles::STATUS_INACTIVE,
            MAX_User_Roles::STATUS_ACTIVE
        );
        
        foreach ($statuses_to_test as $status) {
            $result = MAX_User_Roles::set_user_status($user_id, $status);
            $current_status = MAX_User_Roles::get_user_status($user_id);
            
            if ($result && $current_status === $status) {
                self::$test_results[] = array('test' => "Set Status: {$status}", 'result' => 'PASS', 'message' => 'Status set successfully');
            } else {
                self::$test_results[] = array('test' => "Set Status: {$status}", 'result' => 'FAIL', 'message' => "Failed to set status to {$status}");
            }
        }
        
        // Test invalid status
        $invalid_result = MAX_User_Roles::set_user_status($user_id, 'invalid_status');
        if (!$invalid_result) {
            self::$test_results[] = array('test' => 'Invalid Status Rejection', 'result' => 'PASS', 'message' => 'Invalid status correctly rejected');
        } else {
            self::$test_results[] = array('test' => 'Invalid Status Rejection', 'result' => 'FAIL', 'message' => 'Invalid status was accepted');
        }
    }
    
    /**
     * Test validation layer
     */
    private static function test_validation_layer() {
        echo "<h3>✅ Testing Validation Layer</h3>\n";
        
        // Test valid data
        $valid_data = array(
            'user_login' => 'valid_user_' . time(),
            'user_email' => 'valid' . time() . '@example.com',
            'first_name' => 'Valid',
            'last_name' => 'User'
        );
        
        $validation_result = MAX_User_Roles::validate_user_data($valid_data);
        if ($validation_result === true) {
            self::$test_results[] = array('test' => 'Valid Data Validation', 'result' => 'PASS', 'message' => 'Valid data passed validation');
        } else {
            self::$test_results[] = array('test' => 'Valid Data Validation', 'result' => 'FAIL', 'message' => 'Valid data failed validation: ' . implode(', ', $validation_result));
        }
        
        // Test invalid data scenarios
        $invalid_scenarios = array(
            'Empty Email' => array('user_email' => ''),
            'Invalid Email' => array('user_email' => 'invalid-email'),
            'Empty First Name' => array('first_name' => ''),
            'Empty Last Name' => array('last_name' => ''),
            'Empty Username' => array('user_login' => '')
        );
        
        foreach ($invalid_scenarios as $scenario => $invalid_data) {
            $test_data = array_merge($valid_data, $invalid_data);
            $validation_result = MAX_User_Roles::validate_user_data($test_data);
            
            if ($validation_result !== true && is_array($validation_result)) {
                self::$test_results[] = array('test' => "Invalid Data: {$scenario}", 'result' => 'PASS', 'message' => 'Invalid data correctly rejected');
            } else {
                self::$test_results[] = array('test' => "Invalid Data: {$scenario}", 'result' => 'FAIL', 'message' => 'Invalid data was accepted');
            }
        }
    }
    
    /**
     * Test user creation with validation
     */
    private static function test_user_creation() {
        echo "<h3>👥 Testing User Creation</h3>\n";
        
        $user_data = array(
            'user_login' => 'created_professional_' . time(),
            'user_email' => 'created' . time() . '@example.com',
            'first_name' => 'Created',
            'last_name' => 'Professional'
        );
        
        $user_id = MAX_User_Roles::create_professional_user($user_data);
        
        if (!is_wp_error($user_id) && is_numeric($user_id)) {
            self::$test_results[] = array('test' => 'Professional User Creation', 'result' => 'PASS', 'message' => "User created with ID: {$user_id}");
            
            // Test user has correct role
            $user = get_user_by('ID', $user_id);
            if (MAX_User_Roles::is_max_professional($user)) {
                self::$test_results[] = array('test' => 'User Role Assignment', 'result' => 'PASS', 'message' => 'User has correct role');
            } else {
                self::$test_results[] = array('test' => 'User Role Assignment', 'result' => 'FAIL', 'message' => 'User does not have correct role');
            }
            
            // Test default status
            $status = MAX_User_Roles::get_user_status($user_id);
            if ($status === MAX_User_Roles::STATUS_PENDING) {
                self::$test_results[] = array('test' => 'Default User Status', 'result' => 'PASS', 'message' => 'User has correct default status');
            } else {
                self::$test_results[] = array('test' => 'Default User Status', 'result' => 'FAIL', 'message' => "Expected pending, got: {$status}");
            }
            
            // Cleanup
            wp_delete_user($user_id);
            
        } else {
            $error_message = is_wp_error($user_id) ? $user_id->get_error_message() : 'Unknown error';
            self::$test_results[] = array('test' => 'Professional User Creation', 'result' => 'FAIL', 'message' => $error_message);
        }
    }
    
    /**
     * Test status checks
     */
    private static function test_status_checks() {
        echo "<h3>🔍 Testing Status Checks</h3>\n";
        
        if (!self::$test_user_id) {
            self::$test_results[] = array('test' => 'Status Checks', 'result' => 'SKIP', 'message' => 'No test user available');
            return;
        }
        
        $user = get_user_by('ID', self::$test_user_id);
        
        // Test professional check
        if (MAX_User_Roles::is_max_professional($user)) {
            self::$test_results[] = array('test' => 'Professional Check', 'result' => 'PASS', 'message' => 'User correctly identified as professional');
        } else {
            self::$test_results[] = array('test' => 'Professional Check', 'result' => 'FAIL', 'message' => 'User not identified as professional');
        }
        
        // Test access check with active status
        MAX_User_Roles::set_user_status(self::$test_user_id, MAX_User_Roles::STATUS_ACTIVE);
        if (MAX_User_Roles::can_access_professional_features(self::$test_user_id)) {
            self::$test_results[] = array('test' => 'Active User Access', 'result' => 'PASS', 'message' => 'Active user can access features');
        } else {
            self::$test_results[] = array('test' => 'Active User Access', 'result' => 'FAIL', 'message' => 'Active user cannot access features');
        }
        
        // Test access check with inactive status
        MAX_User_Roles::set_user_status(self::$test_user_id, MAX_User_Roles::STATUS_INACTIVE);
        if (!MAX_User_Roles::can_access_professional_features(self::$test_user_id)) {
            self::$test_results[] = array('test' => 'Inactive User Access', 'result' => 'PASS', 'message' => 'Inactive user correctly denied access');
        } else {
            self::$test_results[] = array('test' => 'Inactive User Access', 'result' => 'FAIL', 'message' => 'Inactive user can access features');
        }
    }
    
    /**
     * Cleanup test environment
     */
    private static function cleanup_tests() {
        echo "<h3>🧹 Cleaning up test environment...</h3>\n";
        
        // Delete test user
        if (self::$test_user_id) {
            wp_delete_user(self::$test_user_id);
            echo "<p>✅ Test user deleted</p>\n";
        }
        
        echo "<p class='test-pass'>✅ Cleanup complete</p>\n";
    }
    
    /**
     * Display test results
     */
    private static function display_results() {
        echo "<h3>📊 Test Results Summary</h3>\n";
        
        $total_tests = count(self::$test_results);
        $passed_tests = count(array_filter(self::$test_results, function($result) { return $result['result'] === 'PASS'; }));
        $failed_tests = count(array_filter(self::$test_results, function($result) { return $result['result'] === 'FAIL'; }));
        $skipped_tests = count(array_filter(self::$test_results, function($result) { return $result['result'] === 'SKIP'; }));
        
        echo "<p><strong>Total Tests:</strong> {$total_tests}</p>\n";
        echo "<p class='test-pass'><strong>Passed:</strong> {$passed_tests}</p>\n";
        echo "<p class='test-fail'><strong>Failed:</strong> {$failed_tests}</p>\n";
        echo "<p class='test-warning'><strong>Skipped:</strong> {$skipped_tests}</p>\n";
        
        $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        echo "<p><strong>Success Rate:</strong> {$success_rate}%</p>\n";
        
        // Detailed results table
        echo "<h4>Detailed Results</h4>\n";
        echo "<table>\n";
        echo "<tr><th>Test</th><th>Result</th><th>Message</th></tr>\n";
        
        foreach (self::$test_results as $result) {
            $class = $result['result'] === 'PASS' ? 'test-pass' : ($result['result'] === 'FAIL' ? 'test-fail' : 'test-warning');
            $icon = $result['result'] === 'PASS' ? '✅' : ($result['result'] === 'FAIL' ? '❌' : '⚠️');
            
            echo "<tr class='{$class}'>";
            echo "<td>{$result['test']}</td>";
            echo "<td>{$icon} {$result['result']}</td>";
            echo "<td>{$result['message']}</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        if ($failed_tests === 0) {
            echo "<h3 class='test-pass'>🎉 All tests passed! MAX User Roles is working correctly.</h3>\n";
        } else {
            echo "<h3 class='test-fail'>⚠️ Some tests failed. Please review the issues above.</h3>\n";
        }
    }
}

// Run tests if accessed directly
if (!defined('MAX_USER_MANAGEMENT_TESTING')) {
    MAX_User_Roles_Test::run_all_tests();
}
?>
