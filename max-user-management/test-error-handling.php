<?php
/**
 * Test Error Handling - MAX User Management
 * 
 * This file tests the improved error handling for AJAX requests
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if we're in WordPress admin
if (!is_admin()) {
    wp_die(__('This page can only be accessed from WordPress admin.', 'max-user-management'));
}

echo '<div class="wrap">';
echo '<h1>MAX User Management - Error Handling Test</h1>';

echo '<h2>1. Test Improved Error Handling</h2>';
echo '<p>Click the buttons below to test different error scenarios:</p>';

echo '<div style="margin: 20px 0;">';

// Test button for successful AJAX
echo '<button type="button" class="button button-primary max-ajax-button" 
        data-action="max_test_wings_connection"
        data-loading="Testing connection...">
    <span class="dashicons dashicons-yes"></span>
    Test Successful AJAX
</button>';

echo ' ';

// Test button for error AJAX (non-existent action)
echo '<button type="button" class="button max-ajax-button" 
        data-action="max_nonexistent_action"
        data-loading="Testing error...">
    <span class="dashicons dashicons-warning"></span>
    Test Error AJAX (Non-existent Action)
</button>';

echo ' ';

// Test button for permission error
echo '<button type="button" class="button max-ajax-button" 
        data-action="max_import_wings_customers"
        data-loading="Testing permission error...">
    <span class="dashicons dashicons-lock"></span>
    Test Permission Error (if not admin)
</button>';

echo '</div>';

echo '<h3>Test Results:</h3>';
echo '<div id="error-test-results" style="background: #f1f1f1; padding: 15px; margin: 10px 0; min-height: 100px;"></div>';

echo '<h2>2. JavaScript Error Handling Status</h2>';
echo '<div id="js-status" style="background: #fff; padding: 15px; border: 1px solid #ddd; margin: 10px 0;"></div>';

echo '</div>';

// JavaScript for testing
?>
<script>
jQuery(document).ready(function($) {
    var testResults = $('#error-test-results');
    var jsStatus = $('#js-status');
    
    // Check if required objects are loaded
    var status = '<h4>JavaScript Status:</h4>';
    
    if (typeof max_ajax !== 'undefined') {
        status += '<p style="color: green;">✅ max_ajax object is loaded</p>';
        status += '<p><strong>AJAX URL:</strong> ' + max_ajax.ajax_url + '</p>';
        status += '<p><strong>Nonce:</strong> ' + max_ajax.nonce + '</p>';
    } else {
        status += '<p style="color: red;">❌ max_ajax object is NOT loaded</p>';
    }
    
    if (typeof MAX_Admin !== 'undefined') {
        status += '<p style="color: green;">✅ MAX_Admin object is loaded</p>';
        status += '<p><strong>Error handling:</strong> Improved error handling is active</p>';
    } else {
        status += '<p style="color: red;">❌ MAX_Admin object is NOT loaded</p>';
    }
    
    jsStatus.html(status);
    
    // Monitor AJAX button clicks
    $(document).on('click', '.max-ajax-button', function() {
        var $button = $(this);
        var action = $button.data('action');
        
        testResults.html('<p><strong>Testing AJAX action:</strong> ' + action + '</p>' +
                        '<p><strong>Timestamp:</strong> ' + new Date().toLocaleString() + '</p>' +
                        '<p><strong>Status:</strong> Sending request...</p>');
    });
    
    // Monitor AJAX success events
    $(document).on('max:ajax:success', '.max-ajax-button', function(e, data) {
        testResults.append('<div style="color: green; background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 4px;">' +
                          '<strong>✅ AJAX Success:</strong><br>' +
                          '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                          '</div>');
    });
    
    // Monitor AJAX error events
    $(document).on('max:ajax:error', '.max-ajax-button', function(e, data) {
        testResults.append('<div style="color: #721c24; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 4px;">' +
                          '<strong>❌ AJAX Error (Improved Handling):</strong><br>' +
                          '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                          '</div>');
    });
    
    // Test error handling improvements
    testResults.html('<p><em>Click a test button above to see the improved error handling in action.</em></p>');
});
</script>

<style>
.max-ajax-button {
    margin: 5px;
}
#error-test-results pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
}
</style>
