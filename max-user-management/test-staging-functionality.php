<?php
/**
 * Test fajl za Wings User Mapper funkcionalnost
 */

// Simulacija WordPress okruženja
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Mock WordPress funkcije
function get_option($option, $default = false) {
    $options = array(
        'max_user_mgmt_settings' => array(
            'wings_api_url' => 'https://portal.wings.rs/api/v1/',
            'wings_api_alias' => 'grosstest',
            'wings_api_username' => 'aql',
            'wings_api_password' => 'grossaql'
        )
    );
    return isset($options[$option]) ? $options[$option] : $default;
}

function wp_remote_post($url, $args) {
    // Mock odgovor za login
    if (strpos($url, 'system.user.log') !== false) {
        return array(
            'response' => array('code' => 200),
            'body' => json_encode(array(
                'data' => array(
                    array('attributes' => array('token' => 'mock_session_token'))
                )
            ))
        );
    }
    
    // Mock odgovor za korisnike
    if (strpos($url, 'local.kupac.svi') !== false) {
        return array(
            'response' => array('code' => 200),
            'body' => json_encode(array(
                'data' => array(
                    array(
                        'attributes' => array(
                            'Šifra' => 'TEST001',
                            'Naziv' => 'Test Korisnik',
                            'Adresa' => 'Test Adresa 123',
                            'Mesto' => 'Beograd',
                            'Kontakt' => 'Marko',
                            'Telefon' => '011123456',
                            'Fax' => '011654321',
                            'Mobilni' => '0641234567',
                            'E-mail' => '<EMAIL>',
                            'PIB' => '123456789',
                            'Rabat' => '10%'
                        )
                    ),
                    array(
                        'attributes' => array(
                            'Šifra' => 'TEST002',
                            'Naziv' => 'Test Korisnik 2',
                            'Adresa' => 'Test Adresa 456',
                            'Mesto' => 'Novi Sad',
                            'Kontakt' => 'Ana',
                            'Telefon' => '021123456',
                            'E-mail' => '', // Prazan email
                            'PIB' => '987654321'
                        )
                    )
                )
            ))
        );
    }
    
    return new WP_Error('mock_error', 'Mock error');
}

function wp_remote_retrieve_response_code($response) {
    return $response['response']['code'] ?? 500;
}

function wp_remote_retrieve_body($response) {
    return $response['body'] ?? '';
}

function wp_remote_retrieve_headers($response) {
    return array();
}

function is_wp_error($thing) {
    return $thing instanceof WP_Error;
}

function wp_json_encode($data) {
    return json_encode($data);
}

function wp_insert_user($userdata) {
    // Mock kreiranje korisnika
    return rand(1000, 9999); // Random user ID
}

function wp_generate_password($length = 12, $special_chars = true) {
    return 'mock_password_123';
}

function username_exists($username) {
    return false; // Pretvaramo da korisnik ne postoji
}

function email_exists($email) {
    return false; // Pretvaramo da email ne postoji
}

function update_user_meta($user_id, $meta_key, $meta_value) {
    return true;
}

class WP_Error {
    private $code;
    private $message;
    
    public function __construct($code, $message) {
        $this->code = $code;
        $this->message = $message;
    }
    
    public function get_error_code() {
        return $this->code;
    }
    
    public function get_error_message() {
        return $this->message;
    }
}

// Mock MAX_User_Management klasa
class MAX_User_Management {
    public static function log($message, $level = 'info', $context = array()) {
        echo "[{$level}] {$message}\n";
        if (!empty($context)) {
            echo "Context: " . print_r($context, true) . "\n";
        }
    }
}

// Mock wpdb
global $wpdb;
$wpdb = new stdClass();
$wpdb->prefix = 'wp_';

// Mock database operacije
$wpdb->get_row = function($query, $output = OBJECT) {
    return null; // Pretvaramo da nema postojećih zapisa
};

$wpdb->insert = function($table, $data, $format = null) {
    echo "INSERT INTO {$table}: " . print_r($data, true) . "\n";
    return 1; // Success
};

$wpdb->update = function($table, $data, $where, $format = null, $where_format = null) {
    echo "UPDATE {$table}: " . print_r($data, true) . " WHERE " . print_r($where, true) . "\n";
    return 1; // Success
};

$wpdb->get_results = function($query, $output = OBJECT) {
    // Mock rezultati
    return array(
        (object) array(
            'id' => 1,
            'wings_id' => 'TEST001',
            'username' => 'Test Korisnik',
            'email' => '<EMAIL>',
            'first_name' => 'Marko',
            'city' => 'Beograd',
            'sync_status' => 'pending',
            'wp_user_id' => null
        ),
        (object) array(
            'id' => 2,
            'wings_id' => 'TEST002',
            'username' => 'Test Korisnik 2',
            'email' => '',
            'first_name' => 'Ana',
            'city' => 'Novi Sad',
            'sync_status' => 'pending',
            'wp_user_id' => null
        )
    );
};

$wpdb->get_var = function($query) {
    return 2; // Mock count
};

$wpdb->delete = function($table, $where, $where_format = null) {
    echo "DELETE FROM {$table} WHERE " . print_r($where, true) . "\n";
    return 1; // Success
};

$wpdb->prepare = function($query, ...$args) {
    return vsprintf(str_replace('%s', "'%s'", str_replace('%d', '%d', $query)), $args);
};

$wpdb->last_error = '';

echo "=== Test Wings User Mapper funkcionalnosti ===\n\n";

// Uključi potrebne fajlove
require_once 'includes/class-max-wings-api.php';
require_once 'includes/class-max-wings-user-mapper.php';

echo "1. Testiranje kreiranja MAX_Wings_User_Mapper instance...\n";
$mapper = MAX_Wings_User_Mapper::get_instance();
echo "✓ Instance kreirana uspešno\n\n";

echo "2. Testiranje importa korisnika u staging tabelu...\n";
$result = $mapper->import_wings_customers_to_staging(0, 10);

if (is_wp_error($result)) {
    echo "✗ Greška pri importu: " . $result->get_error_message() . "\n";
} else {
    echo "✓ Import uspešan:\n";
    echo "  - Importovano: " . $result['imported'] . "\n";
    echo "  - Ažurirano: " . $result['updated'] . "\n";
    echo "  - Greške: " . $result['errors'] . "\n";
    echo "  - Ukupno obrađeno: " . $result['total_processed'] . "\n";
}
echo "\n";

echo "3. Testiranje dobijanja korisnika iz staging tabele...\n";
$staging_users = $mapper->get_staging_users('pending', 10, 0);
echo "✓ Dobijeno " . count($staging_users) . " korisnika iz staging tabele\n\n";

echo "4. Testiranje kreiranja WordPress korisnika iz staging podataka...\n";
$wp_user_id = $mapper->create_wp_user_from_staging(1);

if (is_wp_error($wp_user_id)) {
    echo "✗ Greška pri kreiranju WP korisnika: " . $wp_user_id->get_error_message() . "\n";
} else {
    echo "✓ WordPress korisnik kreiran sa ID: " . $wp_user_id . "\n";
}
echo "\n";

echo "5. Testiranje ažuriranja statusa korisnika...\n";
$status_result = $mapper->update_staging_user_status(1, 'synced');
echo "✓ Status ažuriran: " . ($status_result ? 'uspešno' : 'neuspešno') . "\n\n";

echo "6. Testiranje brisanja korisnika iz staging tabele...\n";
$delete_result = $mapper->delete_staging_user(2);
echo "✓ Korisnik obrisan: " . ($delete_result ? 'uspešno' : 'neuspešno') . "\n\n";

echo "=== Svi testovi završeni ===\n";
