<?php
/**
 * Debug Customer Data - MAX User Management
 * 
 * This file helps debug the customer data structure from Wings API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if we're in WordPress admin
if (!is_admin()) {
    wp_die(__('This page can only be accessed from WordPress admin.', 'max-user-management'));
}

echo '<div class="wrap">';
echo '<h1>MAX User Management - Debug Customer Data</h1>';

echo '<h2>1. Test Wings API Connection</h2>';

try {
    // Test if classes exist
    if (!class_exists('MAX_Wings_API')) {
        throw new Exception('MAX_Wings_API class not found');
    }
    
    echo '<p>✅ MAX_Wings_API class found</p>';
    
    // Test API instance
    $api = MAX_Wings_API::get_instance();
    echo '<p>✅ Wings API instance created</p>';
    
    // Test connection
    $connection = $api->test_connection();
    if (is_wp_error($connection)) {
        echo '<p>❌ API Connection failed: ' . $connection->get_error_message() . '</p>';
    } else {
        echo '<p>✅ API Connection successful</p>';
        
        echo '<h2>2. Raw Customer Data from Wings API</h2>';
        
        // Get customers from Wings
        $customers = $api->get_all_customers(0, 5); // Get first 5 customers
        
        if (is_wp_error($customers)) {
            echo '<p>❌ Failed to get customers: ' . $customers->get_error_message() . '</p>';
        } else {
            echo '<p>✅ Retrieved ' . count($customers) . ' customers</p>';
            
            echo '<h3>Raw Customer Data Structure:</h3>';
            echo '<pre style="background: #f1f1f1; padding: 15px; overflow-x: auto; max-height: 400px; overflow-y: auto;">';
            echo htmlspecialchars(print_r($customers, true));
            echo '</pre>';
            
            if (!empty($customers)) {
                echo '<h3>First Customer Analysis:</h3>';
                $first_customer = $customers[0];
                
                echo '<div style="background: #fff; padding: 15px; border: 1px solid #ddd; margin: 10px 0;">';
                echo '<h4>Customer Structure:</h4>';
                echo '<ul>';
                echo '<li><strong>Type:</strong> ' . gettype($first_customer) . '</li>';
                echo '<li><strong>Is Array:</strong> ' . (is_array($first_customer) ? 'Yes' : 'No') . '</li>';
                echo '<li><strong>Is Object:</strong> ' . (is_object($first_customer) ? 'Yes' : 'No') . '</li>';
                
                if (is_array($first_customer)) {
                    echo '<li><strong>Array Count:</strong> ' . count($first_customer) . '</li>';
                    echo '<li><strong>Has Numeric Keys:</strong> ' . (isset($first_customer[0]) ? 'Yes' : 'No') . '</li>';
                    echo '<li><strong>Array Keys:</strong> ' . implode(', ', array_keys($first_customer)) . '</li>';
                    
                    // Check for common Wings fields
                    $wings_fields = ['sifra', 'naziv', 'adresa', 'mesto', 'kontakt', 'telefon', 'email'];
                    echo '<li><strong>Wings Fields Present:</strong><ul>';
                    foreach ($wings_fields as $field) {
                        $present = isset($first_customer[$field]) ? 'Yes' : 'No';
                        $value = isset($first_customer[$field]) ? $first_customer[$field] : 'N/A';
                        echo "<li>{$field}: {$present} (Value: " . htmlspecialchars($value) . ")</li>";
                    }
                    echo '</ul></li>';
                    
                    // Check for attributes structure
                    if (isset($first_customer['attributes'])) {
                        echo '<li><strong>Has Attributes:</strong> Yes</li>';
                        echo '<li><strong>Attributes Type:</strong> ' . gettype($first_customer['attributes']) . '</li>';
                        if (is_array($first_customer['attributes'])) {
                            echo '<li><strong>Attributes Keys:</strong> ' . implode(', ', array_keys($first_customer['attributes'])) . '</li>';
                        }
                    } else {
                        echo '<li><strong>Has Attributes:</strong> No</li>';
                    }
                }
                echo '</ul>';
                echo '</div>';
                
                echo '<h3>Validation Test:</h3>';
                echo '<div style="background: #fff; padding: 15px; border: 1px solid #ddd; margin: 10px 0;">';
                
                // Test the validation logic
                $has_sifra = false;
                $has_naziv = false;
                
                if (is_array($first_customer)) {
                    // Check direct fields
                    if (!empty($first_customer['sifra'])) {
                        $has_sifra = true;
                        echo '<p>✅ Direct sifra found: ' . htmlspecialchars($first_customer['sifra']) . '</p>';
                    }
                    if (!empty($first_customer['naziv'])) {
                        $has_naziv = true;
                        echo '<p>✅ Direct naziv found: ' . htmlspecialchars($first_customer['naziv']) . '</p>';
                    }
                    
                    // Check attributes
                    if (isset($first_customer['attributes']) && is_array($first_customer['attributes'])) {
                        if (!empty($first_customer['attributes']['sifra'])) {
                            $has_sifra = true;
                            echo '<p>✅ Attributes sifra found: ' . htmlspecialchars($first_customer['attributes']['sifra']) . '</p>';
                        }
                        if (!empty($first_customer['attributes']['naziv'])) {
                            $has_naziv = true;
                            echo '<p>✅ Attributes naziv found: ' . htmlspecialchars($first_customer['attributes']['naziv']) . '</p>';
                        }
                    }
                    
                    // Check numeric array format
                    if (isset($first_customer[0])) {
                        echo '<p>📋 Numeric array format detected</p>';
                        echo '<p>Index 0 (sifra): ' . htmlspecialchars($first_customer[0] ?? 'empty') . '</p>';
                        echo '<p>Index 1 (naziv): ' . htmlspecialchars($first_customer[1] ?? 'empty') . '</p>';
                        
                        if (!empty($first_customer[0])) $has_sifra = true;
                        if (!empty($first_customer[1])) $has_naziv = true;
                    }
                }
                
                echo '<h4>Validation Result:</h4>';
                if ($has_sifra || $has_naziv) {
                    echo '<p style="color: green;">✅ Customer would pass validation (has sifra or naziv)</p>';
                } else {
                    echo '<p style="color: red;">❌ Customer would fail validation (missing both sifra and naziv)</p>';
                    echo '<p><strong>This is likely the cause of "Nema validnih kupaca za import" error!</strong></p>';
                }
                
                echo '</div>';
            }
        }
    }
    
} catch (Exception $e) {
    echo '<p>❌ Error: ' . $e->getMessage() . '</p>';
}

echo '</div>';
?>
