<?php
/**
 * Plugin Name: MAX User Management
 * Plugin URI: https://maxwebsajt.com/max-user-management
 * Description: Admin-uprav<PERSON><PERSON>i koris<PERSON>č<PERSON> nalozi sa integracijom Wings Portal sistema za MAX web sajt.
 * Version: 1.0.0
 * Author: MAX Development Team
 * Author URI: https://maxwebsajt.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: max-user-management
 * Domain Path: /languages
 * Requires at least: 5.5
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.5
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MAX_USER_MGMT_VERSION', '1.0.0');
define('MAX_USER_MGMT_PLUGIN_FILE', __FILE__);
define('MAX_USER_MGMT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MAX_USER_MGMT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MAX_USER_MGMT_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Declare HPOS compatibility BEFORE any class definitions
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});

/**
 * Main MAX User Management Class
 */
class MAX_User_Management {

    /**
     * Plugin instance
     */
    private static $instance = null;

    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Note: HPOS compatibility is declared at plugin level
        // Individual compatibility checks can be added here if needed

        // Load plugin text domain
        load_plugin_textdomain('max-user-management', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Include required files
        $this->includes();

        // Initialize components
        $this->init_hooks();
    }

    /**
     * Include required files
     */
    private function includes() {
        // Include config class first
        require_once MAX_USER_MGMT_PLUGIN_DIR . 'includes/class-max-config.php';

        // Include core classes
        require_once MAX_USER_MGMT_PLUGIN_DIR . 'includes/class-max-wings-api.php';
        require_once MAX_USER_MGMT_PLUGIN_DIR . 'includes/class-max-user-roles.php';
        require_once MAX_USER_MGMT_PLUGIN_DIR . 'includes/class-max-wings-user-mapper.php';

        // Include admin panel (always include as it exists now)
        require_once MAX_USER_MGMT_PLUGIN_DIR . 'includes/class-max-admin-panel.php';
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize core components
        MAX_User_Roles::get_instance();

        // Initialize admin panel
        if (is_admin()) {
            MAX_Admin_Panel::get_instance();
        }

        // Frontend hooks only (admin scripts handled by MAX_Admin_Panel)
        add_action('wp_enqueue_scripts', array($this, 'frontend_scripts'));
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Include required files for activation
        $this->includes();

        // Create custom user role
        MAX_User_Roles::create_professionals_role();

        // Create database tables
        $this->create_tables();

        // Set default options
        $default_settings = array(
            'wings_api_url' => 'https://portal.wings.rs/api/v1/',
            'wings_api_alias' => '',
            'wings_api_username' => '',
            'wings_api_password' => '',
            'auto_send_credentials' => true,
            'password_length' => 12,
            'require_password_change' => true,
            'session_timeout' => 24, // hours
            'max_login_attempts' => 5
        );

        foreach ($default_settings as $key => $value) {
            if (get_option('max_user_mgmt_' . $key) === false) {
                add_option('max_user_mgmt_' . $key, $value);
            }
        }

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('max_sync_wings_users');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Admin audit log table
        $table_name = $wpdb->prefix . 'max_admin_log';
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            admin_id bigint(20) NOT NULL,
            action varchar(100) NOT NULL,
            target_user_id bigint(20) DEFAULT NULL,
            details text,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            ip_address varchar(45),
            PRIMARY KEY (id),
            KEY admin_id (admin_id),
            KEY target_user_id (target_user_id),
            KEY timestamp (timestamp)
        ) $charset_collate;";

        // User sync status table
        $sync_table = $wpdb->prefix . 'max_user_sync_status';
        $sync_sql = "CREATE TABLE $sync_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            wings_customer_id varchar(50),
            sync_status varchar(20) DEFAULT 'pending',
            last_sync datetime,
            sync_errors text,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id),
            KEY wings_customer_id (wings_customer_id),
            KEY sync_status (sync_status)
        ) $charset_collate;";

        // Wings users staging table - za mapiranje korisnika iz Wings-a
        $wings_users_table = $wpdb->prefix . 'max_wings_users_staging';
        $wings_users_sql = "CREATE TABLE $wings_users_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            wings_id varchar(50) NOT NULL COMMENT 'Šifra iz Wings-a',
            username varchar(100) NOT NULL COMMENT 'Naziv iz Wings-a',
            address text COMMENT 'Adresa iz Wings-a',
            city varchar(100) COMMENT 'Mesto iz Wings-a',
            first_name varchar(100) COMMENT 'Kontakt iz Wings-a',
            phone varchar(50) COMMENT 'Telefon iz Wings-a',
            phone_2 varchar(50) COMMENT 'Fax iz Wings-a',
            phone_3 varchar(50) COMMENT 'Mobilni iz Wings-a',
            email varchar(100) COMMENT 'E-mail iz Wings-a',
            working_hours text COMMENT 'Radno vreme iz Wings-a',
            status varchar(50) COMMENT 'Status iz Wings-a',
            pib varchar(50) COMMENT 'PIB iz Wings-a',
            discount varchar(50) COMMENT 'Rabat iz Wings-a',
            wp_user_id bigint(20) DEFAULT NULL COMMENT 'ID WordPress korisnika kada se kreira',
            sync_status varchar(20) DEFAULT 'pending' COMMENT 'Status sinhronizacije',
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY wings_id (wings_id),
            KEY wp_user_id (wp_user_id),
            KEY sync_status (sync_status),
            KEY email (email),
            KEY username (username)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($sync_sql);
        dbDelta($wings_users_sql);
    }



    /**
     * Frontend scripts and styles
     */
    public function frontend_scripts() {
        // Only enqueue if files exist
        $css_file = MAX_USER_MGMT_PLUGIN_DIR . 'assets/css/frontend.css';
        $js_file = MAX_USER_MGMT_PLUGIN_DIR . 'assets/js/frontend.js';

        if (file_exists($css_file)) {
            wp_enqueue_style(
                'max-frontend-style',
                MAX_USER_MGMT_PLUGIN_URL . 'assets/css/frontend.css',
                array(),
                MAX_USER_MGMT_VERSION
            );
        }

        if (file_exists($js_file)) {
            wp_enqueue_script(
                'max-frontend-script',
                MAX_USER_MGMT_PLUGIN_URL . 'assets/js/frontend.js',
                array('jquery'),
                MAX_USER_MGMT_VERSION,
                true
            );
        }
    }

    /**
     * HPOS compatibility notice (simplified to prevent output issues)
     */
    public function hpos_compatibility_notice() {
        // Simplified notice to prevent output during activation
        echo '<div class="notice notice-info"><p>MAX User Management: HPOS compatibility declared.</p></div>';
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('MAX User Management plugin zahteva da WooCommerce bude aktivan.', 'max-user-management'); ?></p>
        </div>
        <?php
    }

    /**
     * Enhanced logging system
     */
    public static function log($message, $level = 'info', $context = array()) {
        // Get debug setting
        $settings = get_option('max_user_mgmt_settings', array());
        $debug_enabled = !empty($settings['enable_debug']);

        // Always log errors, only log other levels if debug is enabled
        if ($level !== 'error' && !$debug_enabled) {
            return;
        }

        // Create log entry
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'level' => $level,
            'message' => $message,
            'context' => $context
        );

        // Store in database
        $logs = get_option('max_user_mgmt_log', array());
        $logs[] = $log_entry;

        // Keep only last 500 entries
        if (count($logs) > 500) {
            $logs = array_slice($logs, -500);
        }

        update_option('max_user_mgmt_log', $logs);

        // Also log to WooCommerce if available
        if (function_exists('wc_get_logger')) {
            $logger = wc_get_logger();
            $logger->log($level, $message, array('source' => 'max-user-management'));
        }

        // Also log to WordPress debug log if enabled
        if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            error_log("MAX User Management [{$level}]: {$message}");
        }

        // For debug mode, also log context data
        if ($debug_enabled && !empty($context)) {
            error_log("MAX User Management [{$level}] Context: " . print_r($context, true));
        }
    }
}

// Initialize the plugin
MAX_User_Management::get_instance();