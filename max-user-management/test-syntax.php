<?php
/**
 * Simple syntax test for MAX User Management plugin
 */

// Mock WordPress functions to prevent fatal errors
if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) { return dirname($file) . '/'; }
}
if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) { return 'http://example.com/'; }
}
if (!function_exists('plugin_basename')) {
    function plugin_basename($file) { return basename($file); }
}
if (!function_exists('add_action')) {
    function add_action($hook, $callback) { return true; }
}
if (!function_exists('class_exists')) {
    function class_exists($class) { return false; }
}
if (!function_exists('get_option')) {
    function get_option($option, $default = false) { return $default; }
}
if (!function_exists('update_option')) {
    function update_option($option, $value) { return true; }
}
if (!function_exists('add_option')) {
    function add_option($option, $value) { return true; }
}
if (!function_exists('flush_rewrite_rules')) {
    function flush_rewrite_rules() { return true; }
}
if (!function_exists('wp_clear_scheduled_hook')) {
    function wp_clear_scheduled_hook($hook) { return true; }
}
if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) { return true; }
}
if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) { return true; }
}
if (!function_exists('load_plugin_textdomain')) {
    function load_plugin_textdomain($domain, $deprecated, $path) { return true; }
}
if (!function_exists('is_admin')) {
    function is_admin() { return true; }
}
if (!function_exists('admin_url')) {
    function admin_url($path) { return 'http://example.com/wp-admin/' . $path; }
}
if (!function_exists('wp_create_nonce')) {
    function wp_create_nonce($action) { return 'test_nonce'; }
}
if (!function_exists('wp_enqueue_style')) {
    function wp_enqueue_style($handle, $src, $deps = array(), $ver = false) { return true; }
}
if (!function_exists('wp_enqueue_script')) {
    function wp_enqueue_script($handle, $src, $deps = array(), $ver = false, $in_footer = false) { return true; }
}
if (!function_exists('wp_localize_script')) {
    function wp_localize_script($handle, $object_name, $l10n) { return true; }
}
if (!function_exists('wc_get_logger')) {
    function wc_get_logger() { return new stdClass(); }
}
if (!function_exists('error_log')) {
    function error_log($message) { return true; }
}
if (!function_exists('__')) {
    function __($text, $domain = 'default') { return $text; }
}
if (!function_exists('_e')) {
    function _e($text, $domain = 'default') { echo $text; }
}
if (!defined('ABSPATH')) {
    define('ABSPATH', '/');
}

// Mock global variables
global $wpdb;
$wpdb = new stdClass();
$wpdb->prefix = 'wp_';

// Mock WP_Error class
if (!class_exists('WP_Error')) {
    class WP_Error {
        public function __construct($code, $message) {}
    }
}

echo "Testing syntax of MAX User Management plugin files...\n";

// Test main plugin file
echo "Testing main plugin file...\n";
try {
    include_once 'max-user-management.php';
    echo "✓ Main plugin file syntax OK\n";
} catch (ParseError $e) {
    echo "✗ Parse error in main plugin file: " . $e->getMessage() . "\n";
    exit(1);
} catch (Error $e) {
    echo "✓ Main plugin file syntax OK (runtime error expected: " . $e->getMessage() . ")\n";
} catch (Exception $e) {
    echo "✓ Main plugin file syntax OK (exception expected: " . $e->getMessage() . ")\n";
}

echo "All syntax tests completed!\n";
