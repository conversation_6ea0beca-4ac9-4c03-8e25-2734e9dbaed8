<?php
/**
 * Test AJAX Import Functionality
 * Access via: /wp-admin/admin.php?page=max-test-ajax-import
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have permission to access this page.'));
}

echo '<div class="wrap">';
echo '<h1>MAX User Management - AJAX Import Test</h1>';

echo '<h2>1. JavaScript Test</h2>';
echo '<p>Check if max_ajax object is loaded:</p>';
echo '<div id="js-test-results" style="background: #f1f1f1; padding: 15px; margin: 10px 0;"></div>';

echo '<h2>2. Manual AJAX Test</h2>';
echo '<p>Test the import functionality manually:</p>';

echo '<button type="button" class="button button-primary max-ajax-button" 
        data-action="max_import_wings_customers"
        data-confirm="This is a test import. Continue?"
        data-loading="Testing import...">
    <span class="dashicons dashicons-download"></span>
    Test Import from Wings
</button>';

echo '<div id="ajax-test-results" style="background: #f9f9f9; padding: 15px; margin: 15px 0; border: 1px solid #ddd;"></div>';

echo '<h2>3. Direct PHP Test</h2>';
echo '<p>Test the import method directly:</p>';

if (isset($_GET['test_direct']) && $_GET['test_direct'] === '1') {
    echo '<div style="background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffeaa7;">';
    echo '<h4>Direct PHP Test Results:</h4>';
    
    try {
        // Test if classes exist
        if (!class_exists('MAX_Wings_API')) {
            throw new Exception('MAX_Wings_API class not found');
        }
        
        if (!class_exists('MAX_Admin_Panel')) {
            throw new Exception('MAX_Admin_Panel class not found');
        }
        
        echo '<p>✅ Required classes found</p>';
        
        // Test API instance
        $api = MAX_Wings_API::get_instance();
        echo '<p>✅ Wings API instance created</p>';
        
        // Test connection
        $connection = $api->test_connection();
        if (is_wp_error($connection)) {
            echo '<p>❌ API Connection failed: ' . $connection->get_error_message() . '</p>';
        } else {
            echo '<p>✅ API Connection successful</p>';
            echo '<pre>' . print_r($connection, true) . '</pre>';
        }
        
    } catch (Exception $e) {
        echo '<p>❌ Error: ' . $e->getMessage() . '</p>';
    }
    
    echo '</div>';
} else {
    echo '<a href="?page=max-test-ajax-import&test_direct=1" class="button">Run Direct PHP Test</a>';
}

echo '<h2>4. Debug Information</h2>';
echo '<table class="widefat">';
echo '<tr><th>Setting</th><th>Value</th></tr>';

$debug_info = array(
    'WordPress AJAX URL' => admin_url('admin-ajax.php'),
    'Current User Can Manage Options' => current_user_can('manage_options') ? 'Yes' : 'No',
    'MAX_Wings_API Class Exists' => class_exists('MAX_Wings_API') ? 'Yes' : 'No',
    'MAX_Admin_Panel Class Exists' => class_exists('MAX_Admin_Panel') ? 'Yes' : 'No',
    'jQuery Loaded' => wp_script_is('jquery', 'enqueued') ? 'Yes' : 'No',
    'Admin Script Loaded' => wp_script_is('max-admin-script', 'enqueued') ? 'Yes' : 'No',
    'Current Hook' => current_filter(),
    'Is Admin' => is_admin() ? 'Yes' : 'No'
);

foreach ($debug_info as $key => $value) {
    echo "<tr><td>{$key}</td><td>{$value}</td></tr>";
}
echo '</table>';

echo '</div>';

// JavaScript for testing
?>
<script>
jQuery(document).ready(function($) {
    // Test if max_ajax object exists
    var testResults = $('#js-test-results');
    
    if (typeof max_ajax !== 'undefined') {
        testResults.html('<p style="color: green;">✅ max_ajax object is loaded</p>' +
                        '<p><strong>AJAX URL:</strong> ' + max_ajax.ajax_url + '</p>' +
                        '<p><strong>Nonce:</strong> ' + max_ajax.nonce + '</p>' +
                        '<p><strong>Strings:</strong> ' + JSON.stringify(max_ajax.strings) + '</p>');
    } else {
        testResults.html('<p style="color: red;">❌ max_ajax object is NOT loaded</p>');
    }
    
    // Test if MAX_Admin object exists
    if (typeof MAX_Admin !== 'undefined') {
        testResults.append('<p style="color: green;">✅ MAX_Admin object is loaded</p>');
    } else {
        testResults.append('<p style="color: red;">❌ MAX_Admin object is NOT loaded</p>');
    }
    
    // Monitor AJAX button clicks
    $(document).on('click', '.max-ajax-button', function() {
        var $button = $(this);
        var action = $button.data('action');
        
        $('#ajax-test-results').html('<p><strong>Button clicked!</strong></p>' +
                                   '<p>Action: ' + action + '</p>' +
                                   '<p>Timestamp: ' + new Date().toLocaleString() + '</p>');
    });
    
    // Monitor AJAX success/error events
    $(document).on('max:ajax:success', '.max-ajax-button', function(e, data) {
        $('#ajax-test-results').append('<p style="color: green;"><strong>AJAX Success:</strong> ' + JSON.stringify(data) + '</p>');
    });
    
    $(document).on('max:ajax:error', '.max-ajax-button', function(e, data) {
        $('#ajax-test-results').append('<p style="color: red;"><strong>AJAX Error:</strong> ' + JSON.stringify(data) + '</p>');
    });
});
</script>

<style>
.max-ajax-button {
    margin: 10px 0;
}
</style>
<?php

// Add this page to admin menu temporarily
add_action('admin_menu', function() {
    add_submenu_page(
        'max-user-management',
        'AJAX Import Test',
        'AJAX Import Test',
        'manage_options',
        'max-test-ajax-import',
        function() {
            // Content is already output above
        }
    );
});
?>
