# MAX User Management - HPOS Compatibility Guide

## 🚨 **Critical Issue Resolved: HPOS Compatibility**

### **Problem:**
```
This plugin is incompatible with the enabled WooCommerce feature 'High-Performance order storage', it shouldn't be activated.
```

### **Root Cause:**
WooCommerce's High-Performance Order Storage (HPOS) changes how orders are stored and accessed. Plugins that work with WooCommerce data must explicitly declare HPOS compatibility.

## ✅ **HPOS Compatibility Implementation**

### **1. Plugin Header Declaration**
```php
/**
 * Plugin Name: MAX User Management
 * ...
 * Woo: 8.5.0
 */

// Declare HPOS compatibility
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});
```

### **2. HPOS Compatibility Helper Class**
Created `class-max-hpos-compatibility.php` with comprehensive HPOS support:

```php
class MAX_HPOS_Compatibility {
    // Check if HPOS is enabled
    public static function is_hpos_enabled();
    
    // Check if plugin is compatible
    public static function is_hpos_compatible();
    
    // HPOS-compatible order methods
    public static function get_order($order_id);
    public static function get_orders($args);
    public static function get_order_meta($order_id, $meta_key);
    public static function update_order_meta($order_id, $meta_key, $meta_value);
    
    // Display compatibility notices
    public static function display_compatibility_notice($plugin_name);
}
```

### **3. Main Plugin Integration**
```php
// Check HPOS compatibility during initialization
if (!MAX_HPOS_Compatibility::is_hpos_compatible()) {
    add_action('admin_notices', array($this, 'hpos_compatibility_notice'));
    return;
}
```

## 🔧 **HPOS-Compatible Methods**

### **Order Retrieval:**
```php
// ❌ Old way (not HPOS compatible)
$order = new WC_Order($order_id);

// ✅ New way (HPOS compatible)
$order = MAX_HPOS_Compatibility::get_order($order_id);
```

### **Order Meta:**
```php
// ❌ Old way
$meta = get_post_meta($order_id, 'meta_key', true);

// ✅ New way
$meta = MAX_HPOS_Compatibility::get_order_meta($order_id, 'meta_key');
```

### **Order Queries:**
```php
// ❌ Old way
$orders = get_posts(array('post_type' => 'shop_order'));

// ✅ New way
$orders = MAX_HPOS_Compatibility::get_orders(array('status' => 'completed'));
```

## 📋 **Compatibility Checklist**

### **✅ Implemented:**
- [x] HPOS compatibility declaration
- [x] FeaturesUtil integration
- [x] OrderUtil usage detection
- [x] HPOS-compatible order methods
- [x] Compatibility status checking
- [x] Admin notices for incompatibility
- [x] Fallback methods for legacy systems

### **🔄 For Future Development:**
- [ ] HPOS-compatible order creation
- [ ] HPOS-compatible order status updates
- [ ] HPOS-compatible order search
- [ ] HPOS-compatible bulk operations
- [ ] HPOS performance optimizations

## 🚀 **How to Use HPOS Methods**

### **1. Check HPOS Status:**
```php
$status = MAX_HPOS_Compatibility::get_hpos_status();
echo "HPOS Enabled: " . ($status['hpos_enabled'] ? 'Yes' : 'No');
echo "Plugin Compatible: " . ($status['hpos_compatible'] ? 'Yes' : 'No');
```

### **2. Get Orders HPOS-Compatible Way:**
```php
// Get customer orders
$orders = MAX_HPOS_Compatibility::get_customer_orders($customer_id);

// Search orders
$orders = MAX_HPOS_Compatibility::search_orders('search term');

// Get order count
$count = MAX_HPOS_Compatibility::get_order_count(array('status' => 'completed'));
```

### **3. Work with Order Meta:**
```php
// Get meta
$value = MAX_HPOS_Compatibility::get_order_meta($order_id, 'wings_sync_status');

// Update meta
MAX_HPOS_Compatibility::update_order_meta($order_id, 'wings_sync_status', 'synced');

// Delete meta
MAX_HPOS_Compatibility::delete_order_meta($order_id, 'old_meta_key');
```

## ⚠️ **Important Notes for Plugin Development**

### **1. Always Declare Compatibility:**
```php
// Add this to EVERY WooCommerce plugin
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});
```

### **2. Use WooCommerce Functions:**
```php
// ✅ Always use wc_get_order() instead of new WC_Order()
$order = wc_get_order($order_id);

// ✅ Always use wc_get_orders() instead of get_posts()
$orders = wc_get_orders($args);
```

### **3. Avoid Direct Database Queries:**
```php
// ❌ Don't do this
$wpdb->get_results("SELECT * FROM {$wpdb->posts} WHERE post_type = 'shop_order'");

// ✅ Do this instead
$orders = wc_get_orders(array('limit' => -1));
```

### **4. Test Both Modes:**
- Test with HPOS enabled
- Test with HPOS disabled
- Test migration scenarios

## 🧪 **Testing HPOS Compatibility**

### **1. Enable HPOS:**
1. Go to WooCommerce > Settings > Advanced > Features
2. Enable "High-performance order storage"
3. Test plugin functionality

### **2. Check Compatibility:**
```php
// Run this test
$status = MAX_HPOS_Compatibility::get_hpos_status();
var_dump($status);

// Check recommendations
$recommendations = MAX_HPOS_Compatibility::get_compatibility_recommendations();
foreach ($recommendations as $rec) {
    echo $rec['type'] . ': ' . $rec['message'] . "\n";
}
```

### **3. Migration Testing:**
1. Create orders with HPOS disabled
2. Enable HPOS
3. Run WooCommerce migration
4. Test plugin with migrated data

## 📚 **Resources**

- [WooCommerce HPOS Documentation](https://woocommerce.com/document/high-performance-order-storage/)
- [HPOS Developer Guide](https://github.com/woocommerce/woocommerce/wiki/High-Performance-Order-Storage-Upgrade-Recipe-Book)
- [WooCommerce Utilities](https://github.com/woocommerce/woocommerce/tree/trunk/plugins/woocommerce/src/Utilities)

## 🎯 **Summary**

The MAX User Management plugin now includes:

1. **Full HPOS compatibility declaration**
2. **Comprehensive HPOS helper class**
3. **HPOS-compatible order methods**
4. **Compatibility checking and notices**
5. **Future-proof architecture**

**Result**: Plugin will now activate successfully with HPOS enabled and provide proper compatibility notices if issues arise.

## 🔄 **For Future Plugins**

**Always remember to:**
1. Declare HPOS compatibility in plugin header
2. Use `wc_get_order()` and `wc_get_orders()`
3. Avoid direct post meta queries for orders
4. Test with both HPOS enabled and disabled
5. Include compatibility checking in plugin initialization

This ensures all WooCommerce plugins work seamlessly with High-Performance Order Storage! 🚀
