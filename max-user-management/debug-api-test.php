<?php
/**
 * Debug API Test Page for MAX User Management
 * Access via: /wp-admin/admin.php?page=max-debug-api-test
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have permission to access this page.'));
}

// Enable debug mode temporarily
$settings = get_option('max_user_mgmt_settings', array());
$settings['enable_debug'] = true;
update_option('max_user_mgmt_settings', $settings);

echo '<div class="wrap">';
echo '<h1>MAX User Management - API Debug Test</h1>';

// Clear previous logs
if (isset($_GET['clear_logs'])) {
    delete_option('max_user_mgmt_log');
    echo '<div class="notice notice-success"><p>Logs cleared!</p></div>';
}

echo '<p><a href="?page=max-debug-api-test&clear_logs=1" class="button">Clear Logs</a></p>';

echo '<h2>1. Current Configuration</h2>';
echo '<table class="widefat">';
echo '<tr><th>Setting</th><th>Value</th></tr>';

// Get settings from both possible locations
$settings_array = get_option('max_user_mgmt_settings', array());
$individual_settings = array(
    'api_url' => get_option('max_user_mgmt_wings_api_url', ''),
    'api_alias' => get_option('max_user_mgmt_wings_api_alias', ''),
    'api_username' => get_option('max_user_mgmt_wings_api_username', ''),
    'api_password' => get_option('max_user_mgmt_wings_api_password', ''),
);

$api_settings = array(
    'API URL' => $settings_array['wings_api_url'] ?? $individual_settings['api_url'] ?: 'Not set',
    'API Alias' => $settings_array['wings_api_alias'] ?? $individual_settings['api_alias'] ?: 'Not set',
    'API Username' => ($settings_array['wings_api_username'] ?? $individual_settings['api_username']) ? 'Set' : 'Not set',
    'API Password' => ($settings_array['wings_api_password'] ?? $individual_settings['api_password']) ? 'Set' : 'Not set',
    'Debug Mode' => $settings['enable_debug'] ? 'Enabled' : 'Disabled',
    'Settings Source' => !empty($settings_array) ? 'Array (max_user_mgmt_settings)' : 'Individual options'
);

foreach ($api_settings as $key => $value) {
    echo "<tr><td>{$key}</td><td>{$value}</td></tr>";
}
echo '</table>';

echo '<h2>2. Testing API Connection</h2>';

try {
    // Test if class exists
    if (!class_exists('MAX_Wings_API')) {
        echo '<p style="color: red;">❌ MAX_Wings_API class not found!</p>';
    } else {
        echo '<p style="color: green;">✅ MAX_Wings_API class found</p>';
        
        // Get API instance
        $api = MAX_Wings_API::get_instance();
        echo '<p style="color: green;">✅ API instance created</p>';
        
        // Test connection
        echo '<p>Testing connection...</p>';
        $result = $api->test_connection();
        
        if (is_wp_error($result)) {
            echo '<p style="color: red;">❌ Connection failed:</p>';
            echo '<ul>';
            echo '<li><strong>Error Code:</strong> ' . $result->get_error_code() . '</li>';
            echo '<li><strong>Error Message:</strong> ' . $result->get_error_message() . '</li>';
            echo '</ul>';
        } else {
            echo '<p style="color: green;">✅ Connection successful!</p>';
            echo '<pre>' . print_r($result, true) . '</pre>';
        }
    }
} catch (Exception $e) {
    echo '<p style="color: red;">❌ Exception occurred: ' . $e->getMessage() . '</p>';
}

echo '<h2>3. Recent Debug Logs</h2>';
$logs = get_option('max_user_mgmt_log', array());
$recent_logs = array_slice($logs, -20); // Last 20 entries

if (empty($recent_logs)) {
    echo '<p>No logs found. Make sure debug mode is enabled.</p>';
} else {
    echo '<table class="widefat">';
    echo '<tr><th>Time</th><th>Level</th><th>Message</th><th>Context</th></tr>';
    
    foreach (array_reverse($recent_logs) as $log) {
        $level_color = $log['level'] === 'error' ? 'red' : ($log['level'] === 'debug' ? 'blue' : 'black');
        $context_display = !empty($log['context']) ? '<details><summary>View</summary><pre>' . print_r($log['context'], true) . '</pre></details>' : '';
        
        echo "<tr>";
        echo "<td>{$log['timestamp']}</td>";
        echo "<td style='color: {$level_color}; font-weight: bold;'>" . strtoupper($log['level']) . "</td>";
        echo "<td>{$log['message']}</td>";
        echo "<td>{$context_display}</td>";
        echo "</tr>";
    }
    echo '</table>';
}

echo '<h2>4. WordPress Debug Information</h2>';
echo '<table class="widefat">';
echo '<tr><th>Setting</th><th>Value</th></tr>';

$debug_info = array(
    'WP_DEBUG' => defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled',
    'WP_DEBUG_LOG' => defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled',
    'WP_DEBUG_DISPLAY' => defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? 'Enabled' : 'Disabled',
    'Error Log Location' => ini_get('error_log') ?: 'Default location',
    'WooCommerce Active' => class_exists('WooCommerce') ? 'Yes' : 'No',
    'WC Logger Available' => function_exists('wc_get_logger') ? 'Yes' : 'No'
);

foreach ($debug_info as $key => $value) {
    echo "<tr><td>{$key}</td><td>{$value}</td></tr>";
}
echo '</table>';

echo '<h2>5. Manual API Test</h2>';
echo '<p>You can also test the API manually by clicking the "Test Connection" button in the MAX Users admin panel.</p>';

echo '<h2>6. Troubleshooting Tips</h2>';
echo '<ul>';
echo '<li><strong>No logs appearing:</strong> Make sure debug mode is enabled in MAX Users settings</li>';
echo '<li><strong>Connection timeout:</strong> Check if your server can reach portal.wings.rs</li>';
echo '<li><strong>Invalid credentials:</strong> Verify your API username and password</li>';
echo '<li><strong>Wrong alias:</strong> Make sure you\'re using the correct API alias (e.g., "grosstest" for test environment)</li>';
echo '</ul>';

echo '</div>';

// Add this page to admin menu temporarily
add_action('admin_menu', function() {
    add_submenu_page(
        'max-user-management',
        'API Debug Test',
        'API Debug Test',
        'manage_options',
        'max-debug-api-test',
        function() {
            // Content is already output above
        }
    );
});
?>
