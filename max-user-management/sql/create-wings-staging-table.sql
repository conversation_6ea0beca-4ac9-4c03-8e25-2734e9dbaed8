-- Kreiranje tabele za Wings korisničko mapiranje
-- Ova tabela se automatski kreira pri aktivaciji plugin-a

CREATE TABLE IF NOT EXISTS wp_max_wings_users_staging (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    wings_id varchar(50) NOT NULL COMMENT 'Šifra iz Wings-a',
    username varchar(100) NOT NULL COMMENT 'Naziv iz Wings-a',
    address text COMMENT 'Adresa iz Wings-a',
    city varchar(100) COMMENT 'Mesto iz Wings-a',
    first_name varchar(100) COMMENT 'Kontakt iz Wings-a',
    phone varchar(50) COMMENT 'Telefon iz Wings-a',
    phone_2 varchar(50) COMMENT 'Fax iz Wings-a',
    phone_3 varchar(50) COMMENT 'Mobilni iz Wings-a',
    email varchar(100) COMMENT 'E-mail iz Wings-a',
    working_hours text COMMENT 'Radno vreme iz Wings-a',
    status varchar(50) COMMENT 'Status iz Wings-a',
    pib varchar(50) COMMENT 'PIB iz Wings-a',
    discount varchar(50) COMMENT 'Rabat iz Wings-a',
    wp_user_id bigint(20) DEFAULT NULL COMMENT 'ID WordPress korisnika kada se kreira',
    sync_status varchar(20) DEFAULT 'pending' COMMENT 'Status sinhronizacije (pending, synced, error)',
    created_date datetime DEFAULT CURRENT_TIMESTAMP,
    updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY wings_id (wings_id),
    KEY wp_user_id (wp_user_id),
    KEY sync_status (sync_status),
    KEY email (email),
    KEY username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Staging tabela za mapiranje Wings korisnika';

-- Indeksi za optimizaciju performansi
CREATE INDEX idx_wings_staging_status_created ON wp_max_wings_users_staging (sync_status, created_date);
CREATE INDEX idx_wings_staging_email_status ON wp_max_wings_users_staging (email, sync_status);

-- Primer upita za dobijanje statistika
-- SELECT 
--     sync_status,
--     COUNT(*) as count
-- FROM wp_max_wings_users_staging 
-- GROUP BY sync_status;

-- Primer upita za dobijanje korisnika na čekanju
-- SELECT * FROM wp_max_wings_users_staging 
-- WHERE sync_status = 'pending' 
-- ORDER BY created_date DESC 
-- LIMIT 50;
