<?php
/**
 * MAX User Management - Output Diagnostic Tool
 * 
 * This script helps identify what's causing unexpected output during plugin activation
 * Run this file directly: http://your-site.com/wp-content/plugins/max-user-management/diagnose-output.php
 */

// Include WordPress if running standalone
if (!defined('ABSPATH')) {
    require_once('../../../../wp-config.php');
}

echo "<h2>MAX User Management - Output Diagnostic</h2>\n";
echo "<style>
    .test-pass { color: green; }
    .test-fail { color: red; }
    .test-warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto; }
    .output-capture { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
</style>\n";

// Function to capture output
function capture_output_from_file($file_path, $description) {
    echo "<h3>Testing: {$description}</h3>\n";
    
    if (!file_exists($file_path)) {
        echo "<p class='test-fail'>❌ File not found: {$file_path}</p>\n";
        return false;
    }
    
    // Start output buffering
    ob_start();
    
    // Capture any errors
    $error_output = '';
    set_error_handler(function($severity, $message, $file, $line) use (&$error_output) {
        $error_output .= "Error: {$message} in {$file} on line {$line}\n";
    });
    
    try {
        // Include the file
        include $file_path;
        
        // Get any output
        $output = ob_get_contents();
        
        if (!empty($output)) {
            echo "<p class='test-warning'>⚠️ Output detected ({strlen($output)} characters):</p>\n";
            echo "<div class='output-capture'><pre>" . htmlspecialchars($output) . "</pre></div>\n";
        } else {
            echo "<p class='test-pass'>✅ No output detected</p>\n";
        }
        
        if (!empty($error_output)) {
            echo "<p class='test-fail'>❌ Errors detected:</p>\n";
            echo "<div class='output-capture'><pre>" . htmlspecialchars($error_output) . "</pre></div>\n";
        }
        
    } catch (Exception $e) {
        echo "<p class='test-fail'>❌ Exception: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    } catch (Error $e) {
        echo "<p class='test-fail'>❌ Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    } finally {
        // Restore error handler
        restore_error_handler();
        
        // Clean output buffer
        ob_end_clean();
    }
    
    return true;
}

// Test individual files
echo "<h2>🔍 Testing Individual Files</h2>\n";

$files_to_test = array(
    __DIR__ . '/includes/class-max-config.php' => 'MAX Config Class',
    __DIR__ . '/includes/class-max-user-roles.php' => 'MAX User Roles Class',
    __DIR__ . '/includes/class-max-wings-api.php' => 'MAX Wings API Class',
    __DIR__ . '/includes/class-max-hpos-compatibility.php' => 'MAX HPOS Compatibility Class'
);

foreach ($files_to_test as $file => $description) {
    capture_output_from_file($file, $description);
}

// Test main plugin file sections
echo "<h2>🔍 Testing Main Plugin File Sections</h2>\n";

// Test HPOS declaration
echo "<h3>Testing HPOS Declaration</h3>\n";
ob_start();
try {
    // Simulate the HPOS declaration
    if (function_exists('add_action')) {
        add_action('before_woocommerce_init', function() {
            if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
                \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
            }
        });
        echo "<p class='test-pass'>✅ HPOS declaration executed without output</p>\n";
    } else {
        echo "<p class='test-warning'>⚠️ add_action function not available</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='test-fail'>❌ HPOS declaration error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
$hpos_output = ob_get_clean();

if (!empty($hpos_output)) {
    echo "<p class='test-warning'>⚠️ HPOS declaration produced output:</p>\n";
    echo "<div class='output-capture'><pre>" . htmlspecialchars($hpos_output) . "</pre></div>\n";
}

// Test constants definition
echo "<h3>Testing Constants Definition</h3>\n";
ob_start();
try {
    // Test if constants can be defined without output
    if (!defined('TEST_MAX_USER_MGMT_VERSION')) {
        define('TEST_MAX_USER_MGMT_VERSION', '1.0.0');
        define('TEST_MAX_USER_MGMT_PLUGIN_FILE', __FILE__);
        if (function_exists('plugin_dir_path')) {
            define('TEST_MAX_USER_MGMT_PLUGIN_DIR', plugin_dir_path(__FILE__));
        }
        if (function_exists('plugin_dir_url')) {
            define('TEST_MAX_USER_MGMT_PLUGIN_URL', plugin_dir_url(__FILE__));
        }
        if (function_exists('plugin_basename')) {
            define('TEST_MAX_USER_MGMT_PLUGIN_BASENAME', plugin_basename(__FILE__));
        }
    }
    echo "<p class='test-pass'>✅ Constants defined without output</p>\n";
} catch (Exception $e) {
    echo "<p class='test-fail'>❌ Constants definition error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
$constants_output = ob_get_clean();

if (!empty($constants_output)) {
    echo "<p class='test-warning'>⚠️ Constants definition produced output:</p>\n";
    echo "<div class='output-capture'><pre>" . htmlspecialchars($constants_output) . "</pre></div>\n";
}

// Check for BOM or hidden characters
echo "<h2>🔍 Checking for BOM and Hidden Characters</h2>\n";

$main_file = __DIR__ . '/max-user-management.php';
if (file_exists($main_file)) {
    $content = file_get_contents($main_file);
    
    // Check for BOM
    if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
        echo "<p class='test-fail'>❌ BOM (Byte Order Mark) detected at start of main file</p>\n";
    } else {
        echo "<p class='test-pass'>✅ No BOM detected</p>\n";
    }
    
    // Check for whitespace before <?php
    if (preg_match('/^\s+<\?php/', $content)) {
        echo "<p class='test-fail'>❌ Whitespace detected before opening PHP tag</p>\n";
    } else {
        echo "<p class='test-pass'>✅ No whitespace before opening PHP tag</p>\n";
    }
    
    // Check for content after closing ?>
    if (preg_match('/\?>\s*\S/', $content)) {
        echo "<p class='test-fail'>❌ Content detected after closing PHP tag</p>\n";
    } else {
        echo "<p class='test-pass'>✅ No content after closing PHP tag</p>\n";
    }
    
    // Check file size
    $file_size = strlen($content);
    echo "<p>File size: {$file_size} bytes</p>\n";
    
    // Show first and last 100 characters
    echo "<h4>First 100 characters:</h4>\n";
    echo "<pre>" . htmlspecialchars(substr($content, 0, 100)) . "</pre>\n";
    
    echo "<h4>Last 100 characters:</h4>\n";
    echo "<pre>" . htmlspecialchars(substr($content, -100)) . "</pre>\n";
    
} else {
    echo "<p class='test-fail'>❌ Main plugin file not found</p>\n";
}

// Test minimal plugin activation
echo "<h2>🔍 Testing Minimal Plugin Simulation</h2>\n";

ob_start();
try {
    // Simulate minimal plugin activation
    if (function_exists('add_action') && function_exists('register_activation_hook')) {
        
        // Test class definition
        if (!class_exists('Test_MAX_User_Management')) {
            class Test_MAX_User_Management {
                public static function get_instance() {
                    return new self();
                }
                
                public function __construct() {
                    // Minimal constructor
                }
                
                public function activate() {
                    // Minimal activation
                    return true;
                }
            }
        }
        
        $test_instance = Test_MAX_User_Management::get_instance();
        $test_instance->activate();
        
        echo "<p class='test-pass'>✅ Minimal plugin simulation successful</p>\n";
        
    } else {
        echo "<p class='test-warning'>⚠️ WordPress functions not available for testing</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='test-fail'>❌ Minimal plugin simulation error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

$simulation_output = ob_get_clean();

if (!empty($simulation_output)) {
    echo "<p class='test-warning'>⚠️ Minimal plugin simulation produced output:</p>\n";
    echo "<div class='output-capture'><pre>" . htmlspecialchars($simulation_output) . "</pre></div>\n";
}

echo "<h2>📋 Summary and Recommendations</h2>\n";
echo "<ol>\n";
echo "<li>Check each file above for any output or errors</li>\n";
echo "<li>Look for BOM or hidden characters in files</li>\n";
echo "<li>Ensure no echo/print statements in class files</li>\n";
echo "<li>Verify HPOS declaration doesn't cause output</li>\n";
echo "<li>Try the minimal plugin version for testing</li>\n";
echo "</ol>\n";

echo "<h3>🚀 Next Steps</h3>\n";
echo "<p>1. If no output is detected above, the issue might be in WordPress core interaction</p>\n";
echo "<p>2. Try activating the minimal plugin version: <code>max-user-management-minimal.php</code></p>\n";
echo "<p>3. Check WordPress debug.log for additional error information</p>\n";
echo "<p>4. Temporarily disable other plugins to check for conflicts</p>\n";

echo "<p><strong>Diagnostic completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
