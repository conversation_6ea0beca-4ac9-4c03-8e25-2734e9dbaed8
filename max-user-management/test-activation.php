<?php
/**
 * Test MAX User Management Plugin Activation
 * 
 * This file tests if the plugin can be loaded without fatal errors
 * Run this file directly to test: http://your-site.com/wp-content/plugins/max-user-management/test-activation.php
 */

// Include WordPress if running standalone
if (!defined('ABSPATH')) {
    require_once('../../../../wp-config.php');
}

echo "<h2>MAX User Management - Activation Test</h2>\n";
echo "<style>
    .test-pass { color: green; }
    .test-fail { color: red; }
    .test-warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>\n";

// Test 1: Check if main plugin file exists
echo "<h3>1. Testing Main Plugin File</h3>\n";
$main_file = __DIR__ . '/max-user-management.php';
if (file_exists($main_file)) {
    echo "<p class='test-pass'>✅ Main plugin file exists</p>\n";
} else {
    echo "<p class='test-fail'>❌ Main plugin file missing: {$main_file}</p>\n";
    exit;
}

// Test 2: Check syntax of main file
echo "<h3>2. Testing Main File Syntax</h3>\n";
$syntax_check = shell_exec("php -l " . escapeshellarg($main_file) . " 2>&1");
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "<p class='test-pass'>✅ Main file syntax is valid</p>\n";
} else {
    echo "<p class='test-fail'>❌ Syntax error in main file:</p>\n";
    echo "<pre>{$syntax_check}</pre>\n";
}

// Test 3: Check required class files
echo "<h3>3. Testing Required Class Files</h3>\n";
$required_files = array(
    'includes/class-max-config.php',
    'includes/class-max-wings-api.php', 
    'includes/class-max-user-roles.php'
);

foreach ($required_files as $file) {
    $file_path = __DIR__ . '/' . $file;
    if (file_exists($file_path)) {
        echo "<p class='test-pass'>✅ {$file} exists</p>\n";
        
        // Check syntax
        $syntax_check = shell_exec("php -l " . escapeshellarg($file_path) . " 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "<p class='test-pass'>✅ {$file} syntax is valid</p>\n";
        } else {
            echo "<p class='test-fail'>❌ Syntax error in {$file}:</p>\n";
            echo "<pre>{$syntax_check}</pre>\n";
        }
    } else {
        echo "<p class='test-fail'>❌ {$file} missing</p>\n";
    }
}

// Test 4: Try to include files without fatal errors
echo "<h3>4. Testing File Inclusion</h3>\n";

try {
    // Capture any output/errors
    ob_start();
    error_reporting(E_ALL);
    
    // Define constants that the plugin expects
    if (!defined('MAX_USER_MGMT_VERSION')) {
        define('MAX_USER_MGMT_VERSION', '1.0.0');
    }
    if (!defined('MAX_USER_MGMT_PLUGIN_FILE')) {
        define('MAX_USER_MGMT_PLUGIN_FILE', $main_file);
    }
    if (!defined('MAX_USER_MGMT_PLUGIN_DIR')) {
        define('MAX_USER_MGMT_PLUGIN_DIR', __DIR__ . '/');
    }
    if (!defined('MAX_USER_MGMT_PLUGIN_URL')) {
        define('MAX_USER_MGMT_PLUGIN_URL', plugin_dir_url($main_file));
    }
    if (!defined('MAX_USER_MGMT_PLUGIN_BASENAME')) {
        define('MAX_USER_MGMT_PLUGIN_BASENAME', plugin_basename($main_file));
    }
    
    // Include config first
    if (file_exists(__DIR__ . '/includes/class-max-config.php')) {
        include_once __DIR__ . '/includes/class-max-config.php';
        echo "<p class='test-pass'>✅ MAX_Config class loaded</p>\n";
    }
    
    // Include Wings API
    if (file_exists(__DIR__ . '/includes/class-max-wings-api.php')) {
        include_once __DIR__ . '/includes/class-max-wings-api.php';
        echo "<p class='test-pass'>✅ MAX_Wings_API class loaded</p>\n";
    }
    
    // Include User Roles
    if (file_exists(__DIR__ . '/includes/class-max-user-roles.php')) {
        include_once __DIR__ . '/includes/class-max-user-roles.php';
        echo "<p class='test-pass'>✅ MAX_User_Roles class loaded</p>\n";
    }
    
    $output = ob_get_clean();
    if (!empty($output)) {
        echo "<p class='test-warning'>⚠️ Output during inclusion:</p>\n";
        echo "<pre>{$output}</pre>\n";
    }
    
} catch (ParseError $e) {
    ob_end_clean();
    echo "<p class='test-fail'>❌ Parse Error: " . $e->getMessage() . "</p>\n";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>\n";
} catch (Error $e) {
    ob_end_clean();
    echo "<p class='test-fail'>❌ Fatal Error: " . $e->getMessage() . "</p>\n";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>\n";
} catch (Exception $e) {
    ob_end_clean();
    echo "<p class='test-fail'>❌ Exception: " . $e->getMessage() . "</p>\n";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>\n";
}

// Test 5: Check if classes are defined
echo "<h3>5. Testing Class Definitions</h3>\n";

$classes_to_check = array(
    'MAX_Config',
    'MAX_Wings_API', 
    'MAX_User_Roles'
);

foreach ($classes_to_check as $class_name) {
    if (class_exists($class_name)) {
        echo "<p class='test-pass'>✅ {$class_name} class is defined</p>\n";
        
        // Check if it has required methods
        $reflection = new ReflectionClass($class_name);
        $methods = $reflection->getMethods();
        echo "<p>Methods: " . implode(', ', array_map(function($m) { return $m->getName(); }, $methods)) . "</p>\n";
        
    } else {
        echo "<p class='test-fail'>❌ {$class_name} class not found</p>\n";
    }
}

// Test 6: Test constants
echo "<h3>6. Testing Constants</h3>\n";

if (class_exists('MAX_User_Roles')) {
    $constants = array(
        'ROLE_NAME',
        'ROLE_DISPLAY_NAME', 
        'STATUS_ACTIVE',
        'CAP_VIEW_PROFESSIONAL_PRICES'
    );
    
    foreach ($constants as $constant) {
        if (defined("MAX_User_Roles::{$constant}")) {
            $value = constant("MAX_User_Roles::{$constant}");
            echo "<p class='test-pass'>✅ {$constant} = '{$value}'</p>\n";
        } else {
            echo "<p class='test-fail'>❌ {$constant} not defined</p>\n";
        }
    }
}

// Test 7: Try to instantiate classes
echo "<h3>7. Testing Class Instantiation</h3>\n";

try {
    if (class_exists('MAX_Config')) {
        $settings = MAX_Config::get_all_settings();
        echo "<p class='test-pass'>✅ MAX_Config::get_all_settings() works</p>\n";
    }
    
    if (class_exists('MAX_User_Roles')) {
        $status_options = MAX_User_Roles::get_status_options();
        echo "<p class='test-pass'>✅ MAX_User_Roles::get_status_options() works</p>\n";
        echo "<p>Status options: " . implode(', ', array_keys($status_options)) . "</p>\n";
    }
    
} catch (Error $e) {
    echo "<p class='test-fail'>❌ Error during instantiation: " . $e->getMessage() . "</p>\n";
} catch (Exception $e) {
    echo "<p class='test-fail'>❌ Exception during instantiation: " . $e->getMessage() . "</p>\n";
}

echo "<h3>🎯 Test Summary</h3>\n";
echo "<p>If you see mostly green checkmarks above, the plugin should activate without fatal errors.</p>\n";
echo "<p>If you see red X marks, those issues need to be fixed before activation.</p>\n";

echo "<h3>📋 Next Steps</h3>\n";
echo "<ol>\n";
echo "<li>Fix any red X issues shown above</li>\n";
echo "<li>Try activating the plugin in WordPress admin</li>\n";
echo "<li>If activation fails, check WordPress error logs</li>\n";
echo "<li>Run the unit tests: <code>wp eval-file wp-content/plugins/max-user-management/tests/test-max-user-roles.php</code></li>\n";
echo "</ol>\n";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
