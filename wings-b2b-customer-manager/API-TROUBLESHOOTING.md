# Wings B2B Customer Manager - API Troubleshooting Guide

## 🚨 **Problem: "Neispravna struktura odgovora API-ja"**

### **Uzrok:**
Wings Portal API može vratiti podatke u različitim formatima, a plugin očekuje specifičnu strukturu.

## 🔍 **Debug Process**

### **Korak 1: Testiraj API konekciju**
1. Idite na **Wings B2B → Wings API**
2. Kliknite **"Testiraj konekciju"**
3. Proverite da li je autentifikacija uspešna

### **Korak 2: Debug API odgovor**
1. Na istoj stranici kliknite **"Debug API odgovor"**
2. Analizirajte strukturu odgovora:
   - Response Type
   - Structure Type  
   - Data Count
   - Sample Data

### **Korak 3: Analiziraj strukturu**
API može vratiti podatke u 3 formata:

#### **Format 1: JSON API**
```json
{
  "data": [
    {
      "type": "local-kupac-svi",
      "id": "12990",
      "attributes": {
        "sifra": "12990",
        "naziv": "ADAM APOTEKA",
        "adresa": "<PERSON><PERSON> Velikog 110",
        ...
      }
    }
  ]
}
```

#### **Format 2: DataTables**
```json
{
  "draw": 1,
  "recordsTotal": 1838,
  "recordsFiltered": 1838,
  "aaData": [
    ["12990", "ADAM APOTEKA", "Miloša Velikog 110", "11320 Velika Plana", ...]
  ]
}
```

#### **Format 3: Direct Array**
```json
[
  ["12990", "ADAM APOTEKA", "Miloša Velikog 110", "11320 Velika Plana", ...],
  ["16265", "DRUGA APOTEKA", "Neka adresa 123", "12000 Požarevac", ...]
]
```

## 🛠️ **Rešavanje problema**

### **Automatska detekcija**
Plugin automatski pokušava da detektuje format:

```php
// Pokušava različite strukture
if (isset($response['data'])) {
    // JSON API format
} elseif (isset($response['aaData'])) {
    // DataTables format  
} elseif (is_array($response)) {
    // Direct array format
}
```

### **Field mapping**
Za array format, plugin mapira pozicije u polja:

```php
$field_mappings = array(
    0 => 'sifra',
    1 => 'naziv', 
    2 => 'adresa',
    3 => 'mesto',
    4 => 'kontakt',
    5 => 'telefon',
    6 => 'fax',
    7 => 'mobilni',
    8 => 'email',
    9 => 'radnovreme',
    10 => 'status',
    11 => 'pib',
    12 => 'rabat',
    13 => 'komercijalista',
    14 => 'mb',
    15 => 'limit',
    16 => 'racun',
    17 => 'rokplacanja',
    18 => 'tolerancija',
    19 => 'klasa'
);
```

## 🔧 **Manual Testing**

### **Test API direktno**
```bash
# Test autentifikacije
curl -X POST "https://portal.wings.rs/api/v1/grossaql/system.user.log" \
  -d "aUn=aql&aUp=grossaql"

# Test dohvatanja kupaca
curl "https://portal.wings.rs/api/v1/grossaql/local.kupac.svi?dLength=5&dStart=0&output=jsonapi"
```

### **Debug fajl**
Pokrenite debug fajl:
```
your-site.com/wp-content/plugins/wings-b2b-customer-manager/debug-api-response.php
```

## 📊 **Česti problemi i rešenja**

### **Problem 1: Authentication Failed**
**Uzrok:** Pogrešni credentials
**Rešenje:** 
- Proverite username/password u Settings
- Za test koristite: aql/grossaql/grossaql

### **Problem 2: Empty Response**
**Uzrok:** API ne vraća podatke
**Rešenje:**
- Proverite da li postoje kupci u Wings sistemu
- Smanjite limit (probajte sa 5-10)
- Proverite API endpoint URL

### **Problem 3: Wrong Data Structure**
**Uzrok:** API vraća neočekivanu strukturu
**Rešenje:**
- Koristite "Debug API odgovor" dugme
- Analizirajte sample data
- Prilagodite field mapping ako je potrebno

### **Problem 4: Field Mapping Issues**
**Uzrok:** Polja nisu na očekivanim pozicijama
**Rešenje:**
- Proverite debug output
- Ažurirajte field mappings u kodu
- Testirajte sa manjim batch-om

## 🔄 **Korak po korak rešavanje**

### **1. Proverite API credentials**
```
Wings B2B → Podešavanja → API Podešavanja
Username: aql
Password: grossaql  
Alias: grossaql
```

### **2. Testirajte konekciju**
```
Wings B2B → Wings API → "Testiraj konekciju"
```

### **3. Debug API odgovor**
```
Wings B2B → Wings API → "Debug API odgovor"
```

### **4. Analizirajte rezultate**
- Proverite Structure Type
- Pogledajte Sample Data
- Identifikujte format podataka

### **5. Prilagodite kod (ako je potrebno)**
Ako API vraća drugačiju strukturu, možda treba da ažurirate:
- Field mappings u `map_array_to_attributes()`
- Response parsing u `ajax_import_customers_from_api()`

## 📝 **Debug informacije**

### **Šta da tražite u debug output-u:**

#### **Uspešan odgovor:**
```
Structure Type: JSON API (data key)
Data Count: 5
Sample Data: [valid customer objects]
```

#### **DataTables format:**
```
Structure Type: DataTables (aaData key)  
Data Count: 5
Sample Data: [arrays with customer data]
```

#### **Problem:**
```
Structure Type: Unknown
Data Count: 0
Sample Data: null or empty
```

## 🚀 **Sledeći koraci**

### **Ako debug pokazuje validne podatke:**
- API radi, problem je u parsing-u
- Proverite field mapping
- Testirajte sa manjim batch-om

### **Ako debug pokazuje prazne podatke:**
- Problem sa API pozivom
- Proverite credentials
- Proverite API endpoint
- Kontaktirajte Wings support

### **Ako debug pokazuje neočekivanu strukturu:**
- API vraća drugačiji format
- Treba prilagoditi kod
- Dokumentujte novu strukturu

## 📞 **Support**

### **Debug fajlovi:**
- `debug-api-response.php` - Detaljni API debug
- WordPress error log - PHP greške
- Browser console - JavaScript greške

### **Informacije za support:**
- Debug API output
- Sample API response
- Error messages
- WordPress/WooCommerce verzije

**Cilj je da identifikujemo tačnu strukturu API odgovora i prilagodimo kod da je pravilno parsira.** 🎯
