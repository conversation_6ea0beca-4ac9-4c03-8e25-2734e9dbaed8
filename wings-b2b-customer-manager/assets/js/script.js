/**
 * Wings B2B Customer Manager - Frontend JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize components
    initOrderHistory();
    initDashboard();
    initCustomerProfile();
    
    /**
     * Initialize Order History functionality
     */
    function initOrderHistory() {
        // Toggle order details on row click
        $('.wings-orders-table tbody tr:not(.order-details)').on('click', function(e) {
            // Don't toggle if clicking on a button or link
            if ($(e.target).is('a, button, .button')) {
                return;
            }
            
            var $detailsRow = $(this).next('.order-details');
            
            // Close other open details
            $('.order-details').not($detailsRow).slideUp();
            
            // Toggle current details
            $detailsRow.slideToggle();
            
            // Add visual feedback
            $(this).toggleClass('expanded');
        });
        
        // Export functionality
        $('#export-orders').on('click', function() {
            exportOrders('csv');
        });
        
        $('#export-orders-pdf').on('click', function() {
            exportOrders('pdf');
        });
        
        // Filter form enhancement
        $('.filter-form').on('submit', function() {
            showLoadingSpinner();
        });
        
        // Auto-submit filter form on select change
        $('.filter-form select').on('change', function() {
            $(this).closest('form').submit();
        });
    }
    
    /**
     * Initialize Dashboard functionality
     */
    function initDashboard() {
        // Animate dashboard cards on load
        $('.dashboard-card').each(function(index) {
            $(this).delay(index * 100).animate({
                opacity: 1,
                transform: 'translateY(0)'
            }, 500);
        });
        
        // Quick actions hover effects
        $('.quick-actions .button').on('mouseenter', function() {
            $(this).addClass('hover-effect');
        }).on('mouseleave', function() {
            $(this).removeClass('hover-effect');
        });
        
        // Placeholder email notice interaction
        $('.notice .button').on('click', function() {
            $(this).closest('.notice').fadeOut();
        });
    }
    
    /**
     * Initialize Customer Profile functionality
     */
    function initCustomerProfile() {
        // Profile form validation
        $('.wings-profile-form').on('submit', function(e) {
            var isValid = validateProfileForm();
            
            if (!isValid) {
                e.preventDefault();
                return false;
            }
            
            showLoadingSpinner();
        });
        
        // Email field validation
        $('#user_email').on('blur', function() {
            validateEmail($(this).val());
        });
        
        // Phone number formatting
        $('input[type="tel"]').on('input', function() {
            formatPhoneNumber($(this));
        });
    }
    
    /**
     * Export orders functionality
     */
    function exportOrders(format) {
        var params = new URLSearchParams(window.location.search);
        params.set('action', 'wings_export_orders');
        params.set('format', format);
        
        if (typeof wings_b2b_ajax !== 'undefined') {
            params.set('nonce', wings_b2b_ajax.nonce);
        }
        
        var exportUrl = (typeof wings_b2b_ajax !== 'undefined') ? 
            wings_b2b_ajax.ajax_url : '/wp-admin/admin-ajax.php';
        
        // Show loading indicator
        showNotification('Priprema izvoza...', 'info');
        
        // Create temporary link and trigger download
        var link = document.createElement('a');
        link.href = exportUrl + '?' + params.toString();
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Hide loading indicator after delay
        setTimeout(function() {
            hideNotification();
        }, 2000);
    }
    
    /**
     * Validate profile form
     */
    function validateProfileForm() {
        var isValid = true;
        var email = $('#user_email').val();
        
        // Clear previous errors
        $('.form-error').remove();
        
        // Validate email
        if (!isValidEmail(email)) {
            showFieldError('#user_email', 'Molimo unesite ispravnu email adresu.');
            isValid = false;
        }
        
        return isValid;
    }
    
    /**
     * Validate email address
     */
    function validateEmail(email) {
        if (!isValidEmail(email)) {
            showFieldError('#user_email', 'Neispravna email adresa.');
            return false;
        } else {
            hideFieldError('#user_email');
            return true;
        }
    }
    
    /**
     * Check if email is valid
     */
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * Format phone number
     */
    function formatPhoneNumber($input) {
        var value = $input.val().replace(/\D/g, '');
        var formattedValue = '';
        
        if (value.length > 0) {
            if (value.length <= 3) {
                formattedValue = value;
            } else if (value.length <= 6) {
                formattedValue = value.slice(0, 3) + '-' + value.slice(3);
            } else {
                formattedValue = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
            }
        }
        
        $input.val(formattedValue);
    }
    
    /**
     * Show field error
     */
    function showFieldError(fieldSelector, message) {
        var $field = $(fieldSelector);
        var $error = $('<div class="form-error" style="color: #dc3232; font-size: 0.85em; margin-top: 5px;">' + message + '</div>');
        
        $field.addClass('error');
        $field.after($error);
    }
    
    /**
     * Hide field error
     */
    function hideFieldError(fieldSelector) {
        var $field = $(fieldSelector);
        $field.removeClass('error');
        $field.next('.form-error').remove();
    }
    
    /**
     * Show loading spinner
     */
    function showLoadingSpinner() {
        if ($('#wings-loading-spinner').length === 0) {
            var spinner = '<div id="wings-loading-spinner" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background: rgba(255,255,255,0.9); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">' +
                '<div style="text-align: center;">' +
                '<div class="spinner" style="border: 3px solid #f3f3f3; border-top: 3px solid #0073aa; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>' +
                '<p style="margin: 0; color: #333;">Učitava...</p>' +
                '</div>' +
                '</div>';
            
            $('body').append(spinner);
            
            // Add CSS animation
            if ($('#wings-spinner-css').length === 0) {
                $('head').append('<style id="wings-spinner-css">@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>');
            }
        }
    }
    
    /**
     * Hide loading spinner
     */
    function hideLoadingSpinner() {
        $('#wings-loading-spinner').remove();
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type) {
        type = type || 'info';
        
        var notificationClass = 'wings-notification-' + type;
        var backgroundColor = '#0073aa';
        
        switch(type) {
            case 'success':
                backgroundColor = '#46b450';
                break;
            case 'error':
                backgroundColor = '#dc3232';
                break;
            case 'warning':
                backgroundColor = '#ffba00';
                break;
        }
        
        var notification = '<div id="wings-notification" class="' + notificationClass + '" style="position: fixed; top: 20px; right: 20px; z-index: 9999; background: ' + backgroundColor + '; color: #fff; padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); max-width: 300px;">' +
            '<p style="margin: 0;">' + message + '</p>' +
            '</div>';
        
        // Remove existing notification
        $('#wings-notification').remove();
        
        // Add new notification
        $('body').append(notification);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            hideNotification();
        }, 5000);
    }
    
    /**
     * Hide notification
     */
    function hideNotification() {
        $('#wings-notification').fadeOut(function() {
            $(this).remove();
        });
    }
    
    /**
     * AJAX helper function
     */
    function ajaxRequest(action, data, callback) {
        if (typeof wings_b2b_ajax === 'undefined') {
            console.error('Wings B2B AJAX object not found');
            return;
        }
        
        var requestData = {
            action: action,
            nonce: wings_b2b_ajax.nonce
        };
        
        $.extend(requestData, data);
        
        $.ajax({
            url: wings_b2b_ajax.ajax_url,
            type: 'POST',
            data: requestData,
            success: function(response) {
                if (typeof callback === 'function') {
                    callback(response);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                showNotification('Greška pri komunikaciji sa serverom.', 'error');
            }
        });
    }
    
    /**
     * Utility function to get URL parameter
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }
    
    /**
     * Utility function to format currency
     */
    function formatCurrency(amount) {
        return new Intl.NumberFormat('sr-RS', {
            style: 'currency',
            currency: 'RSD'
        }).format(amount);
    }
    
    /**
     * Utility function to format date
     */
    function formatDate(dateString) {
        var date = new Date(dateString);
        return date.toLocaleDateString('sr-RS', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    // Global error handler
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
    });
    
    // Handle page visibility change
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Page is hidden
            hideLoadingSpinner();
        }
    });
});
