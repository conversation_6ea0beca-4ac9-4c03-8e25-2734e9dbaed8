/**
 * Wings B2B Customer Manager - Frontend Styles
 */

/* General Styles */
.wings-b2b-dashboard,
.wings-order-history,
.wings-customer-profile {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Dashboard Styles */
.wings-b2b-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.dashboard-welcome {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dashboard-welcome h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.8em;
    font-weight: 300;
}

.notice {
    padding: 15px;
    margin: 20px 0;
    border-left: 4px solid #ffba00;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.notice.notice-warning {
    border-left-color: #ffba00;
    background: #fffbf0;
}

.notice.notice-success {
    border-left-color: #46b450;
    background: #f0fff4;
}

.notice.notice-error {
    border-left-color: #dc3232;
    background: #fff0f0;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.dashboard-card h4 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.2em;
    font-weight: 600;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

/* Quick Actions */
.quick-actions {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quick-actions li {
    margin-bottom: 12px;
}

.quick-actions .button {
    display: block;
    width: 100%;
    text-align: center;
    padding: 12px 20px;
    text-decoration: none;
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    color: #fff;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-actions .button:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,115,170,0.3);
}

/* Customer Info */
.customer-info ul,
.b2b-info {
    list-style: none;
    padding: 0;
    margin: 0;
}

.customer-info li,
.b2b-info li {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.customer-info li:last-child,
.b2b-info li:last-child {
    border-bottom: none;
}

.customer-info strong,
.b2b-info strong {
    color: #555;
    font-weight: 600;
    min-width: 120px;
}

/* Recent Orders */
.recent-orders .orders-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-orders .order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.recent-orders .order-item:hover {
    background: #f8f9fa;
    margin: 0 -15px;
    padding: 15px;
    border-radius: 8px;
}

.recent-orders .order-item:last-child {
    border-bottom: none;
}

.recent-orders .order-info strong {
    color: #0073aa;
    font-weight: 600;
}

.recent-orders .order-date {
    font-size: 0.9em;
    color: #666;
    display: block;
    margin-top: 4px;
}

.recent-orders .order-status span {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Order Status Colors */
.status-completed {
    background: #46b450;
    color: #fff;
}

.status-processing {
    background: #ffba00;
    color: #fff;
}

.status-pending {
    background: #999;
    color: #fff;
}

.status-cancelled {
    background: #dc3232;
    color: #fff;
}

.status-on-hold {
    background: #f56e28;
    color: #fff;
}

.status-refunded {
    background: #999;
    color: #fff;
}

.recent-orders .order-total {
    font-weight: 600;
    color: #333;
    font-size: 1.1em;
}

.view-all {
    margin-top: 20px;
    text-align: center;
}

/* Badges */
.placeholder-email-badge {
    background: #ffba00;
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 600;
    margin-left: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Order History Styles */
.order-filters {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
}

.filter-form .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    flex: 1;
    min-width: 180px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9em;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0,115,170,0.1);
}

/* Order Actions */
.order-actions {
    margin-bottom: 25px;
    text-align: right;
}

.order-actions .button {
    margin-left: 12px;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
}

/* Orders Table */
.orders-table-container {
    overflow-x: auto;
    margin-bottom: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.wings-orders-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
}

.wings-orders-table th,
.wings-orders-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.wings-orders-table th {
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    color: #fff;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85em;
    letter-spacing: 0.5px;
}

.wings-orders-table tbody tr {
    cursor: pointer;
    transition: all 0.3s ease;
}

.wings-orders-table tbody tr:hover {
    background: #f8f9fa;
}

.wings-orders-table tbody tr:not(.order-details):hover {
    background: #e3f2fd;
    transform: scale(1.01);
}

/* Order Details */
.order-details {
    background: #f8f9fa !important;
}

.order-details td {
    padding: 25px;
}

.order-items h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
}

.order-items ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.order-items li {
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
}

.order-items li:last-child {
    border-bottom: none;
}

.order-note {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-left: 4px solid #0073aa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Summary and No Orders */
.order-summary {
    text-align: center;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    color: #666;
    border: 1px solid #e9ecef;
}

.no-orders {
    text-align: center;
    padding: 50px 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.no-orders p {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 25px;
}

/* Button Styles */
.button {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.button:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,115,170,0.3);
}

.button-primary {
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
}

.button-small {
    padding: 8px 15px;
    font-size: 12px;
}

/* Customer Profile */
.wings-customer-profile {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.wings-profile-form {
    background: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0,115,170,0.1);
}

.form-text {
    font-size: 0.85em;
    margin-top: 5px;
}

.text-warning {
    color: #856404;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-form .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .order-actions {
        text-align: center;
    }
    
    .order-actions .button {
        display: block;
        margin: 8px 0;
        width: 100%;
    }
    
    .recent-orders .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .customer-info li,
    .b2b-info li {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .customer-info strong,
    .b2b-info strong {
        min-width: auto;
    }
    
    .wings-orders-table {
        font-size: 0.9em;
    }
    
    .wings-orders-table th,
    .wings-orders-table td {
        padding: 10px;
    }
    
    /* Hide some columns on mobile */
    .wings-orders-table th:nth-child(5),
    .wings-orders-table td:nth-child(5) {
        display: none;
    }
}

@media (max-width: 480px) {
    .wings-orders-table th:nth-child(4),
    .wings-orders-table td:nth-child(4) {
        display: none;
    }
    
    .wings-orders-table {
        font-size: 0.8em;
    }
    
    .dashboard-welcome {
        padding: 20px;
    }
    
    .dashboard-card {
        padding: 20px;
    }
    
    .wings-profile-form {
        padding: 20px;
    }
}
