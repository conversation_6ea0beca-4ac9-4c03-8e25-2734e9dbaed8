# Wings B2B Customer Manager - Wings Portal API Integration

## 🚀 **Nove funkcionalnosti dodane:**

### 1. **Progress Bar Fix**
- ✅ Popravljen progress bar koji se nije prikazivao
- ✅ Dodani inline stilovi za progress bar
- ✅ Real-time ažuriranje napretka tokom batch import-a

### 2. **Wings Portal API Integration**
- ✅ Direktan import kupaca iz Wings Portal API-ja
- ✅ Kreiranje novih kupaca u Wings Portal-u
- ✅ Test konekcije sa API-jem
- ✅ Kompletna API konfiguracija

### 3. **Nova Admin Stranica - Kreiraj Kupca**
- ✅ Forma za kreiranje novih kupaca
- ✅ Lokalno kreiranje (WordPress/WooCommerce)
- ✅ Kreiranje u Wings Portal-u preko API-ja
- ✅ Validacija i error handling

### 4. **Nova Admin Stranica - Wings API**
- ✅ Test API konekcije
- ✅ Direktan import iz API-ja
- ✅ Konfigurisanje batch veličine
- ✅ Real-time rezultati

## 🔧 **Wings Portal API Endpoints**

### **Autentifikacija:**
```
POST https://portal.wings.rs/api/v1/{alias}/system.user.log
Body: aUn=username&aUp=password
```

### **Dohvatanje kupaca:**
```
GET https://portal.wings.rs/api/v1/{alias}/local.kupac.svi?dLength=1000&dStart=0&output=jsonapi
```

### **Kreiranje kupca:**
```
POST https://portal.wings.rs/api/v1/{alias}/local.kupac.nov
Body: naziv=...&adresa=...&mesto=...&kontakt=...
```

## 📁 **Nova struktura admin stranica:**

```
Wings B2B
├── Dashboard (glavna stranica)
├── Import kupaca (JSON upload + batch processing)
├── B2B kupci (lista postojećih kupaca)
├── Kreiraj kupca (nova stranica)
│   ├── Lokalno kreiranje
│   └── Kreiranje u Wings Portal-u
├── Wings API (nova stranica)
│   ├── Test konekcije
│   └── Import iz API-ja
└── Podešavanja
    ├── Email podešavanja
    └── API podešavanja (novo)
```

## 🎯 **Kako koristiti nove funkcionalnosti:**

### **1. Konfiguracija API-ja:**
1. Idite na **Wings B2B → Podešavanja**
2. Unesite API credentials:
   - **Username**: aql (za test)
   - **Password**: grossaql (za test)
   - **Alias**: grossaql (za test)
3. Sačuvajte podešavanja

### **2. Test API konekcije:**
1. Idite na **Wings B2B → Wings API**
2. Kliknite **"Testiraj konekciju"**
3. Proverite da li je konekcija uspešna

### **3. Import kupaca iz API-ja:**
1. Na **Wings API** stranici
2. Podesite broj kupaca (max 5000)
3. Podesite početni offset (0 = početak)
4. Kliknite **"Importuj iz API"**
5. Pratite rezultate

### **4. Kreiranje novog kupca:**
1. Idite na **Wings B2B → Kreiraj kupca**
2. Popunite formu sa podacima kupca
3. Izaberite opciju:
   - **"Kreiraj kupca lokalno"** - samo u WordPress/WooCommerce
   - **"Kreiraj u Wings Portal-u"** - direktno u Wings sistem
4. Pratite rezultate

## 🔄 **API Workflow:**

### **Import iz API-ja:**
```
1. Autentifikacija sa Wings Portal
2. Dohvatanje kupaca (batch po batch)
3. Konverzija u interni format
4. Import u WordPress/WooCommerce
5. Kreiranje B2B naloga
6. Generisanje placeholder email-ova
```

### **Kreiranje kupca u Wings:**
```
1. Validacija forme
2. Autentifikacija sa Wings Portal
3. Slanje podataka na API
4. Potvrda kreiranja
5. Opciono lokalno kreiranje
```

## 📊 **API Response Format:**

### **Uspešan odgovor (kupci):**
```json
{
  "data": [
    {
      "type": "local-kupac-svi",
      "id": "12990",
      "attributes": {
        "sifra": "12990",
        "naziv": "'ADAM' APOTEKA",
        "adresa": "Miloša Velikog 110",
        "mesto": "11320 Velika Plana",
        "kontakt": "Ilinka Ljubomirović",
        "telefon": "026-514-778",
        "email": "",
        "pib": "101176404",
        "rabat": 0.0,
        "komercijalista": "9"
      }
    }
  ]
}
```

### **Greška:**
```json
{
  "error": "Authentication failed",
  "message": "Invalid credentials"
}
```

## 🛠️ **Tehnička implementacija:**

### **Nova klasa: `Wings_B2B_API`**
```php
class Wings_B2B_API {
    // Autentifikacija
    public function authenticate($username, $password);
    
    // API pozivi
    public function make_request($endpoint, $params, $method);
    
    // Kupci
    public function get_customers($limit, $start);
    public function create_customer($customer_data);
    
    // Test
    public function test_connection();
    
    // AJAX handlers
    public function ajax_import_customers_from_api();
    public function ajax_create_customer_in_wings();
    public function ajax_test_api_connection();
}
```

### **Session Management:**
- Automatska autentifikacija pre svakog API poziva
- Čuvanje session cookie-ja za poslednje pozive
- Retry logika za neuspešne pozive

### **Error Handling:**
- Detaljno logovanje API grešaka
- User-friendly poruke za admin
- Fallback opcije za neuspešne pozive

## 🔐 **Bezbednost:**

### **API Credentials:**
- Čuvaju se u WordPress options tabeli
- Enkriptovani u bazi podataka
- Dostupni samo admin korisnicima

### **Nonce Protection:**
- Svi AJAX pozivi zaštićeni nonce-om
- Validacija korisničkih dozvola
- CSRF zaštita

### **Data Validation:**
- Sanitizacija svih input-a
- Validacija API odgovora
- SQL injection zaštita

## 📈 **Performance:**

### **Optimizacije:**
- Batch processing za velike import-e
- Session reuse za API pozive
- Minimal memory footprint
- Timeout handling

### **Monitoring:**
- API response time logging
- Success/failure rate tracking
- Error frequency monitoring
- Performance metrics

## 🎯 **Prednosti novih funkcionalnosti:**

### **1. Direktan API pristup:**
- ✅ Nema potrebe za JSON fajlove
- ✅ Real-time sinhronizacija
- ✅ Automatska autentifikacija
- ✅ Batch processing

### **2. Kreiranje kupaca:**
- ✅ Direktno iz admin panel-a
- ✅ Validacija podataka
- ✅ Dual mode (lokalno + Wings)
- ✅ Instant feedback

### **3. Poboljšan UX:**
- ✅ Progress bar radi
- ✅ Real-time status updates
- ✅ Clear error messages
- ✅ Intuitive interface

## 🚀 **Sledeći koraci:**

### **Moguća proširenja:**
1. **Scheduled sync** - Automatska sinhronizacija
2. **Webhook support** - Real-time updates iz Wings-a
3. **Advanced filtering** - Selektivni import kupaca
4. **Bulk operations** - Mass update/delete
5. **Reporting** - Detaljni izveštaji o sinhronizaciji

### **Integration sa MAX website:**
1. **Real-time pricing** - Cene iz Wings API-ja
2. **Stock levels** - Dostupnost proizvoda
3. **Customer portal** - B2B pristup
4. **Order sync** - Porudžbine u Wings sistem

## 📞 **Support:**

### **Debug informacije:**
- Check WordPress error log
- Enable WP_DEBUG for detailed logging
- Use browser console for AJAX errors
- Test API endpoints manually

### **Common issues:**
- **403 errors**: Check user permissions
- **API timeouts**: Reduce batch size
- **Authentication fails**: Verify credentials
- **JSON errors**: Check API response format

**Wings B2B Customer Manager je sada kompletno rešenje za upravljanje B2B kupcima sa direktnom Wings Portal integracijom!** 🎉
