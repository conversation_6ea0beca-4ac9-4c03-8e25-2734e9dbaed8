# Wings B2B Customer Manager - AJAX Fix Guide

## 🚨 **Problem: 403 Forbidden Error on AJAX Import**

```
jquery.js?ver=3.7.1:9940  POST https://dev.brandbusters.net/wp-admin/admin-ajax.php 403 (Forbidden)
```

## 🔍 **Possible Causes & Solutions**

### 1. **Plugin Not Activated**
**Check:** Go to WordPress Admin → Plugins
**Fix:** Activate "Wings B2B Customer Manager" plugin

### 2. **AJAX Action Not Registered**
**Check:** Run debug test files
**Fix:** Ensure plugin classes are loading properly

### 3. **Nonce Mismatch**
**Check:** Verify nonce names match between PHP and JavaScript
**Fix:** Use consistent nonce names

### 4. **Permission Issues**
**Check:** User has `manage_options` capability
**Fix:** Login as administrator

### 5. **File Upload Issues**
**Check:** File upload permissions and size limits
**Fix:** Adjust server settings

## 🛠️ **Step-by-Step Debugging**

### Step 1: Basic Plugin Check
```bash
# Navigate to plugin directory
cd wp-content/plugins/wings-b2b-customer-manager/

# Run simple test
php simple-test.php
```

### Step 2: Check Plugin Activation
1. Go to WordPress Admin → Plugins
2. Look for "Wings B2B Customer Manager"
3. If not active, click "Activate"
4. If activation fails, check error messages

### Step 3: Debug AJAX Actions
```bash
# Run debug actions test
php debug-actions.php
```

### Step 4: Test AJAX Manually
```bash
# Run AJAX test
php test-ajax.php
```

## 🔧 **Quick Fixes**

### Fix 1: Reactivate Plugin
```php
// Deactivate and reactivate plugin
deactivate_plugins('wings-b2b-customer-manager/wings-b2b-customer-manager.php');
activate_plugin('wings-b2b-customer-manager/wings-b2b-customer-manager.php');
```

### Fix 2: Clear Cache
- Clear any caching plugins
- Clear browser cache
- Clear server-side cache

### Fix 3: Check File Permissions
```bash
# Set correct permissions
chmod 755 wp-content/plugins/wings-b2b-customer-manager/
chmod 644 wp-content/plugins/wings-b2b-customer-manager/*.php
```

### Fix 4: Increase Upload Limits
Add to `.htaccess` or `php.ini`:
```
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 300
```

### Fix 5: Debug WordPress
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## 🧪 **Manual AJAX Test**

### Test 1: Basic AJAX
```javascript
jQuery.ajax({
    url: ajaxurl,
    type: 'POST',
    data: {
        action: 'heartbeat',
        _wpnonce: 'heartbeat-nonce-value'
    },
    success: function(response) {
        console.log('Basic AJAX works');
    }
});
```

### Test 2: Wings AJAX
```javascript
jQuery.ajax({
    url: ajaxurl,
    type: 'POST',
    data: {
        action: 'wings_upload_json',
        wings_import_nonce: 'nonce-value'
    },
    success: function(response) {
        console.log('Wings AJAX works');
    }
});
```

## 📋 **Checklist**

- [ ] Plugin is activated
- [ ] WooCommerce is active
- [ ] User has admin permissions
- [ ] File upload directory is writable
- [ ] No PHP errors in log
- [ ] AJAX actions are registered
- [ ] Nonce values match
- [ ] Server allows file uploads
- [ ] No caching conflicts

## 🔍 **Common Error Messages**

### "Neispravna bezbednosna provera"
- **Cause:** Nonce mismatch
- **Fix:** Check nonce generation and verification

### "Nemate dozvolu za ovu akciju"
- **Cause:** User permissions
- **Fix:** Login as administrator

### "Nema uploadovanog fajla"
- **Cause:** File upload issue
- **Fix:** Check file input and server settings

### "Plugin is not compatible with HPOS"
- **Cause:** HPOS compatibility issue
- **Fix:** Already resolved in current version

## 🚀 **Emergency Fix**

If nothing else works, try this emergency fix:

1. **Backup current plugin**
2. **Deactivate plugin**
3. **Delete plugin folder**
4. **Re-upload fresh plugin files**
5. **Activate plugin**
6. **Test import functionality**

## 📞 **Support Information**

### Debug Files Created:
- `simple-test.php` - Basic plugin functionality test
- `debug-actions.php` - AJAX actions debugging
- `test-ajax.php` - AJAX upload testing

### Log Locations:
- WordPress: `wp-content/debug.log`
- Server: Check `error_log` in cPanel or server logs
- Plugin: Look for "Wings B2B" entries

### Browser Console:
- Open Developer Tools (F12)
- Check Console tab for JavaScript errors
- Check Network tab for AJAX request details

## 🎯 **Most Likely Solutions**

Based on the 403 error, the most likely causes are:

1. **Plugin not properly activated** (90% of cases)
2. **User permission issues** (5% of cases)
3. **Server configuration issues** (3% of cases)
4. **Caching conflicts** (2% of cases)

**Start with Step 1 and work through the checklist systematically.**
