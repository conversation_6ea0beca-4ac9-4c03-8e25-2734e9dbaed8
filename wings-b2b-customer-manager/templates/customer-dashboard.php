<?php
/**
 * Customer Dashboard Template
 * Template for B2B customer dashboard
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in and has B2B customer role
if (!is_user_logged_in() || !current_user_can('read')) {
    wp_redirect(wp_login_url());
    exit;
}

get_header();
?>

<div class="wings-b2b-customer-dashboard">
    <div class="container">
        <?php
        // Display the B2B dashboard using shortcode
        echo do_shortcode('[wings_b2b_dashboard]');
        ?>
    </div>
</div>

<style>
.wings-b2b-customer-dashboard {
    padding: 20px 0;
}

.wings-b2b-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-welcome {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.dashboard-welcome h3 {
    margin-top: 0;
    color: #333;
}

.notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid #ffba00;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.notice.notice-warning {
    border-left-color: #ffba00;
}

.notice p {
    margin: 0.5em 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.quick-actions {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quick-actions li {
    margin-bottom: 10px;
}

.quick-actions .button {
    display: inline-block;
    width: 100%;
    text-align: center;
    padding: 10px;
    text-decoration: none;
    background: #0073aa;
    color: #fff;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.quick-actions .button:hover {
    background: #005a87;
    color: #fff;
}

.customer-info ul,
.b2b-info {
    list-style: none;
    padding: 0;
    margin: 0;
}

.customer-info li,
.b2b-info li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.customer-info li:last-child,
.b2b-info li:last-child {
    border-bottom: none;
}

.customer-info strong,
.b2b-info strong {
    display: inline-block;
    width: 120px;
    color: #333;
}

.recent-orders .orders-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-orders .order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.recent-orders .order-item:last-child {
    border-bottom: none;
}

.recent-orders .order-info strong {
    color: #0073aa;
}

.recent-orders .order-date {
    font-size: 0.9em;
    color: #666;
    display: block;
}

.recent-orders .order-status span {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
}

.recent-orders .status-completed {
    background: #46b450;
    color: #fff;
}

.recent-orders .status-processing {
    background: #ffba00;
    color: #fff;
}

.recent-orders .status-pending {
    background: #999;
    color: #fff;
}

.recent-orders .status-cancelled {
    background: #dc3232;
    color: #fff;
}

.recent-orders .order-total {
    font-weight: bold;
    color: #333;
}

.view-all {
    margin-top: 15px;
    text-align: center;
}

.placeholder-email-badge {
    background: #ffba00;
    color: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    margin-left: 5px;
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .recent-orders .order-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .recent-orders .order-status,
    .recent-orders .order-total {
        margin-top: 5px;
    }
    
    .customer-info strong,
    .b2b-info strong {
        width: auto;
        display: block;
        margin-bottom: 5px;
    }
}
</style>

<?php get_footer(); ?>
