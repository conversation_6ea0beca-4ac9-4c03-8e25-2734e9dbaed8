<?php
/**
 * Order History Template
 * Template for displaying customer order history
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in and has B2B customer role
if (!is_user_logged_in() || !current_user_can('read')) {
    wp_redirect(wp_login_url());
    exit;
}

get_header();
?>

<div class="wings-order-history-page">
    <div class="container">
        <?php
        // Display the order history using shortcode
        echo do_shortcode('[wings_order_history]');
        ?>
    </div>
</div>

<style>
.wings-order-history-page {
    padding: 20px 0;
}

.wings-order-history {
    max-width: 1200px;
    margin: 0 auto;
}

.wings-order-history h2 {
    color: #333;
    margin-bottom: 30px;
    border-bottom: 3px solid #0073aa;
    padding-bottom: 10px;
}

.order-filters {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.filter-form .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.filter-group {
    flex: 1;
    min-width: 150px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-group .button {
    padding: 8px 15px;
    margin-right: 10px;
}

.order-actions {
    margin-bottom: 20px;
    text-align: right;
}

.order-actions .button {
    margin-left: 10px;
}

.orders-table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

.wings-orders-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.wings-orders-table th,
.wings-orders-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.wings-orders-table th {
    background: #0073aa;
    color: #fff;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9em;
}

.wings-orders-table tbody tr {
    cursor: pointer;
    transition: background-color 0.3s;
}

.wings-orders-table tbody tr:hover {
    background: #f8f9fa;
}

.wings-orders-table tbody tr:not(.order-details):hover {
    background: #e3f2fd;
}

.order-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
    display: inline-block;
}

.status-completed {
    background: #46b450;
    color: #fff;
}

.status-processing {
    background: #ffba00;
    color: #fff;
}

.status-pending {
    background: #999;
    color: #fff;
}

.status-cancelled {
    background: #dc3232;
    color: #fff;
}

.status-on-hold {
    background: #f56e28;
    color: #fff;
}

.status-refunded {
    background: #999;
    color: #fff;
}

.order-details {
    background: #f8f9fa !important;
}

.order-details td {
    padding: 20px;
}

.order-items h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.order-items ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.order-items li {
    padding: 8px 0;
    border-bottom: 1px solid #ddd;
}

.order-items li:last-child {
    border-bottom: none;
}

.order-note {
    margin-top: 15px;
    padding: 15px;
    background: #fff;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.order-note strong {
    color: #333;
}

.order-note p {
    margin: 5px 0 0 0;
    color: #666;
}

.order-summary {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    color: #666;
}

.no-orders {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.no-orders p {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 20px;
}

.button {
    display: inline-block;
    padding: 8px 15px;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.button:hover {
    background: #005a87;
    color: #fff;
}

.button-primary {
    background: #0073aa;
}

.button-primary:hover {
    background: #005a87;
}

.button-small {
    padding: 6px 10px;
    font-size: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
    .filter-form .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .order-actions {
        text-align: center;
    }
    
    .order-actions .button {
        display: block;
        margin: 5px 0;
        width: 100%;
    }
    
    .wings-orders-table {
        font-size: 0.9em;
    }
    
    .wings-orders-table th,
    .wings-orders-table td {
        padding: 8px;
    }
    
    /* Hide some columns on mobile */
    .wings-orders-table th:nth-child(5),
    .wings-orders-table td:nth-child(5) {
        display: none;
    }
}

@media (max-width: 480px) {
    .wings-orders-table th:nth-child(4),
    .wings-orders-table td:nth-child(4) {
        display: none;
    }
    
    .wings-orders-table {
        font-size: 0.8em;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Toggle order details on row click
    $('.wings-orders-table tbody tr:not(.order-details)').click(function(e) {
        // Don't toggle if clicking on a button or link
        if ($(e.target).is('a, button, .button')) {
            return;
        }
        
        $(this).next('.order-details').slideToggle();
    });
    
    // Export functionality
    $('#export-orders').click(function() {
        var params = new URLSearchParams(window.location.search);
        params.set('action', 'wings_export_orders');
        params.set('format', 'csv');
        params.set('nonce', '<?php echo wp_create_nonce('wings_export_orders'); ?>');
        
        window.location.href = '<?php echo admin_url('admin-ajax.php'); ?>?' + params.toString();
    });
    
    $('#export-orders-pdf').click(function() {
        var params = new URLSearchParams(window.location.search);
        params.set('action', 'wings_export_orders');
        params.set('format', 'pdf');
        params.set('nonce', '<?php echo wp_create_nonce('wings_export_orders'); ?>');
        
        window.location.href = '<?php echo admin_url('admin-ajax.php'); ?>?' + params.toString();
    });
});
</script>

<?php get_footer(); ?>
