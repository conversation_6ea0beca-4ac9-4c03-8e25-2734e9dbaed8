<?php
/**
 * Debug AJAX Actions for Wings B2B Customer Manager
 */

// WordPress environment
require_once('../../../wp-config.php');

// Check if user is logged in and has permissions
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    wp_die('Access denied');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Wings B2B Debug Actions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Wings B2B Debug Actions</h1>
    
    <h2>Plugin Status</h2>
    <?php
    echo '<p><strong>Plugin Class Exists:</strong> ';
    if (class_exists('Wings_B2B_Customer_Manager')) {
        echo '<span class="success">✓ Yes</span>';
    } else {
        echo '<span class="error">✗ No</span>';
    }
    echo '</p>';
    
    echo '<p><strong>Admin Page Class Exists:</strong> ';
    if (class_exists('Wings_B2B_Admin_Page')) {
        echo '<span class="success">✓ Yes</span>';
    } else {
        echo '<span class="error">✗ No</span>';
    }
    echo '</p>';
    
    echo '<p><strong>Customer Importer Class Exists:</strong> ';
    if (class_exists('Wings_B2B_Customer_Importer')) {
        echo '<span class="success">✓ Yes</span>';
    } else {
        echo '<span class="error">✗ No</span>';
    }
    echo '</p>';
    ?>
    
    <h2>WordPress Hooks</h2>
    <?php
    global $wp_filter;
    
    $ajax_actions = array(
        'wp_ajax_wings_upload_json',
        'wp_ajax_wings_import_customers',
        'wp_ajax_wings_export_orders'
    );
    
    foreach ($ajax_actions as $action) {
        echo '<p><strong>' . $action . ':</strong> ';
        if (isset($wp_filter[$action]) && !empty($wp_filter[$action]->callbacks)) {
            echo '<span class="success">✓ Registered</span>';
            echo '<br><small>Callbacks: ' . count($wp_filter[$action]->callbacks) . '</small>';
        } else {
            echo '<span class="error">✗ Not registered</span>';
        }
        echo '</p>';
    }
    ?>
    
    <h2>Test Simple AJAX</h2>
    <button id="test-ajax">Test Basic AJAX</button>
    <div id="ajax-result"></div>
    
    <h2>Test Wings AJAX</h2>
    <button id="test-wings-ajax">Test Wings AJAX</button>
    <div id="wings-result"></div>
    
    <h2>Manual Action Test</h2>
    <p>Try calling the action directly:</p>
    <a href="<?php echo admin_url('admin-ajax.php?action=wings_upload_json'); ?>" target="_blank">
        Test wings_upload_json action
    </a>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
    jQuery(document).ready(function($) {
        $('#test-ajax').click(function() {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'heartbeat',
                    _wpnonce: '<?php echo wp_create_nonce('heartbeat-nonce'); ?>'
                },
                success: function(response) {
                    $('#ajax-result').html('<span class="success">✓ Basic AJAX works</span><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#ajax-result').html('<span class="error">✗ Basic AJAX failed: ' + error + '</span><pre>' + xhr.responseText + '</pre>');
                }
            });
        });
        
        $('#test-wings-ajax').click(function() {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'wings_upload_json',
                    wings_import_nonce: '<?php echo wp_create_nonce('wings_import_customers'); ?>'
                },
                success: function(response) {
                    $('#wings-result').html('<span class="success">✓ Wings AJAX responded</span><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#wings-result').html('<span class="error">✗ Wings AJAX failed: ' + error + '</span><pre>' + xhr.responseText + '</pre>');
                }
            });
        });
    });
    </script>
    
    <h2>WordPress Info</h2>
    <ul>
        <li><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></li>
        <li><strong>WooCommerce Active:</strong> <?php echo class_exists('WooCommerce') ? 'Yes' : 'No'; ?></li>
        <li><strong>Current User ID:</strong> <?php echo get_current_user_id(); ?></li>
        <li><strong>Can Manage Options:</strong> <?php echo current_user_can('manage_options') ? 'Yes' : 'No'; ?></li>
        <li><strong>AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?></li>
        <li><strong>Is Admin:</strong> <?php echo is_admin() ? 'Yes' : 'No'; ?></li>
    </ul>
    
    <h2>Plugin Constants</h2>
    <?php
    $constants = array(
        'WINGS_B2B_VERSION',
        'WINGS_B2B_PLUGIN_FILE',
        'WINGS_B2B_PLUGIN_DIR',
        'WINGS_B2B_PLUGIN_URL'
    );
    
    foreach ($constants as $constant) {
        echo '<p><strong>' . $constant . ':</strong> ';
        if (defined($constant)) {
            echo '<span class="success">' . constant($constant) . '</span>';
        } else {
            echo '<span class="error">Not defined</span>';
        }
        echo '</p>';
    }
    ?>
    
    <h2>Active Plugins</h2>
    <?php
    $active_plugins = get_option('active_plugins');
    foreach ($active_plugins as $plugin) {
        if (strpos($plugin, 'wings-b2b') !== false) {
            echo '<p><span class="success">✓ ' . $plugin . '</span></p>';
        }
    }
    ?>
    
    <h2>Error Log</h2>
    <p>Check your WordPress error log for any Wings B2B related errors.</p>
    <p><strong>Error Log Location:</strong> <?php echo ini_get('error_log') ?: 'Check wp-config.php for WP_DEBUG_LOG'; ?></p>
</body>
</html>
