<?php
/**
 * Email Generator Class
 * Generates placeholder email addresses for customers without emails
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_Email_Generator {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Constructor
    }
    
    /**
     * Generate email address for customer
     * 
     * @param array $customer_data Customer data from Wings Portal
     * @return string Generated email address
     */
    public function generate_email($customer_data) {
        $domain = get_option('wings_b2b_email_domain', 'b2b.placeholder.local');
        $sifra = sanitize_text_field($customer_data['sifra']);
        
        // Primary format: sifra@domain
        $email = $sifra . '@' . $domain;
        
        // Check if email already exists
        if (email_exists($email)) {
            // Add timestamp suffix if email exists
            $email = $sifra . '-' . time() . '@' . $domain;
        }
        
        return $email;
    }
    
    /**
     * Generate username from customer data
     * 
     * @param array $customer_data Customer data from Wings Portal
     * @return string Generated username
     */
    public function generate_username($customer_data) {
        $sifra = sanitize_text_field($customer_data['sifra']);
        $naziv = sanitize_text_field($customer_data['naziv']);
        
        // Primary format: wings_sifra
        $username = 'wings_' . $sifra;
        
        // Check if username already exists
        if (username_exists($username)) {
            // Add timestamp suffix if username exists
            $username = 'wings_' . $sifra . '_' . time();
        }
        
        return $username;
    }
    
    /**
     * Generate display name from customer data
     * 
     * @param array $customer_data Customer data from Wings Portal
     * @return string Generated display name
     */
    public function generate_display_name($customer_data) {
        $naziv = sanitize_text_field($customer_data['naziv']);
        $kontakt = sanitize_text_field($customer_data['kontakt']);
        
        if (!empty($kontakt) && !empty($naziv)) {
            return $kontakt . ' (' . $naziv . ')';
        } elseif (!empty($naziv)) {
            return $naziv;
        } elseif (!empty($kontakt)) {
            return $kontakt;
        } else {
            return 'Wings Customer ' . $customer_data['sifra'];
        }
    }
    
    /**
     * Check if email is a placeholder email
     * 
     * @param string $email Email address to check
     * @return bool True if placeholder email
     */
    public function is_placeholder_email($email) {
        $domain = get_option('wings_b2b_email_domain', 'b2b.placeholder.local');
        return strpos($email, '@' . $domain) !== false;
    }
    
    /**
     * Get all users with placeholder emails
     * 
     * @return array Array of user IDs with placeholder emails
     */
    public function get_users_with_placeholder_emails() {
        $domain = get_option('wings_b2b_email_domain', 'b2b.placeholder.local');
        
        $users = get_users(array(
            'role' => 'b2b_customer',
            'search' => '*@' . $domain,
            'search_columns' => array('user_email'),
            'fields' => 'ID'
        ));
        
        return $users;
    }
    
    /**
     * Update placeholder email to real email
     * 
     * @param int $user_id User ID
     * @param string $new_email New email address
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function update_placeholder_email($user_id, $new_email) {
        // Validate email
        if (!is_email($new_email)) {
            return new WP_Error('invalid_email', __('Neispravna email adresa.', 'wings-b2b-customer-manager'));
        }
        
        // Check if email already exists
        if (email_exists($new_email) && email_exists($new_email) != $user_id) {
            return new WP_Error('email_exists', __('Email adresa već postoji.', 'wings-b2b-customer-manager'));
        }
        
        // Update user email
        $result = wp_update_user(array(
            'ID' => $user_id,
            'user_email' => $new_email
        ));
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Remove placeholder email meta
        delete_user_meta($user_id, 'wings_placeholder_email');
        
        // Add updated email meta
        update_user_meta($user_id, 'wings_email_updated', current_time('mysql'));
        
        return true;
    }
    
    /**
     * Mark email as placeholder
     * 
     * @param int $user_id User ID
     * @param string $email Placeholder email
     */
    public function mark_as_placeholder($user_id, $email) {
        update_user_meta($user_id, 'wings_placeholder_email', true);
        update_user_meta($user_id, 'wings_original_email', $email);
        update_user_meta($user_id, 'wings_email_generated', current_time('mysql'));
    }
    
    /**
     * Generate password for new customer
     * 
     * @param int $length Password length
     * @return string Generated password
     */
    public function generate_password($length = 12) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[rand(0, strlen($chars) - 1)];
        }
        
        return $password;
    }
    
    /**
     * Send welcome email to new B2B customer
     * 
     * @param int $user_id User ID
     * @param string $password Generated password
     * @param array $customer_data Original customer data
     */
    public function send_welcome_email($user_id, $password, $customer_data) {
        $user = get_user_by('ID', $user_id);
        
        if (!$user) {
            return false;
        }
        
        $subject = __('Dobrodošli u našu B2B prodavnicu', 'wings-b2b-customer-manager');
        
        $message = sprintf(
            __('Poštovani %s,

Vaš B2B nalog je uspešno kreiran.

Podaci za prijavu:
Username: %s
Email: %s
Password: %s

Napomena: Vaša email adresa je privremena. Molimo vas da je ažurirate u vašem profilu.

Firma: %s
Adresa: %s %s
Telefon: %s

Možete se prijaviti na: %s

Srdačan pozdrav,
Tim za podršku', 'wings-b2b-customer-manager'),
            $user->display_name,
            $user->user_login,
            $user->user_email,
            $password,
            $customer_data['naziv'],
            $customer_data['adresa'],
            $customer_data['mesto'],
            $customer_data['telefon'],
            wp_login_url()
        );
        
        return wp_mail($user->user_email, $subject, $message);
    }
}
