<?php
/**
 * Customer Importer Class
 * Imports customers from Wings Portal JSON data
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_Customer_Importer {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_wings_import_customers', array($this, 'ajax_import_customers'));
        add_action('wp_ajax_wings_import_batch', array($this, 'ajax_import_batch'));
        add_action('wp_ajax_wings_prepare_import', array($this, 'ajax_prepare_import'));
    }
    
    /**
     * Import customers from JSON file
     * 
     * @param string $json_file_path Path to JSON file
     * @return array Import results
     */
    public function import_from_json($json_file_path) {
        if (!file_exists($json_file_path)) {
            return array(
                'success' => false,
                'message' => __('<PERSON><PERSON><PERSON> fajl ne postoji.', 'wings-b2b-customer-manager')
            );
        }
        
        $json_content = file_get_contents($json_file_path);
        $data = json_decode($json_content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'success' => false,
                'message' => __('Greška u čitanju JSON fajla.', 'wings-b2b-customer-manager')
            );
        }
        
        if (!isset($data['customers']) || !is_array($data['customers'])) {
            return array(
                'success' => false,
                'message' => __('Neispravna struktura JSON fajla.', 'wings-b2b-customer-manager')
            );
        }
        
        return $this->process_customers($data['customers']);
    }
    
    /**
     * Process customers array
     * 
     * @param array $customers Array of customer data
     * @return array Processing results
     */
    public function process_customers($customers) {
        $results = array(
            'success' => true,
            'total' => count($customers),
            'imported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => array()
        );
        
        $email_generator = Wings_B2B_Email_Generator::get_instance();
        
        foreach ($customers as $customer_data) {
            if (!isset($customer_data['attributes'])) {
                $results['skipped']++;
                continue;
            }
            
            $attributes = $customer_data['attributes'];
            
            // Skip if no sifra
            if (empty($attributes['sifra'])) {
                $results['skipped']++;
                continue;
            }
            
            try {
                $result = $this->import_single_customer($attributes, $email_generator);
                
                if ($result['action'] === 'imported') {
                    $results['imported']++;
                } elseif ($result['action'] === 'updated') {
                    $results['updated']++;
                } else {
                    $results['skipped']++;
                }
                
            } catch (Exception $e) {
                $results['errors'][] = sprintf(
                    __('Greška kod kupca %s: %s', 'wings-b2b-customer-manager'),
                    $attributes['sifra'],
                    $e->getMessage()
                );
            }
        }
        
        return $results;
    }
    
    /**
     * Import single customer
     * 
     * @param array $customer_data Customer attributes
     * @param Wings_B2B_Email_Generator $email_generator Email generator instance
     * @return array Import result
     */
    private function import_single_customer($customer_data, $email_generator) {
        global $wpdb;
        
        // Check if customer already exists by sifra
        $existing_customer = $wpdb->get_row($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->prefix}wings_b2b_customers WHERE wings_sifra = %s",
            $customer_data['sifra']
        ));
        
        if ($existing_customer) {
            // Update existing customer
            return $this->update_existing_customer($existing_customer->user_id, $customer_data);
        } else {
            // Create new customer
            return $this->create_new_customer($customer_data, $email_generator);
        }
    }
    
    /**
     * Create new customer
     * 
     * @param array $customer_data Customer attributes
     * @param Wings_B2B_Email_Generator $email_generator Email generator instance
     * @return array Creation result
     */
    private function create_new_customer($customer_data, $email_generator) {
        // Generate user data
        $username = $email_generator->generate_username($customer_data);
        $email = $email_generator->generate_email($customer_data);
        $password = $email_generator->generate_password();
        $display_name = $email_generator->generate_display_name($customer_data);
        
        // Create WordPress user
        $user_id = wp_create_user($username, $password, $email);
        
        if (is_wp_error($user_id)) {
            throw new Exception($user_id->get_error_message());
        }
        
        // Update user data
        wp_update_user(array(
            'ID' => $user_id,
            'display_name' => $display_name,
            'first_name' => $customer_data['kontakt'],
            'last_name' => '',
            'role' => 'b2b_customer'
        ));
        
        // Mark email as placeholder
        $email_generator->mark_as_placeholder($user_id, $email);
        
        // Save additional customer data
        $this->save_customer_meta($user_id, $customer_data);
        
        // Save to custom table
        $this->save_customer_to_table($user_id, $customer_data);
        
        // Send welcome email (optional)
        // $email_generator->send_welcome_email($user_id, $password, $customer_data);
        
        return array('action' => 'imported', 'user_id' => $user_id);
    }
    
    /**
     * Update existing customer
     * 
     * @param int $user_id User ID
     * @param array $customer_data Customer attributes
     * @return array Update result
     */
    private function update_existing_customer($user_id, $customer_data) {
        // Update user meta
        $this->save_customer_meta($user_id, $customer_data);
        
        // Update custom table
        $this->update_customer_in_table($user_id, $customer_data);
        
        return array('action' => 'updated', 'user_id' => $user_id);
    }
    
    /**
     * Save customer meta data
     * 
     * @param int $user_id User ID
     * @param array $customer_data Customer attributes
     */
    private function save_customer_meta($user_id, $customer_data) {
        // WooCommerce billing fields
        update_user_meta($user_id, 'billing_company', $customer_data['naziv']);
        update_user_meta($user_id, 'billing_address_1', $customer_data['adresa']);
        update_user_meta($user_id, 'billing_city', $customer_data['mesto']);
        update_user_meta($user_id, 'billing_phone', $customer_data['telefon']);
        
        // Wings specific data
        update_user_meta($user_id, 'wings_sifra', $customer_data['sifra']);
        update_user_meta($user_id, 'wings_kontakt', $customer_data['kontakt']);
        update_user_meta($user_id, 'wings_fax', $customer_data['fax']);
        update_user_meta($user_id, 'wings_mobilni', $customer_data['mobilni']);
        update_user_meta($user_id, 'wings_radnovreme', $customer_data['radnovreme']);
        update_user_meta($user_id, 'wings_pib', $customer_data['pib']);
        update_user_meta($user_id, 'wings_mb', $customer_data['mb']);
        update_user_meta($user_id, 'wings_rabat', $customer_data['rabat']);
        update_user_meta($user_id, 'wings_komercijalista', $customer_data['komercijalista']);
        update_user_meta($user_id, 'wings_limit', $customer_data['limit']);
        update_user_meta($user_id, 'wings_racun', $customer_data['racun']);
        update_user_meta($user_id, 'wings_rokplacanja', $customer_data['rokplacanja']);
        update_user_meta($user_id, 'wings_tolerancija', $customer_data['tolerancija']);
        update_user_meta($user_id, 'wings_klasa', $customer_data['klasa']);
        update_user_meta($user_id, 'wings_status', $customer_data['status']);
        update_user_meta($user_id, 'wings_last_sync', current_time('mysql'));
    }
    
    /**
     * Save customer to custom table
     * 
     * @param int $user_id User ID
     * @param array $customer_data Customer attributes
     */
    private function save_customer_to_table($user_id, $customer_data) {
        global $wpdb;
        
        $wpdb->insert(
            $wpdb->prefix . 'wings_b2b_customers',
            array(
                'user_id' => $user_id,
                'wings_sifra' => $customer_data['sifra'],
                'pib' => $customer_data['pib'],
                'mb' => $customer_data['mb'],
                'rabat' => floatval($customer_data['rabat']),
                'komercijalista' => $customer_data['komercijalista'],
                'limit_amount' => floatval($customer_data['limit']),
                'payment_terms' => intval($customer_data['rokplacanja']),
                'tolerancija' => intval($customer_data['tolerancija']),
                'klasa' => intval($customer_data['klasa']),
                'status' => $customer_data['status']
            ),
            array('%d', '%s', '%s', '%s', '%f', '%s', '%f', '%d', '%d', '%d', '%s')
        );
    }
    
    /**
     * Update customer in custom table
     * 
     * @param int $user_id User ID
     * @param array $customer_data Customer attributes
     */
    private function update_customer_in_table($user_id, $customer_data) {
        global $wpdb;
        
        $wpdb->update(
            $wpdb->prefix . 'wings_b2b_customers',
            array(
                'pib' => $customer_data['pib'],
                'mb' => $customer_data['mb'],
                'rabat' => floatval($customer_data['rabat']),
                'komercijalista' => $customer_data['komercijalista'],
                'limit_amount' => floatval($customer_data['limit']),
                'payment_terms' => intval($customer_data['rokplacanja']),
                'tolerancija' => intval($customer_data['tolerancija']),
                'klasa' => intval($customer_data['klasa']),
                'status' => $customer_data['status']
            ),
            array('user_id' => $user_id),
            array('%s', '%s', '%f', '%s', '%f', '%d', '%d', '%d', '%s'),
            array('%d')
        );
    }
    
    /**
     * AJAX handler for preparing import (analyze file)
     */
    public function ajax_prepare_import() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }

        $json_file = $_POST['json_file'] ?? '';

        if (empty($json_file) || !file_exists($json_file)) {
            wp_send_json_error(__('JSON fajl ne postoji.', 'wings-b2b-customer-manager'));
        }

        // Analyze file
        $analysis = $this->analyze_json_file($json_file);

        if ($analysis['success']) {
            wp_send_json_success($analysis);
        } else {
            wp_send_json_error($analysis['message']);
        }
    }

    /**
     * AJAX handler for batch import
     */
    public function ajax_import_batch() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }

        $json_file = $_POST['json_file'] ?? '';
        $batch_start = intval($_POST['batch_start'] ?? 0);
        $batch_size = intval($_POST['batch_size'] ?? 50);

        if (empty($json_file) || !file_exists($json_file)) {
            wp_send_json_error(__('JSON fajl ne postoji.', 'wings-b2b-customer-manager'));
        }

        // Process batch
        $result = $this->import_batch_from_json($json_file, $batch_start, $batch_size);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for customer import (legacy - now redirects to batch)
     */
    public function ajax_import_customers() {
        // Redirect to prepare import for large files
        $this->ajax_prepare_import();
    }

    /**
     * Analyze JSON file without importing
     *
     * @param string $json_file_path Path to JSON file
     * @return array Analysis results
     */
    public function analyze_json_file($json_file_path) {
        if (!file_exists($json_file_path)) {
            return array(
                'success' => false,
                'message' => __('JSON fajl ne postoji.', 'wings-b2b-customer-manager')
            );
        }

        $json_content = file_get_contents($json_file_path);
        $data = json_decode($json_content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'success' => false,
                'message' => __('Greška u čitanju JSON fajla.', 'wings-b2b-customer-manager')
            );
        }

        if (!isset($data['customers']) || !is_array($data['customers'])) {
            return array(
                'success' => false,
                'message' => __('Neispravna struktura JSON fajla.', 'wings-b2b-customer-manager')
            );
        }

        $total_customers = count($data['customers']);
        $batch_size = 50; // Process 50 customers at a time
        $total_batches = ceil($total_customers / $batch_size);

        // Analyze first few customers to check data quality
        $sample_customers = array_slice($data['customers'], 0, 5);
        $sample_analysis = array();

        foreach ($sample_customers as $customer) {
            if (isset($customer['attributes'])) {
                $attrs = $customer['attributes'];
                $sample_analysis[] = array(
                    'sifra' => $attrs['sifra'] ?? 'N/A',
                    'naziv' => $attrs['naziv'] ?? 'N/A',
                    'email' => !empty($attrs['email']) ? 'Has email' : 'No email',
                    'telefon' => !empty($attrs['telefon']) ? 'Has phone' : 'No phone'
                );
            }
        }

        return array(
            'success' => true,
            'total_customers' => $total_customers,
            'batch_size' => $batch_size,
            'total_batches' => $total_batches,
            'estimated_time' => $total_batches * 30, // 30 seconds per batch estimate
            'sample_data' => $sample_analysis,
            'file_size' => filesize($json_file_path),
            'file_path' => $json_file_path
        );
    }

    /**
     * Import batch of customers from JSON file
     *
     * @param string $json_file_path Path to JSON file
     * @param int $batch_start Starting index
     * @param int $batch_size Number of customers to process
     * @return array Import results
     */
    public function import_batch_from_json($json_file_path, $batch_start = 0, $batch_size = 50) {
        if (!file_exists($json_file_path)) {
            return array(
                'success' => false,
                'message' => __('JSON fajl ne postoji.', 'wings-b2b-customer-manager')
            );
        }

        $json_content = file_get_contents($json_file_path);
        $data = json_decode($json_content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'success' => false,
                'message' => __('Greška u čitanju JSON fajla.', 'wings-b2b-customer-manager')
            );
        }

        if (!isset($data['customers']) || !is_array($data['customers'])) {
            return array(
                'success' => false,
                'message' => __('Neispravna struktura JSON fajla.', 'wings-b2b-customer-manager')
            );
        }

        $all_customers = $data['customers'];
        $total_customers = count($all_customers);

        // Get batch slice
        $batch_customers = array_slice($all_customers, $batch_start, $batch_size);

        // Process batch
        $results = $this->process_customers($batch_customers);

        // Add batch information
        $results['batch_start'] = $batch_start;
        $results['batch_size'] = count($batch_customers);
        $results['total_customers'] = $total_customers;
        $results['processed'] = $batch_start + count($batch_customers);
        $results['remaining'] = max(0, $total_customers - ($batch_start + count($batch_customers)));
        $results['progress_percent'] = round(($results['processed'] / $total_customers) * 100, 2);
        $results['is_complete'] = $results['remaining'] == 0;

        return $results;
    }
}
