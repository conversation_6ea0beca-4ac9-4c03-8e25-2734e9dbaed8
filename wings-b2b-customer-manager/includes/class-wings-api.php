<?php
/**
 * Wings Portal API Integration Class
 * Handles communication with Wings Portal API
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_API {
    
    private static $instance = null;
    private $api_base_url;
    private $alias;
    private $session_cookie;
    private $authenticated = false;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->api_base_url = 'https://portal.wings.rs/api/v1';
        $this->alias = get_option('wings_b2b_api_alias', 'grossaql'); // Default test alias
        
        add_action('wp_ajax_wings_api_import_customers', array($this, 'ajax_import_customers_from_api'));
        add_action('wp_ajax_wings_api_create_customer', array($this, 'ajax_create_customer_in_wings'));
        add_action('wp_ajax_wings_api_test_connection', array($this, 'ajax_test_api_connection'));
        add_action('wp_ajax_wings_api_debug_response', array($this, 'ajax_debug_api_response'));
    }
    
    /**
     * Authenticate with Wings Portal API
     * 
     * @param string $username Username
     * @param string $password Password
     * @return bool|WP_Error Authentication result
     */
    public function authenticate($username = null, $password = null) {
        if (!$username) {
            $username = get_option('wings_b2b_api_username', 'aql');
        }
        if (!$password) {
            $password = get_option('wings_b2b_api_password', 'grossaql');
        }
        
        $endpoint = $this->api_base_url . '/' . $this->alias . '/system.user.log';
        
        $response = wp_remote_post($endpoint, array(
            'body' => array(
                'aUn' => $username,
                'aUp' => $password
            ),
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded'
            )
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_error', 'Invalid JSON response from Wings API');
        }
        
        // Check for successful authentication
        if (isset($data['data']) && !empty($data['data'])) {
            // Extract session cookie from response headers
            $cookies = wp_remote_retrieve_header($response, 'set-cookie');
            if ($cookies) {
                if (is_array($cookies)) {
                    foreach ($cookies as $cookie) {
                        if (strpos($cookie, 'PHPSESSID') !== false) {
                            $this->session_cookie = $cookie;
                            break;
                        }
                    }
                } else {
                    if (strpos($cookies, 'PHPSESSID') !== false) {
                        $this->session_cookie = $cookies;
                    }
                }
            }
            
            $this->authenticated = true;
            return true;
        }
        
        return new WP_Error('auth_failed', 'Authentication failed');
    }
    
    /**
     * Make authenticated API request
     * 
     * @param string $endpoint API endpoint
     * @param array $params Request parameters
     * @param string $method HTTP method
     * @return array|WP_Error API response
     */
    public function make_request($endpoint, $params = array(), $method = 'GET') {
        if (!$this->authenticated) {
            $auth_result = $this->authenticate();
            if (is_wp_error($auth_result)) {
                return $auth_result;
            }
        }
        
        $url = $this->api_base_url . '/' . $this->alias . '/' . $endpoint;
        
        $args = array(
            'timeout' => 60,
            'headers' => array()
        );
        
        // Add session cookie if available
        if ($this->session_cookie) {
            $args['headers']['Cookie'] = $this->session_cookie;
        }
        
        if ($method === 'POST') {
            $args['body'] = $params;
            $response = wp_remote_post($url, $args);
        } else {
            if (!empty($params)) {
                $url .= '?' . http_build_query($params);
            }
            $response = wp_remote_get($url, $args);
        }
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_error', 'Invalid JSON response: ' . $body);
        }
        
        return $data;
    }
    
    /**
     * Get all customers from Wings Portal
     * 
     * @param int $limit Number of customers to fetch
     * @param int $start Starting offset
     * @return array|WP_Error Customers data
     */
    public function get_customers($limit = 1000, $start = 0) {
        $params = array(
            'dLength' => $limit,
            'dStart' => $start,
            'output' => 'jsonapi'
        );
        
        return $this->make_request('local.kupac.svi', $params, 'GET');
    }
    
    /**
     * Create new customer in Wings Portal
     * 
     * @param array $customer_data Customer data
     * @return array|WP_Error Creation result
     */
    public function create_customer($customer_data) {
        $params = array(
            'naziv' => $customer_data['naziv'] ?? '',
            'adresa' => $customer_data['adresa'] ?? '',
            'mesto' => $customer_data['mesto'] ?? '',
            'kontakt' => $customer_data['kontakt'] ?? '',
            'telefon' => $customer_data['telefon'] ?? '',
            'email' => $customer_data['email'] ?? '',
            'pib' => $customer_data['pib'] ?? '',
            'mb' => $customer_data['mb'] ?? '',
            'komercijalista' => $customer_data['komercijalista'] ?? '1',
            'klasa' => $customer_data['klasa'] ?? '1'
        );
        
        return $this->make_request('local.kupac.nov', $params, 'POST');
    }
    
    /**
     * Test API connection
     * 
     * @return bool|WP_Error Connection test result
     */
    public function test_connection() {
        $auth_result = $this->authenticate();
        if (is_wp_error($auth_result)) {
            return $auth_result;
        }
        
        // Try to get user info
        $result = $this->make_request('local.korisnik.info');
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        return true;
    }
    
    /**
     * AJAX handler for importing customers from API
     */
    public function ajax_import_customers_from_api() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }

        $limit = intval($_POST['limit'] ?? 1000);
        $start = intval($_POST['start'] ?? 0);

        // Get customers from API
        $customers_response = $this->get_customers($limit, $start);

        if (is_wp_error($customers_response)) {
            wp_send_json_error($customers_response->get_error_message());
        }

        // Debug: Log the actual API response structure
        error_log('Wings API Response: ' . print_r($customers_response, true));

        // Try different possible response structures
        $customers_data = null;

        if (isset($customers_response['data']) && is_array($customers_response['data'])) {
            // JSON API format
            $customers_data = $customers_response['data'];
        } elseif (isset($customers_response['aaData']) && is_array($customers_response['aaData'])) {
            // DataTables format
            $customers_data = $customers_response['aaData'];
        } elseif (is_array($customers_response) && !empty($customers_response)) {
            // Direct array format
            $customers_data = $customers_response;
        } else {
            // Return debug info to help identify structure
            wp_send_json_error(array(
                'message' => __('Neispravna struktura odgovora API-ja.', 'wings-b2b-customer-manager'),
                'debug_info' => array(
                    'response_keys' => array_keys($customers_response),
                    'response_type' => gettype($customers_response),
                    'sample_data' => array_slice($customers_response, 0, 2, true)
                )
            ));
        }

        // Convert API response to import format
        $import_data = array(
            'export_info' => array(
                'timestamp' => current_time('c'),
                'total_customers' => count($customers_data),
                'source' => 'Wings Portal API'
            ),
            'customers' => array()
        );

        foreach ($customers_data as $index => $customer) {
            // Handle different customer data formats
            if (isset($customer['attributes'])) {
                // JSON API format
                $import_data['customers'][] = array(
                    'type' => 'local-kupac-svi',
                    'id' => $customer['id'] ?? $index,
                    'attributes' => $customer['attributes']
                );
            } elseif (is_array($customer) && count($customer) > 5) {
                // Array format - map to attributes
                $attributes = $this->map_array_to_attributes($customer);
                if ($attributes) {
                    $import_data['customers'][] = array(
                        'type' => 'local-kupac-svi',
                        'id' => $attributes['sifra'] ?? $index,
                        'attributes' => $attributes
                    );
                }
            } elseif (is_object($customer)) {
                // Object format - convert to array
                $customer_array = (array) $customer;
                $attributes = $this->map_array_to_attributes($customer_array);
                if ($attributes) {
                    $import_data['customers'][] = array(
                        'type' => 'local-kupac-svi',
                        'id' => $attributes['sifra'] ?? $index,
                        'attributes' => $attributes
                    );
                }
            }
        }

        if (empty($import_data['customers'])) {
            // Enhanced debug information
            $debug_info = array(
                'total_records' => count($customers_data),
                'sample_record' => $customers_data[0] ?? null,
                'validation_details' => array()
            );

            // Add validation details for first few records
            for ($i = 0; $i < min(3, count($customers_data)); $i++) {
                $customer = $customers_data[$i];
                $validation_detail = array(
                    'index' => $i,
                    'type' => gettype($customer),
                    'is_array' => is_array($customer),
                    'is_object' => is_object($customer),
                    'has_attributes' => isset($customer['attributes']),
                    'has_id' => isset($customer['id']),
                    'array_count' => is_array($customer) ? count($customer) : 0,
                    'keys' => is_array($customer) ? array_keys($customer) : [],
                    'mapping_result' => null
                );

                // Test mapping
                if (is_array($customer) && count($customer) > 5) {
                    $test_attributes = $this->map_array_to_attributes($customer);
                    $validation_detail['mapping_result'] = $test_attributes ? 'success' : 'failed';
                    $validation_detail['mapped_attributes'] = $test_attributes;
                } elseif (is_object($customer)) {
                    $customer_array = (array) $customer;
                    $test_attributes = $this->map_array_to_attributes($customer_array);
                    $validation_detail['mapping_result'] = $test_attributes ? 'success' : 'failed';
                    $validation_detail['mapped_attributes'] = $test_attributes;
                }

                $debug_info['validation_details'][] = $validation_detail;
            }

            wp_send_json_error(array(
                'message' => __('Nema validnih kupaca za import.', 'wings-b2b-customer-manager'),
                'debug_info' => $debug_info
            ));
        }

        // Process import using existing importer
        $importer = Wings_B2B_Customer_Importer::get_instance();
        $result = $importer->process_customers($import_data['customers']);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * AJAX handler for creating customer in Wings
     */
    public function ajax_create_customer_in_wings() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }
        
        $customer_data = array(
            'naziv' => sanitize_text_field($_POST['naziv'] ?? ''),
            'adresa' => sanitize_text_field($_POST['adresa'] ?? ''),
            'mesto' => sanitize_text_field($_POST['mesto'] ?? ''),
            'kontakt' => sanitize_text_field($_POST['kontakt'] ?? ''),
            'telefon' => sanitize_text_field($_POST['telefon'] ?? ''),
            'email' => sanitize_email($_POST['email'] ?? ''),
            'pib' => sanitize_text_field($_POST['pib'] ?? ''),
            'mb' => sanitize_text_field($_POST['mb'] ?? ''),
            'komercijalista' => sanitize_text_field($_POST['komercijalista'] ?? '1'),
            'klasa' => intval($_POST['klasa'] ?? 1)
        );
        
        $result = $this->create_customer($customer_data);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success(array(
            'message' => __('Kupac uspešno kreiran u Wings Portal-u.', 'wings-b2b-customer-manager'),
            'customer_id' => $result,
            'data' => $result
        ));
    }
    
    /**
     * AJAX handler for testing API connection
     */
    public function ajax_test_api_connection() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }
        
        $result = $this->test_connection();
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success(array(
            'message' => __('Konekcija sa Wings Portal API je uspešna.', 'wings-b2b-customer-manager')
        ));
    }

    /**
     * Map array data to attributes format
     *
     * @param array $customer_array Raw customer data array
     * @return array|null Mapped attributes or null if invalid
     */
    private function map_array_to_attributes($customer_array) {
        // Common Wings Portal API field mappings
        $field_mappings = array(
            0 => 'sifra',
            1 => 'naziv',
            2 => 'adresa',
            3 => 'mesto',
            4 => 'kontakt',
            5 => 'telefon',
            6 => 'fax',
            7 => 'mobilni',
            8 => 'email',
            9 => 'radnovreme',
            10 => 'status',
            11 => 'pib',
            12 => 'rabat',
            13 => 'komercijalista',
            14 => 'mb',
            15 => 'limit',
            16 => 'racun',
            17 => 'rokplacanja',
            18 => 'tolerancija',
            19 => 'klasa'
        );

        $attributes = array();

        // If array has numeric keys, map using field mappings
        if (isset($customer_array[0])) {
            foreach ($field_mappings as $index => $field_name) {
                if (isset($customer_array[$index])) {
                    $attributes[$field_name] = $customer_array[$index];
                }
            }
        } else {
            // If array has string keys, use direct mapping
            $direct_mappings = array(
                'sifra' => 'sifra',
                'naziv' => 'naziv',
                'adresa' => 'adresa',
                'mesto' => 'mesto',
                'kontakt' => 'kontakt',
                'telefon' => 'telefon',
                'fax' => 'fax',
                'mobilni' => 'mobilni',
                'email' => 'email',
                'radnovreme' => 'radnovreme',
                'status' => 'status',
                'pib' => 'pib',
                'rabat' => 'rabat',
                'komercijalista' => 'komercijalista',
                'mb' => 'mb',
                'limit' => 'limit',
                'racun' => 'racun',
                'rokplacanja' => 'rokplacanja',
                'tolerancija' => 'tolerancija',
                'klasa' => 'klasa'
            );

            foreach ($direct_mappings as $api_field => $attr_field) {
                if (isset($customer_array[$api_field])) {
                    $attributes[$attr_field] = $customer_array[$api_field];
                }
            }
        }

        // Validate that we have minimum required fields
        // More flexible validation - accept if we have any meaningful data
        $has_meaningful_data = false;

        // Check for any of these key fields
        $key_fields = ['sifra', 'naziv', 'email', 'kontakt', 'telefon'];
        foreach ($key_fields as $field) {
            if (!empty($attributes[$field])) {
                $has_meaningful_data = true;
                break;
            }
        }

        // Also check numeric array format
        if (!$has_meaningful_data && isset($customer_array[0])) {
            for ($i = 0; $i <= 4; $i++) { // Check first 5 elements
                if (!empty($customer_array[$i])) {
                    $has_meaningful_data = true;
                    break;
                }
            }
        }

        if (!$has_meaningful_data) {
            return null;
        }

        // Generate sifra if missing but we have other data
        if (empty($attributes['sifra'])) {
            if (!empty($attributes['naziv'])) {
                $attributes['sifra'] = sanitize_title($attributes['naziv']) . '_' . time();
            } elseif (!empty($attributes['email'])) {
                $attributes['sifra'] = sanitize_title($attributes['email']) . '_' . time();
            } elseif (!empty($attributes['kontakt'])) {
                $attributes['sifra'] = sanitize_title($attributes['kontakt']) . '_' . time();
            } else {
                $attributes['sifra'] = 'customer_' . time() . '_' . rand(1000, 9999);
            }
        }

        // Generate naziv if missing but we have other data
        if (empty($attributes['naziv'])) {
            if (!empty($attributes['kontakt'])) {
                $attributes['naziv'] = $attributes['kontakt'];
            } elseif (!empty($attributes['email'])) {
                $attributes['naziv'] = $attributes['email'];
            } elseif (!empty($attributes['sifra'])) {
                $attributes['naziv'] = 'Customer ' . $attributes['sifra'];
            } else {
                $attributes['naziv'] = 'Customer ' . time();
            }
        }

        // Set defaults for missing fields
        $defaults = array(
            'sifra' => '',
            'naziv' => '',
            'adresa' => '',
            'mesto' => '',
            'kontakt' => '',
            'telefon' => '',
            'fax' => '',
            'mobilni' => '',
            'email' => '',
            'radnovreme' => '',
            'status' => 'K',
            'pib' => '',
            'rabat' => 0.0,
            'komercijalista' => '1',
            'mb' => '',
            'limit' => 0.0,
            'racun' => '',
            'rokplacanja' => 0,
            'tolerancija' => 0,
            'klasa' => 1
        );

        return array_merge($defaults, $attributes);
    }

    /**
     * AJAX handler for debugging API response
     */
    public function ajax_debug_api_response() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }

        $limit = intval($_POST['limit'] ?? 5);
        $start = intval($_POST['start'] ?? 0);

        // Get raw API response
        $customers_response = $this->get_customers($limit, $start);

        if (is_wp_error($customers_response)) {
            wp_send_json_error($customers_response->get_error_message());
        }

        // Return debug information
        $debug_info = array(
            'response_type' => gettype($customers_response),
            'response_keys' => is_array($customers_response) ? array_keys($customers_response) : 'Not an array',
            'total_records' => is_array($customers_response) ? count($customers_response) : 0,
            'sample_data' => array()
        );

        // Get sample data from different possible structures
        if (isset($customers_response['data']) && is_array($customers_response['data'])) {
            $debug_info['structure_type'] = 'JSON API (data key)';
            $debug_info['data_count'] = count($customers_response['data']);
            $debug_info['sample_data'] = array_slice($customers_response['data'], 0, 2);
        } elseif (isset($customers_response['aaData']) && is_array($customers_response['aaData'])) {
            $debug_info['structure_type'] = 'DataTables (aaData key)';
            $debug_info['data_count'] = count($customers_response['aaData']);
            $debug_info['sample_data'] = array_slice($customers_response['aaData'], 0, 2);
        } elseif (is_array($customers_response)) {
            $debug_info['structure_type'] = 'Direct Array';
            $debug_info['data_count'] = count($customers_response);
            $debug_info['sample_data'] = array_slice($customers_response, 0, 2);
        } else {
            $debug_info['structure_type'] = 'Unknown';
            $debug_info['raw_response'] = $customers_response;
        }

        wp_send_json_success(array(
            'message' => __('API response debug informacije.', 'wings-b2b-customer-manager'),
            'debug_info' => $debug_info
        ));
    }
}
