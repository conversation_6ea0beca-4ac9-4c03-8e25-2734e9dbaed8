<?php
/**
 * B2B User Manager Class
 * Manages B2B customer functionality and permissions
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_User_Manager {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_login', array($this, 'redirect_b2b_customer_after_login'), 10, 2);
        add_filter('woocommerce_prevent_admin_access', array($this, 'prevent_b2b_admin_access'));
        add_action('template_redirect', array($this, 'redirect_b2b_dashboard'));
        add_shortcode('wings_b2b_dashboard', array($this, 'render_b2b_dashboard'));
        add_shortcode('wings_customer_info', array($this, 'render_customer_info'));
    }
    
    public function init() {
        // Add custom endpoints
        add_rewrite_endpoint('b2b-dashboard', EP_ROOT | EP_PAGES);
        add_rewrite_endpoint('order-history', EP_ROOT | EP_PAGES);
        add_rewrite_endpoint('customer-profile', EP_ROOT | EP_PAGES);
    }
    
    /**
     * Redirect B2B customers after login
     */
    public function redirect_b2b_customer_after_login($user_login, $user) {
        if (in_array('b2b_customer', $user->roles)) {
            wp_redirect(home_url('/b2b-dashboard/'));
            exit;
        }
    }
    
    /**
     * Prevent B2B customers from accessing admin
     */
    public function prevent_b2b_admin_access($prevent_access) {
        if (current_user_can('b2b_customer') && !current_user_can('manage_options')) {
            return true;
        }
        return $prevent_access;
    }
    
    /**
     * Handle B2B dashboard redirects
     */
    public function redirect_b2b_dashboard() {
        global $wp_query;
        
        if (isset($wp_query->query_vars['b2b-dashboard'])) {
            $this->load_b2b_dashboard_template();
            exit;
        }
        
        if (isset($wp_query->query_vars['order-history'])) {
            $this->load_order_history_template();
            exit;
        }
        
        if (isset($wp_query->query_vars['customer-profile'])) {
            $this->load_customer_profile_template();
            exit;
        }
    }
    
    /**
     * Load B2B dashboard template
     */
    private function load_b2b_dashboard_template() {
        if (!is_user_logged_in() || !current_user_can('read')) {
            wp_redirect(wp_login_url());
            exit;
        }
        
        $template_path = WINGS_B2B_PLUGIN_DIR . 'templates/customer-dashboard.php';
        
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            // Fallback content
            get_header();
            echo '<div class="wings-b2b-dashboard">';
            echo $this->render_b2b_dashboard();
            echo '</div>';
            get_footer();
        }
    }
    
    /**
     * Load order history template
     */
    private function load_order_history_template() {
        if (!is_user_logged_in() || !current_user_can('read')) {
            wp_redirect(wp_login_url());
            exit;
        }
        
        $template_path = WINGS_B2B_PLUGIN_DIR . 'templates/order-history.php';
        
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            // Fallback content
            get_header();
            echo '<div class="wings-order-history">';
            $order_history = Wings_B2B_Order_History::get_instance();
            echo $order_history->render_order_history();
            echo '</div>';
            get_footer();
        }
    }
    
    /**
     * Load customer profile template
     */
    private function load_customer_profile_template() {
        if (!is_user_logged_in() || !current_user_can('read')) {
            wp_redirect(wp_login_url());
            exit;
        }
        
        get_header();
        echo '<div class="wings-customer-profile">';
        echo $this->render_customer_profile();
        echo '</div>';
        get_footer();
    }
    
    /**
     * Render B2B dashboard shortcode
     */
    public function render_b2b_dashboard($atts = array()) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Morate biti prijavljeni da biste pristupili ovoj stranici.', 'wings-b2b-customer-manager') . '</p>';
        }
        
        $current_user = wp_get_current_user();
        $customer_data = $this->get_customer_data($current_user->ID);
        
        ob_start();
        ?>
        <div class="wings-b2b-dashboard">
            <h2><?php _e('B2B Dashboard', 'wings-b2b-customer-manager'); ?></h2>
            
            <div class="dashboard-welcome">
                <h3><?php printf(__('Dobrodošli, %s', 'wings-b2b-customer-manager'), $current_user->display_name); ?></h3>
                
                <?php if ($this->has_placeholder_email($current_user->ID)): ?>
                <div class="notice notice-warning">
                    <p><?php _e('Vaša email adresa je privremena. Molimo vas da je ažurirate u vašem profilu.', 'wings-b2b-customer-manager'); ?></p>
                    <a href="/customer-profile/" class="button"><?php _e('Ažuriraj profil', 'wings-b2b-customer-manager'); ?></a>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h4><?php _e('Informacije o firmi', 'wings-b2b-customer-manager'); ?></h4>
                    <?php echo $this->render_customer_info(); ?>
                </div>
                
                <div class="dashboard-card">
                    <h4><?php _e('Brze akcije', 'wings-b2b-customer-manager'); ?></h4>
                    <ul class="quick-actions">
                        <li><a href="<?php echo wc_get_page_permalink('shop'); ?>" class="button"><?php _e('Prodavnica', 'wings-b2b-customer-manager'); ?></a></li>
                        <li><a href="/order-history/" class="button"><?php _e('Istorija porudžbina', 'wings-b2b-customer-manager'); ?></a></li>
                        <li><a href="<?php echo wc_get_page_permalink('myaccount'); ?>" class="button"><?php _e('Moj nalog', 'wings-b2b-customer-manager'); ?></a></li>
                        <li><a href="/customer-profile/" class="button"><?php _e('Profil', 'wings-b2b-customer-manager'); ?></a></li>
                    </ul>
                </div>
                
                <div class="dashboard-card">
                    <h4><?php _e('Poslednje porudžbine', 'wings-b2b-customer-manager'); ?></h4>
                    <?php 
                    $order_history = Wings_B2B_Order_History::get_instance();
                    echo $order_history->render_recent_orders(5);
                    ?>
                </div>
                
                <?php if ($customer_data): ?>
                <div class="dashboard-card">
                    <h4><?php _e('B2B informacije', 'wings-b2b-customer-manager'); ?></h4>
                    <ul class="b2b-info">
                        <?php if ($customer_data->rabat > 0): ?>
                        <li><strong><?php _e('Rabat:', 'wings-b2b-customer-manager'); ?></strong> <?php echo number_format($customer_data->rabat, 2); ?>%</li>
                        <?php endif; ?>
                        
                        <?php if ($customer_data->limit_amount > 0): ?>
                        <li><strong><?php _e('Kreditni limit:', 'wings-b2b-customer-manager'); ?></strong> <?php echo wc_price($customer_data->limit_amount); ?></li>
                        <?php endif; ?>
                        
                        <?php if ($customer_data->payment_terms > 0): ?>
                        <li><strong><?php _e('Rok plaćanja:', 'wings-b2b-customer-manager'); ?></strong> <?php echo $customer_data->payment_terms; ?> <?php _e('dana', 'wings-b2b-customer-manager'); ?></li>
                        <?php endif; ?>
                        
                        <?php if (!empty($customer_data->komercijalista)): ?>
                        <li><strong><?php _e('Komercijalista:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html($customer_data->komercijalista); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render customer info shortcode
     */
    public function render_customer_info($atts = array()) {
        if (!is_user_logged_in()) {
            return '';
        }
        
        $current_user = wp_get_current_user();
        
        ob_start();
        ?>
        <div class="customer-info">
            <ul>
                <li><strong><?php _e('Firma:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html(get_user_meta($current_user->ID, 'billing_company', true)); ?></li>
                <li><strong><?php _e('Kontakt:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html(get_user_meta($current_user->ID, 'wings_kontakt', true)); ?></li>
                <li><strong><?php _e('Adresa:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html(get_user_meta($current_user->ID, 'billing_address_1', true)); ?></li>
                <li><strong><?php _e('Mesto:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html(get_user_meta($current_user->ID, 'billing_city', true)); ?></li>
                <li><strong><?php _e('Telefon:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html(get_user_meta($current_user->ID, 'billing_phone', true)); ?></li>
                <li><strong><?php _e('Email:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html($current_user->user_email); ?></li>
                
                <?php 
                $pib = get_user_meta($current_user->ID, 'wings_pib', true);
                if (!empty($pib)): 
                ?>
                <li><strong><?php _e('PIB:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html($pib); ?></li>
                <?php endif; ?>
                
                <?php 
                $mb = get_user_meta($current_user->ID, 'wings_mb', true);
                if (!empty($mb)): 
                ?>
                <li><strong><?php _e('Matični broj:', 'wings-b2b-customer-manager'); ?></strong> <?php echo esc_html($mb); ?></li>
                <?php endif; ?>
            </ul>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render customer profile form
     */
    public function render_customer_profile() {
        $current_user = wp_get_current_user();
        $email_generator = Wings_B2B_Email_Generator::get_instance();
        
        // Handle form submission
        if ($_POST && wp_verify_nonce($_POST['wings_profile_nonce'], 'wings_update_profile')) {
            $this->handle_profile_update($current_user->ID);
        }
        
        ob_start();
        ?>
        <div class="wings-customer-profile">
            <h2><?php _e('Profil kupca', 'wings-b2b-customer-manager'); ?></h2>
            
            <form method="post" class="wings-profile-form">
                <?php wp_nonce_field('wings_update_profile', 'wings_profile_nonce'); ?>
                
                <div class="form-group">
                    <label for="user_email"><?php _e('Email adresa:', 'wings-b2b-customer-manager'); ?></label>
                    <input type="email" id="user_email" name="user_email" value="<?php echo esc_attr($current_user->user_email); ?>" required>
                    <?php if ($email_generator->is_placeholder_email($current_user->user_email)): ?>
                    <small class="form-text text-warning"><?php _e('Ova email adresa je privremena. Molimo vas da je ažurirate.', 'wings-b2b-customer-manager'); ?></small>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="billing_phone"><?php _e('Telefon:', 'wings-b2b-customer-manager'); ?></label>
                    <input type="tel" id="billing_phone" name="billing_phone" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'billing_phone', true)); ?>">
                </div>
                
                <div class="form-group">
                    <label for="wings_mobilni"><?php _e('Mobilni:', 'wings-b2b-customer-manager'); ?></label>
                    <input type="tel" id="wings_mobilni" name="wings_mobilni" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'wings_mobilni', true)); ?>">
                </div>
                
                <div class="form-group">
                    <label for="wings_kontakt"><?php _e('Kontakt osoba:', 'wings-b2b-customer-manager'); ?></label>
                    <input type="text" id="wings_kontakt" name="wings_kontakt" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'wings_kontakt', true)); ?>">
                </div>
                
                <button type="submit" class="button button-primary"><?php _e('Ažuriraj profil', 'wings-b2b-customer-manager'); ?></button>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Handle profile update
     */
    private function handle_profile_update($user_id) {
        $email_generator = Wings_B2B_Email_Generator::get_instance();
        
        // Update email if changed and valid
        if (!empty($_POST['user_email']) && is_email($_POST['user_email'])) {
            $new_email = sanitize_email($_POST['user_email']);
            $current_user = get_user_by('ID', $user_id);
            
            if ($new_email !== $current_user->user_email) {
                if ($email_generator->is_placeholder_email($current_user->user_email)) {
                    $result = $email_generator->update_placeholder_email($user_id, $new_email);
                    if (is_wp_error($result)) {
                        echo '<div class="notice notice-error"><p>' . $result->get_error_message() . '</p></div>';
                    } else {
                        echo '<div class="notice notice-success"><p>' . __('Email adresa je uspešno ažurirana.', 'wings-b2b-customer-manager') . '</p></div>';
                    }
                } else {
                    wp_update_user(array('ID' => $user_id, 'user_email' => $new_email));
                    echo '<div class="notice notice-success"><p>' . __('Email adresa je uspešno ažurirana.', 'wings-b2b-customer-manager') . '</p></div>';
                }
            }
        }
        
        // Update other fields
        if (isset($_POST['billing_phone'])) {
            update_user_meta($user_id, 'billing_phone', sanitize_text_field($_POST['billing_phone']));
        }
        
        if (isset($_POST['wings_mobilni'])) {
            update_user_meta($user_id, 'wings_mobilni', sanitize_text_field($_POST['wings_mobilni']));
        }
        
        if (isset($_POST['wings_kontakt'])) {
            update_user_meta($user_id, 'wings_kontakt', sanitize_text_field($_POST['wings_kontakt']));
        }
    }
    
    /**
     * Get customer data from custom table
     */
    private function get_customer_data($user_id) {
        global $wpdb;
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wings_b2b_customers WHERE user_id = %d",
            $user_id
        ));
    }
    
    /**
     * Check if user has placeholder email
     */
    private function has_placeholder_email($user_id) {
        return get_user_meta($user_id, 'wings_placeholder_email', true);
    }
}
