<?php
/**
 * Wings B2B HPOS Compatibility Helper
 * Handles WooCommerce High-Performance Order Storage compatibility
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_HPOS_Compatibility {
    
    /**
     * Check if HPOS is enabled
     * 
     * @return bool True if HPOS is enabled
     */
    public static function is_hpos_enabled() {
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil')) {
            return \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
        }
        return false;
    }
    
    /**
     * Check if plugin is HPOS compatible
     * 
     * @return bool True if compatible
     */
    public static function is_hpos_compatible() {
        // If HPOS is not enabled, we're compatible
        if (!self::is_hpos_enabled()) {
            return true;
        }

        // If HPOS is enabled, check if we've declared compatibility
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            return \Automattic\WooCommerce\Utilities\FeaturesUtil::feature_is_enabled('custom_order_tables');
        }

        return false;
    }
    
    /**
     * Declare HPOS compatibility
     * 
     * @param string $plugin_file Main plugin file path
     * @param bool $compatible Whether plugin is compatible
     */
    public static function declare_compatibility($plugin_file, $compatible = true) {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', $plugin_file, $compatible);
        }
    }
    
    /**
     * Get comprehensive HPOS status
     * 
     * @return array HPOS status information
     */
    public static function get_hpos_status() {
        $status = array(
            'hpos_enabled' => self::is_hpos_enabled(),
            'hpos_compatible' => self::is_hpos_compatible(),
            'features_util_exists' => class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil'),
            'order_util_exists' => class_exists('\Automattic\WooCommerce\Utilities\OrderUtil'),
            'woocommerce_version' => defined('WC_VERSION') ? WC_VERSION : 'Unknown'
        );

        if ($status['features_util_exists']) {
            $status['custom_order_tables_enabled'] = \Automattic\WooCommerce\Utilities\FeaturesUtil::feature_is_enabled('custom_order_tables');
        }

        return $status;
    }
    
    /**
     * Get order using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @return WC_Order|false Order object or false
     */
    public static function get_order($order_id) {
        if (function_exists('wc_get_order')) {
            return wc_get_order($order_id);
        }
        return false;
    }
    
    /**
     * Get orders using HPOS-compatible method
     * 
     * @param array $args Query arguments
     * @return array Array of order objects
     */
    public static function get_orders($args = array()) {
        if (function_exists('wc_get_orders')) {
            return wc_get_orders($args);
        }
        return array();
    }
    
    /**
     * Get customer orders using HPOS-compatible method
     * 
     * @param int $customer_id Customer ID
     * @param array $args Additional arguments
     * @return array Array of order objects
     */
    public static function get_customer_orders($customer_id, $args = array()) {
        $default_args = array(
            'customer_id' => $customer_id,
            'limit' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $default_args);
        
        return self::get_orders($args);
    }
    
    /**
     * Get order meta using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $meta_key Meta key
     * @param bool $single Return single value
     * @return mixed Meta value
     */
    public static function get_order_meta($order_id, $meta_key, $single = true) {
        $order = self::get_order($order_id);
        
        if ($order) {
            return $order->get_meta($meta_key, $single);
        }
        
        return false;
    }
    
    /**
     * Update order meta using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $meta_key Meta key
     * @param mixed $meta_value Meta value
     * @return bool Success
     */
    public static function update_order_meta($order_id, $meta_key, $meta_value) {
        $order = self::get_order($order_id);
        
        if ($order) {
            $order->update_meta_data($meta_key, $meta_value);
            $order->save();
            return true;
        }
        
        return false;
    }
    
    /**
     * Delete order meta using HPOS-compatible method
     * 
     * @param int $order_id Order ID
     * @param string $meta_key Meta key
     * @return bool Success
     */
    public static function delete_order_meta($order_id, $meta_key) {
        $order = self::get_order($order_id);
        
        if ($order) {
            $order->delete_meta_data($meta_key);
            $order->save();
            return true;
        }
        
        return false;
    }
    
    /**
     * Search orders using HPOS-compatible method
     * 
     * @param string $search_term Search term
     * @param array $args Additional arguments
     * @return array Array of order objects
     */
    public static function search_orders($search_term, $args = array()) {
        $default_args = array(
            'search' => $search_term,
            'limit' => -1
        );
        
        $args = wp_parse_args($args, $default_args);
        
        return self::get_orders($args);
    }
    
    /**
     * Get order count using HPOS-compatible method
     * 
     * @param array $args Query arguments
     * @return int Order count
     */
    public static function get_order_count($args = array()) {
        $args['return'] = 'ids';
        $args['limit'] = -1;
        
        $orders = self::get_orders($args);
        
        return count($orders);
    }
    
    /**
     * Get compatibility recommendations
     * 
     * @return array Recommendations for HPOS compatibility
     */
    public static function get_compatibility_recommendations() {
        $status = self::get_hpos_status();
        $recommendations = array();
        
        if ($status['hpos_enabled'] && !$status['hpos_compatible']) {
            $recommendations[] = array(
                'type' => 'error',
                'message' => __('Plugin is not compatible with HPOS. Declare compatibility or disable HPOS.', 'wings-b2b-customer-manager')
            );
        }
        
        if (!$status['features_util_exists']) {
            $recommendations[] = array(
                'type' => 'warning',
                'message' => __('WooCommerce FeaturesUtil class not found. Update WooCommerce to latest version.', 'wings-b2b-customer-manager')
            );
        }
        
        if (!$status['order_util_exists']) {
            $recommendations[] = array(
                'type' => 'warning',
                'message' => __('WooCommerce OrderUtil class not found. Update WooCommerce to latest version.', 'wings-b2b-customer-manager')
            );
        }
        
        if (empty($recommendations)) {
            $recommendations[] = array(
                'type' => 'success',
                'message' => __('Plugin is HPOS compatible.', 'wings-b2b-customer-manager')
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Display HPOS compatibility notice
     * 
     * @param string $plugin_name Plugin name
     */
    public static function display_compatibility_notice($plugin_name = 'Wings B2B Customer Manager') {
        $status = self::get_hpos_status();
        $recommendations = self::get_compatibility_recommendations();
        
        foreach ($recommendations as $recommendation) {
            $class = 'notice-' . $recommendation['type'];
            if ($recommendation['type'] === 'error') {
                $class = 'notice-error';
            } elseif ($recommendation['type'] === 'warning') {
                $class = 'notice-warning';
            } else {
                $class = 'notice-success';
            }
            
            echo '<div class="notice ' . esc_attr($class) . '"><p>';
            echo '<strong>' . esc_html($plugin_name) . ':</strong> ';
            echo esc_html($recommendation['message']);
            echo '</p></div>';
        }
    }
    
    /**
     * Check if WooCommerce is active and compatible
     * 
     * @return bool True if WooCommerce is active and compatible
     */
    public static function is_woocommerce_compatible() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            return false;
        }
        
        // Check WooCommerce version
        if (defined('WC_VERSION') && version_compare(WC_VERSION, '5.0', '<')) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get WooCommerce compatibility status
     * 
     * @return array Compatibility status
     */
    public static function get_woocommerce_status() {
        return array(
            'woocommerce_active' => class_exists('WooCommerce'),
            'woocommerce_version' => defined('WC_VERSION') ? WC_VERSION : 'Unknown',
            'woocommerce_compatible' => self::is_woocommerce_compatible(),
            'hpos_status' => self::get_hpos_status()
        );
    }
}
