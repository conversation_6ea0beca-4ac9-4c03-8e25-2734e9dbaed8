<?php
/**
 * Order History Class
 * Manages order history display for B2B customers
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_Order_History {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_shortcode('wings_order_history', array($this, 'render_order_history'));
        add_action('wp_ajax_wings_export_orders', array($this, 'ajax_export_orders'));
        add_action('wp_ajax_wings_filter_orders', array($this, 'ajax_filter_orders'));
    }
    
    /**
     * Render order history
     */
    public function render_order_history($atts = array()) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Morate biti prijavljeni da biste pristupili istoriji porudžbina.', 'wings-b2b-customer-manager') . '</p>';
        }
        
        $atts = shortcode_atts(array(
            'limit' => 20,
            'show_filters' => true,
            'show_export' => true
        ), $atts);
        
        $current_user = wp_get_current_user();
        $orders = $this->get_customer_orders($current_user->ID, $atts['limit']);
        
        ob_start();
        ?>
        <div class="wings-order-history">
            <h2><?php _e('Istorija porudžbina', 'wings-b2b-customer-manager'); ?></h2>
            
            <?php if ($atts['show_filters']): ?>
            <div class="order-filters">
                <form method="get" class="filter-form">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="date_from"><?php _e('Od datuma:', 'wings-b2b-customer-manager'); ?></label>
                            <input type="date" id="date_from" name="date_from" value="<?php echo esc_attr($_GET['date_from'] ?? ''); ?>">
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_to"><?php _e('Do datuma:', 'wings-b2b-customer-manager'); ?></label>
                            <input type="date" id="date_to" name="date_to" value="<?php echo esc_attr($_GET['date_to'] ?? ''); ?>">
                        </div>
                        
                        <div class="filter-group">
                            <label for="order_status"><?php _e('Status:', 'wings-b2b-customer-manager'); ?></label>
                            <select id="order_status" name="order_status">
                                <option value=""><?php _e('Svi statusi', 'wings-b2b-customer-manager'); ?></option>
                                <?php foreach (wc_get_order_statuses() as $status => $label): ?>
                                <option value="<?php echo esc_attr($status); ?>" <?php selected($_GET['order_status'] ?? '', $status); ?>>
                                    <?php echo esc_html($label); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <button type="submit" class="button"><?php _e('Filtriraj', 'wings-b2b-customer-manager'); ?></button>
                            <a href="?" class="button"><?php _e('Resetuj', 'wings-b2b-customer-manager'); ?></a>
                        </div>
                    </div>
                </form>
            </div>
            <?php endif; ?>
            
            <?php if ($atts['show_export']): ?>
            <div class="order-actions">
                <button id="export-orders" class="button"><?php _e('Izvezi u CSV', 'wings-b2b-customer-manager'); ?></button>
                <button id="export-orders-pdf" class="button"><?php _e('Izvezi u PDF', 'wings-b2b-customer-manager'); ?></button>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($orders)): ?>
            <div class="orders-table-container">
                <table class="wings-orders-table">
                    <thead>
                        <tr>
                            <th><?php _e('Broj porudžbine', 'wings-b2b-customer-manager'); ?></th>
                            <th><?php _e('Datum', 'wings-b2b-customer-manager'); ?></th>
                            <th><?php _e('Status', 'wings-b2b-customer-manager'); ?></th>
                            <th><?php _e('Ukupno', 'wings-b2b-customer-manager'); ?></th>
                            <th><?php _e('Stavke', 'wings-b2b-customer-manager'); ?></th>
                            <th><?php _e('Akcije', 'wings-b2b-customer-manager'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                        <tr>
                            <td>
                                <strong>#<?php echo $order->get_order_number(); ?></strong>
                            </td>
                            <td>
                                <?php echo $order->get_date_created()->date_i18n(get_option('date_format')); ?>
                            </td>
                            <td>
                                <span class="order-status status-<?php echo esc_attr($order->get_status()); ?>">
                                    <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                                </span>
                            </td>
                            <td>
                                <strong><?php echo $order->get_formatted_order_total(); ?></strong>
                            </td>
                            <td>
                                <?php echo $order->get_item_count(); ?> <?php _e('stavki', 'wings-b2b-customer-manager'); ?>
                            </td>
                            <td>
                                <a href="<?php echo $order->get_view_order_url(); ?>" class="button button-small">
                                    <?php _e('Prikaži', 'wings-b2b-customer-manager'); ?>
                                </a>
                                
                                <?php if ($order->has_status('completed')): ?>
                                <a href="<?php echo wp_nonce_url(add_query_arg('reorder', $order->get_id()), 'woocommerce-reorder'); ?>" class="button button-small">
                                    <?php _e('Ponovi', 'wings-b2b-customer-manager'); ?>
                                </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                        
                        <!-- Order details row (collapsible) -->
                        <tr class="order-details" style="display: none;">
                            <td colspan="6">
                                <div class="order-items">
                                    <h4><?php _e('Stavke porudžbine:', 'wings-b2b-customer-manager'); ?></h4>
                                    <ul>
                                        <?php foreach ($order->get_items() as $item): ?>
                                        <li>
                                            <strong><?php echo $item->get_name(); ?></strong>
                                            - <?php _e('Količina:', 'wings-b2b-customer-manager'); ?> <?php echo $item->get_quantity(); ?>
                                            - <?php echo $order->get_formatted_line_subtotal($item); ?>
                                        </li>
                                        <?php endforeach; ?>
                                    </ul>
                                    
                                    <?php if ($order->get_customer_note()): ?>
                                    <div class="order-note">
                                        <strong><?php _e('Napomena:', 'wings-b2b-customer-manager'); ?></strong>
                                        <p><?php echo esc_html($order->get_customer_note()); ?></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="order-summary">
                <p>
                    <?php printf(
                        __('Prikazano %d od ukupno %d porudžbina', 'wings-b2b-customer-manager'),
                        count($orders),
                        $this->get_customer_orders_count($current_user->ID)
                    ); ?>
                </p>
            </div>
            
            <?php else: ?>
            <div class="no-orders">
                <p><?php _e('Nemate porudžbine.', 'wings-b2b-customer-manager'); ?></p>
                <a href="<?php echo wc_get_page_permalink('shop'); ?>" class="button button-primary">
                    <?php _e('Idite u prodavnicu', 'wings-b2b-customer-manager'); ?>
                </a>
            </div>
            <?php endif; ?>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Toggle order details
            $('.wings-orders-table tbody tr:not(.order-details)').click(function() {
                $(this).next('.order-details').toggle();
            });
            
            // Export functionality
            $('#export-orders').click(function() {
                window.location.href = '<?php echo admin_url('admin-ajax.php'); ?>?action=wings_export_orders&format=csv&nonce=<?php echo wp_create_nonce('wings_export_orders'); ?>';
            });
            
            $('#export-orders-pdf').click(function() {
                window.location.href = '<?php echo admin_url('admin-ajax.php'); ?>?action=wings_export_orders&format=pdf&nonce=<?php echo wp_create_nonce('wings_export_orders'); ?>';
            });
        });
        </script>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render recent orders (for dashboard)
     */
    public function render_recent_orders($limit = 5) {
        if (!is_user_logged_in()) {
            return '';
        }
        
        $current_user = wp_get_current_user();
        $orders = $this->get_customer_orders($current_user->ID, $limit);
        
        if (empty($orders)) {
            return '<p>' . __('Nemate porudžbine.', 'wings-b2b-customer-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <div class="recent-orders">
            <ul class="orders-list">
                <?php foreach ($orders as $order): ?>
                <li class="order-item">
                    <div class="order-info">
                        <strong>#<?php echo $order->get_order_number(); ?></strong>
                        <span class="order-date"><?php echo $order->get_date_created()->date_i18n(get_option('date_format')); ?></span>
                    </div>
                    <div class="order-status">
                        <span class="status-<?php echo esc_attr($order->get_status()); ?>">
                            <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                        </span>
                    </div>
                    <div class="order-total">
                        <?php echo $order->get_formatted_order_total(); ?>
                    </div>
                </li>
                <?php endforeach; ?>
            </ul>
            
            <div class="view-all">
                <a href="/order-history/" class="button"><?php _e('Prikaži sve porudžbine', 'wings-b2b-customer-manager'); ?></a>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get customer orders
     */
    private function get_customer_orders($user_id, $limit = 20) {
        $args = array(
            'customer_id' => $user_id,
            'limit' => $limit,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        // Apply filters from GET parameters
        if (!empty($_GET['date_from'])) {
            $args['date_created'] = '>=' . sanitize_text_field($_GET['date_from']);
        }

        if (!empty($_GET['date_to'])) {
            $date_to = sanitize_text_field($_GET['date_to']) . ' 23:59:59';
            if (isset($args['date_created'])) {
                $args['date_created'] = array(
                    $args['date_created'],
                    '<=' . $date_to
                );
            } else {
                $args['date_created'] = '<=' . $date_to;
            }
        }

        if (!empty($_GET['order_status'])) {
            $args['status'] = sanitize_text_field($_GET['order_status']);
        }

        return Wings_B2B_HPOS_Compatibility::get_orders($args);
    }
    
    /**
     * Get customer orders count
     */
    private function get_customer_orders_count($user_id) {
        $args = array(
            'customer_id' => $user_id,
            'return' => 'ids',
            'limit' => -1
        );

        return Wings_B2B_HPOS_Compatibility::get_order_count($args);
    }
    
    /**
     * AJAX export orders
     */
    public function ajax_export_orders() {
        check_ajax_referer('wings_export_orders', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_die(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }
        
        $format = $_GET['format'] ?? 'csv';
        $current_user = wp_get_current_user();
        $orders = $this->get_customer_orders($current_user->ID, -1);
        
        if ($format === 'csv') {
            $this->export_orders_csv($orders);
        } elseif ($format === 'pdf') {
            $this->export_orders_pdf($orders);
        }
        
        wp_die();
    }
    
    /**
     * Export orders to CSV
     */
    private function export_orders_csv($orders) {
        $filename = 'porudzbine_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        $output = fopen('php://output', 'w');
        
        // CSV header
        fputcsv($output, array(
            'Broj porudžbine',
            'Datum',
            'Status',
            'Ukupno',
            'Stavke',
            'Napomena'
        ));
        
        // CSV data
        foreach ($orders as $order) {
            $items = array();
            foreach ($order->get_items() as $item) {
                $items[] = $item->get_name() . ' (x' . $item->get_quantity() . ')';
            }
            
            fputcsv($output, array(
                $order->get_order_number(),
                $order->get_date_created()->date_i18n('Y-m-d H:i:s'),
                wc_get_order_status_name($order->get_status()),
                $order->get_total(),
                implode(', ', $items),
                $order->get_customer_note()
            ));
        }
        
        fclose($output);
    }
    
    /**
     * Export orders to PDF (basic implementation)
     */
    private function export_orders_pdf($orders) {
        // This would require a PDF library like TCPDF or FPDF
        // For now, we'll create a simple HTML version
        $filename = 'porudzbine_' . date('Y-m-d') . '.html';
        
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        echo '<html><head><meta charset="utf-8"><title>Istorija porudžbina</title></head><body>';
        echo '<h1>Istorija porudžbina</h1>';
        echo '<table border="1" cellpadding="5" cellspacing="0">';
        echo '<tr><th>Broj</th><th>Datum</th><th>Status</th><th>Ukupno</th><th>Stavke</th></tr>';
        
        foreach ($orders as $order) {
            $items = array();
            foreach ($order->get_items() as $item) {
                $items[] = $item->get_name() . ' (x' . $item->get_quantity() . ')';
            }
            
            echo '<tr>';
            echo '<td>' . $order->get_order_number() . '</td>';
            echo '<td>' . $order->get_date_created()->date_i18n('Y-m-d H:i:s') . '</td>';
            echo '<td>' . wc_get_order_status_name($order->get_status()) . '</td>';
            echo '<td>' . $order->get_formatted_order_total() . '</td>';
            echo '<td>' . implode(', ', $items) . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</body></html>';
    }
}
