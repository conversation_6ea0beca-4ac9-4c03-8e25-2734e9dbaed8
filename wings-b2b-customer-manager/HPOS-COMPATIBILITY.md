# Wings B2B Customer Manager - HPOS Compatibility

## 🚨 **HPOS Compatibility Resolved**

### **Problem:**
```
This plugin is incompatible with the enabled WooCommerce feature 'High-Performance order storage', it shouldn't be activated.
```

### **Root Cause:**
WooCommerce's High-Performance Order Storage (HPOS) changes how orders are stored and accessed. Plugins that work with WooCommerce data must explicitly declare HPOS compatibility.

## ✅ **HPOS Compatibility Implementation**

### **1. Plugin Header Declaration**
```php
/**
 * Plugin Name: Wings B2B Customer Manager
 * ...
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Declare HPOS compatibility BEFORE any class definitions
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});
```

### **2. HPOS Compatibility Helper Class**
Created `class-wings-hpos-compatibility.php` with comprehensive HPOS support:

```php
class Wings_B2B_HPOS_Compatibility {
    // Check if HPOS is enabled
    public static function is_hpos_enabled();
    
    // Check if plugin is compatible
    public static function is_hpos_compatible();
    
    // HPOS-compatible order methods
    public static function get_order($order_id);
    public static function get_orders($args);
    public static function get_customer_orders($customer_id, $args);
    public static function get_order_meta($order_id, $meta_key);
    public static function update_order_meta($order_id, $meta_key, $meta_value);
    public static function get_order_count($args);
    
    // Display compatibility notices
    public static function display_compatibility_notice($plugin_name);
}
```

### **3. Main Plugin Integration**
```php
// Check HPOS compatibility during initialization
if (!Wings_B2B_HPOS_Compatibility::is_hpos_compatible()) {
    add_action('admin_notices', array($this, 'hpos_compatibility_notice'));
    // Continue loading but show notice
}
```

## 🔧 **HPOS-Compatible Methods**

### **Order Retrieval:**
```php
// ❌ Old way (not HPOS compatible)
$orders = wc_get_orders($args);

// ✅ New way (HPOS compatible)
$orders = Wings_B2B_HPOS_Compatibility::get_orders($args);
```

### **Customer Orders:**
```php
// ✅ HPOS compatible customer orders
$orders = Wings_B2B_HPOS_Compatibility::get_customer_orders($customer_id, $args);
```

### **Order Count:**
```php
// ❌ Old way
$orders = wc_get_orders($args);
$count = count($orders);

// ✅ New way
$count = Wings_B2B_HPOS_Compatibility::get_order_count($args);
```

### **Order Meta:**
```php
// ✅ HPOS compatible meta operations
$meta = Wings_B2B_HPOS_Compatibility::get_order_meta($order_id, 'meta_key');
Wings_B2B_HPOS_Compatibility::update_order_meta($order_id, 'meta_key', $value);
```

## 📋 **Compatibility Checklist**

### **✅ Implemented:**
- [x] HPOS compatibility declaration
- [x] FeaturesUtil integration
- [x] OrderUtil usage detection
- [x] HPOS-compatible order methods
- [x] Customer order retrieval
- [x] Order count methods
- [x] Compatibility status checking
- [x] Admin notices for incompatibility
- [x] Fallback methods for legacy systems

### **🔄 Updated Classes:**
- [x] `Wings_B2B_Customer_Manager` - Main plugin class
- [x] `Wings_B2B_Order_History` - Order history functionality
- [x] `Wings_B2B_HPOS_Compatibility` - HPOS helper class

## 🚀 **How to Use HPOS Methods**

### **1. Check HPOS Status:**
```php
$status = Wings_B2B_HPOS_Compatibility::get_hpos_status();
echo "HPOS Enabled: " . ($status['hpos_enabled'] ? 'Yes' : 'No');
echo "Plugin Compatible: " . ($status['hpos_compatible'] ? 'Yes' : 'No');
```

### **2. Get Customer Orders:**
```php
// Get customer orders HPOS-compatible way
$orders = Wings_B2B_HPOS_Compatibility::get_customer_orders($customer_id, array(
    'limit' => 20,
    'orderby' => 'date',
    'order' => 'DESC'
));

// Get order count
$count = Wings_B2B_HPOS_Compatibility::get_order_count(array(
    'customer_id' => $customer_id,
    'status' => 'completed'
));
```

### **3. Work with Order Meta:**
```php
// Get meta
$value = Wings_B2B_HPOS_Compatibility::get_order_meta($order_id, 'wings_sync_status');

// Update meta
Wings_B2B_HPOS_Compatibility::update_order_meta($order_id, 'wings_sync_status', 'synced');

// Delete meta
Wings_B2B_HPOS_Compatibility::delete_order_meta($order_id, 'old_meta_key');
```

## ⚠️ **Important Notes**

### **1. Always Declare Compatibility:**
```php
// Add this to EVERY WooCommerce plugin
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});
```

### **2. Use Helper Methods:**
```php
// ✅ Always use helper methods instead of direct WooCommerce functions
$orders = Wings_B2B_HPOS_Compatibility::get_orders($args);

// ✅ Always use wc_get_order() for single orders
$order = wc_get_order($order_id);
```

### **3. Avoid Direct Database Queries:**
```php
// ❌ Don't do this
$wpdb->get_results("SELECT * FROM {$wpdb->posts} WHERE post_type = 'shop_order'");

// ✅ Do this instead
$orders = Wings_B2B_HPOS_Compatibility::get_orders(array('limit' => -1));
```

## 🧪 **Testing HPOS Compatibility**

### **1. Enable HPOS:**
1. Go to WooCommerce > Settings > Advanced > Features
2. Enable "High-performance order storage"
3. Test plugin functionality

### **2. Check Compatibility:**
```php
// Run this test
$status = Wings_B2B_HPOS_Compatibility::get_hpos_status();
var_dump($status);

// Check recommendations
$recommendations = Wings_B2B_HPOS_Compatibility::get_compatibility_recommendations();
foreach ($recommendations as $rec) {
    echo $rec['type'] . ': ' . $rec['message'] . "\n";
}
```

### **3. Test Both Modes:**
- Test with HPOS enabled
- Test with HPOS disabled
- Test order history functionality
- Test customer dashboard
- Test order export features

## 🔄 **Migration Notes**

### **Before HPOS:**
- Orders stored as WordPress posts
- Order meta stored in postmeta table
- Direct post queries worked

### **After HPOS:**
- Orders stored in custom tables
- Order meta stored in custom meta tables
- Must use WooCommerce API methods

### **Our Solution:**
- Helper class abstracts the differences
- Automatic detection of HPOS status
- Fallback methods for legacy systems
- Comprehensive compatibility checking

## 📊 **Performance Benefits**

### **HPOS Advantages:**
- Faster order queries
- Better database performance
- Improved scalability
- Reduced database load

### **Our Implementation:**
- Optimized order retrieval
- Efficient customer order queries
- Smart caching where possible
- Minimal database calls

## 🎯 **Summary**

**Wings B2B Customer Manager** is now fully HPOS compatible:

1. **Full HPOS compatibility declaration**
2. **Comprehensive HPOS helper class**
3. **HPOS-compatible order methods**
4. **Customer order functionality**
5. **Order history and export features**
6. **Compatibility checking and notices**
7. **Future-proof architecture**

**Result**: Plugin will now activate successfully with HPOS enabled and provide optimal performance in both HPOS and legacy modes.

## 🔄 **For Future Development**

**Always remember to:**
1. Declare HPOS compatibility in plugin header
2. Use helper methods for order operations
3. Avoid direct database queries for orders
4. Test with both HPOS enabled and disabled
5. Include compatibility checking in plugin initialization

This ensures all WooCommerce plugins work seamlessly with High-Performance Order Storage! 🚀
