<?php
/**
 * Admin Page Class
 * Manages admin interface for Wings B2B Customer Manager
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wings_B2B_Admin_Page {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('wp_ajax_wings_upload_json', array($this, 'ajax_upload_json'));
        add_action('wp_ajax_wings_prepare_import', array($this, 'ajax_prepare_import'));
        add_action('wp_ajax_wings_import_batch', array($this, 'ajax_import_batch'));
        add_action('wp_ajax_wings_api_debug_response', array($this, 'ajax_debug_api_response'));
        add_action('wp_ajax_wings_create_customer_local', array($this, 'ajax_create_customer_local'));

        // Debug: Log that admin page is initialized
        error_log('Wings B2B Admin Page: Initialized and AJAX actions registered');
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Wings B2B Manager', 'wings-b2b-customer-manager'),
            __('Wings B2B', 'wings-b2b-customer-manager'),
            'manage_options',
            'wings-b2b-manager',
            array($this, 'admin_page'),
            'dashicons-groups',
            30
        );
        
        add_submenu_page(
            'wings-b2b-manager',
            __('Import kupaca', 'wings-b2b-customer-manager'),
            __('Import kupaca', 'wings-b2b-customer-manager'),
            'manage_options',
            'wings-b2b-import',
            array($this, 'import_page')
        );
        

        
        add_submenu_page(
            'wings-b2b-manager',
            __('Kreiraj kupca', 'wings-b2b-customer-manager'),
            __('Kreiraj kupca', 'wings-b2b-customer-manager'),
            'manage_options',
            'wings-b2b-create-customer',
            array($this, 'create_customer_page')
        );

        add_submenu_page(
            'wings-b2b-manager',
            __('Wings API', 'wings-b2b-customer-manager'),
            __('Wings API', 'wings-b2b-customer-manager'),
            'manage_options',
            'wings-b2b-api',
            array($this, 'api_page')
        );

        add_submenu_page(
            'wings-b2b-manager',
            __('Podešavanja', 'wings-b2b-customer-manager'),
            __('Podešavanja', 'wings-b2b-customer-manager'),
            'manage_options',
            'wings-b2b-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        register_setting('wings_b2b_settings', 'wings_b2b_email_domain');
        register_setting('wings_b2b_settings', 'wings_b2b_auto_approve');
        register_setting('wings_b2b_settings', 'wings_b2b_send_welcome_email');
        register_setting('wings_b2b_settings', 'wings_b2b_default_role');
        register_setting('wings_b2b_settings', 'wings_b2b_api_username');
        register_setting('wings_b2b_settings', 'wings_b2b_api_password');
        register_setting('wings_b2b_settings', 'wings_b2b_api_alias');
    }
    
    /**
     * Main admin page with integrated B2B customers
     */
    public function admin_page() {
        $total_customers = $this->get_b2b_customers_count();
        $placeholder_emails = $this->get_placeholder_emails_count();
        $recent_imports = $this->get_recent_imports();

        // Handle pagination and search for customers section
        $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
        $current_page = isset($_GET['paged']) ? intval($_GET['paged']) : 1;
        $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

        $customers_data = $this->get_b2b_customers_paginated($per_page, $current_page, $search);
        $customers = $customers_data['customers'];
        $total_customers_paginated = $customers_data['total'];
        $total_pages = ceil($total_customers_paginated / $per_page);

        ?>
        <div class="wrap">
            <h1><?php _e('Wings B2B Customer Manager', 'wings-b2b-customer-manager'); ?></h1>

            <div class="wings-admin-dashboard">
                <div class="dashboard-widgets">
                    <div class="dashboard-widget">
                        <h3><?php _e('Ukupno B2B kupaca', 'wings-b2b-customer-manager'); ?></h3>
                        <div class="widget-content">
                            <span class="big-number"><?php echo $total_customers; ?></span>
                        </div>
                    </div>

                    <div class="dashboard-widget">
                        <h3><?php _e('Privremeni email-ovi', 'wings-b2b-customer-manager'); ?></h3>
                        <div class="widget-content">
                            <span class="big-number warning"><?php echo $placeholder_emails; ?></span>
                            <?php if ($placeholder_emails > 0): ?>
                            <p><a href="#customers-section"><?php _e('Prikaži kupce', 'wings-b2b-customer-manager'); ?></a></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="dashboard-widget">
                        <h3><?php _e('Brze akcije', 'wings-b2b-customer-manager'); ?></h3>
                        <div class="widget-content">
                            <p><a href="<?php echo admin_url('admin.php?page=wings-b2b-import'); ?>" class="button button-primary"><?php _e('Import kupaca', 'wings-b2b-customer-manager'); ?></a></p>
                            <p><a href="<?php echo admin_url('admin.php?page=wings-b2b-create-customer'); ?>" class="button"><?php _e('Kreiraj kupca', 'wings-b2b-customer-manager'); ?></a></p>
                            <p><a href="<?php echo admin_url('admin.php?page=wings-b2b-api'); ?>" class="button"><?php _e('Wings API', 'wings-b2b-customer-manager'); ?></a></p>
                            <p><a href="<?php echo admin_url('admin.php?page=wings-b2b-settings'); ?>" class="button"><?php _e('Podešavanja', 'wings-b2b-customer-manager'); ?></a></p>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($recent_imports)): ?>
                <div class="recent-imports">
                    <h3><?php _e('Poslednji import-ovi', 'wings-b2b-customer-manager'); ?></h3>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Datum', 'wings-b2b-customer-manager'); ?></th>
                                <th><?php _e('Fajl', 'wings-b2b-customer-manager'); ?></th>
                                <th><?php _e('Importovano', 'wings-b2b-customer-manager'); ?></th>
                                <th><?php _e('Ažurirano', 'wings-b2b-customer-manager'); ?></th>
                                <th><?php _e('Status', 'wings-b2b-customer-manager'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_imports as $import): ?>
                            <tr>
                                <td><?php echo esc_html($import->import_date); ?></td>
                                <td><?php echo esc_html($import->filename); ?></td>
                                <td><?php echo esc_html($import->imported_count); ?></td>
                                <td><?php echo esc_html($import->updated_count); ?></td>
                                <td>
                                    <span class="status-<?php echo esc_attr($import->status); ?>">
                                        <?php echo esc_html($import->status); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>

                <!-- B2B Customers Section -->
                <div id="customers-section" class="dashboard-customers">
                    <h2><?php _e('B2B kupci', 'wings-b2b-customer-manager'); ?></h2>

                    <!-- Search and filters -->
                    <div class="tablenav top">
                        <div class="alignleft actions">
                            <form method="get" style="display: inline-block;">
                                <input type="hidden" name="page" value="wings-b2b-manager">
                                <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Pretraži po imenu ili firmi...', 'wings-b2b-customer-manager'); ?>" style="width: 250px;">
                                <input type="submit" class="button" value="<?php _e('Pretraži', 'wings-b2b-customer-manager'); ?>">
                                <?php if ($search): ?>
                                <a href="<?php echo admin_url('admin.php?page=wings-b2b-manager'); ?>" class="button"><?php _e('Očisti', 'wings-b2b-customer-manager'); ?></a>
                                <?php endif; ?>
                            </form>
                        </div>

                        <div class="alignright actions">
                            <form method="get" style="display: inline-block; margin-right: 10px;">
                                <input type="hidden" name="page" value="wings-b2b-manager">
                                <?php if ($search): ?><input type="hidden" name="s" value="<?php echo esc_attr($search); ?>"><?php endif; ?>
                                <select name="per_page" onchange="this.form.submit()">
                                    <option value="10" <?php selected($per_page, 10); ?>>10 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                                    <option value="20" <?php selected($per_page, 20); ?>>20 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                                    <option value="50" <?php selected($per_page, 50); ?>>50 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                                    <option value="100" <?php selected($per_page, 100); ?>>100 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                                </select>
                            </form>
                        </div>
                    </div>

                    <!-- Customers Table -->
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th scope="col" style="width: 50px;"><?php _e('ID', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Korisnik', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Firma', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Email', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Telefon', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Wings šifra', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Rabat', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Status', 'wings-b2b-customer-manager'); ?></th>
                                <th scope="col"><?php _e('Akcije', 'wings-b2b-customer-manager'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($customers)): ?>
                                <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td><?php echo $customer->ID; ?></td>
                                    <td>
                                        <strong><?php echo esc_html($customer->display_name); ?></strong><br>
                                        <small><?php echo esc_html($customer->user_login); ?></small>
                                    </td>
                                    <td><?php echo esc_html(get_user_meta($customer->ID, 'billing_company', true)); ?></td>
                                    <td>
                                        <?php echo esc_html($customer->user_email); ?>
                                        <?php if (get_user_meta($customer->ID, 'wings_placeholder_email', true)): ?>
                                        <br><span class="placeholder-email-badge"><?php _e('Privremeni', 'wings-b2b-customer-manager'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo esc_html(get_user_meta($customer->ID, 'billing_phone', true)); ?></td>
                                    <td><?php echo esc_html(get_user_meta($customer->ID, 'wings_sifra', true)); ?></td>
                                    <td><?php echo esc_html(get_user_meta($customer->ID, 'wings_rabat', true)); ?>%</td>
                                    <td>
                                        <span class="status-<?php echo esc_attr(get_user_meta($customer->ID, 'wings_status', true)); ?>">
                                            <?php echo esc_html(get_user_meta($customer->ID, 'wings_status', true)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?php echo get_edit_user_link($customer->ID); ?>" class="button button-small">
                                            <?php _e('Uredi', 'wings-b2b-customer-manager'); ?>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="9">
                                    <?php if ($search): ?>
                                        <?php printf(__('Nema rezultata za "%s".', 'wings-b2b-customer-manager'), esc_html($search)); ?>
                                    <?php else: ?>
                                        <?php _e('Nema B2B kupaca.', 'wings-b2b-customer-manager'); ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <?php if ($total_customers_paginated > $per_page): ?>
                    <div class="tablenav bottom">
                        <div class="alignleft actions">
                            <span class="displaying-num">
                                <?php printf(__('%s stavki', 'wings-b2b-customer-manager'), number_format_i18n($total_customers_paginated)); ?>
                            </span>
                        </div>

                        <div class="tablenav-pages">
                            <?php
                            $pagination_args = array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => '&laquo; ' . __('Prethodna', 'wings-b2b-customer-manager'),
                                'next_text' => __('Sledeća', 'wings-b2b-customer-manager') . ' &raquo;',
                                'total' => $total_pages,
                                'current' => $current_page,
                                'show_all' => false,
                                'end_size' => 1,
                                'mid_size' => 2,
                                'type' => 'plain',
                                'add_args' => array(
                                    'per_page' => $per_page,
                                    's' => $search
                                )
                            );

                            echo paginate_links($pagination_args);
                            ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <style>
            .wings-admin-dashboard {
                max-width: 1200px;
            }

            .dashboard-widgets {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }

            .dashboard-widget {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .dashboard-widget h3 {
                margin: 0 0 10px 0;
                color: #333;
            }

            .big-number {
                font-size: 2.5em;
                font-weight: bold;
                color: #0073aa;
                display: block;
                margin: 10px 0;
            }

            .big-number.warning {
                color: #d63638;
            }

            .dashboard-widget p {
                margin: 5px 0;
                color: #666;
                font-size: 0.9em;
            }

            .recent-imports {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                margin-bottom: 30px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .recent-imports h3 {
                margin-top: 0;
                color: #333;
            }

            .dashboard-customers {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .dashboard-customers h2 {
                margin-top: 0;
                margin-bottom: 20px;
                color: #333;
            }

            .placeholder-email-badge {
                background: #ffba00;
                color: #fff;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 0.8em;
            }

            .tablenav-pages {
                float: right;
            }

            .tablenav-pages .page-numbers {
                display: inline-block;
                padding: 3px 5px;
                margin: 0 2px;
                text-decoration: none;
                border: 1px solid #ddd;
                background: #f7f7f7;
            }

            .tablenav-pages .page-numbers.current {
                background: #0073aa;
                color: #fff;
                border-color: #0073aa;
            }

            .tablenav-pages .page-numbers:hover {
                background: #0073aa;
                color: #fff;
                border-color: #0073aa;
            }
            </style>
        </div>
        <?php
    }
    
    /**
     * Import page
     */
    public function import_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Import kupaca iz Wings Portal-a', 'wings-b2b-customer-manager'); ?></h1>
            
            <div class="wings-import-page">
                <div class="import-form-container">
                    <form id="wings-import-form" method="post" enctype="multipart/form-data">
                        <?php wp_nonce_field('wings_import_customers', 'wings_import_nonce'); ?>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="json_file"><?php _e('JSON fajl', 'wings-b2b-customer-manager'); ?></label>
                                </th>
                                <td>
                                    <input type="file" id="json_file" name="json_file" accept=".json" required>
                                    <p class="description"><?php _e('Odaberite JSON fajl sa podacima kupaca iz Wings Portal-a.', 'wings-b2b-customer-manager'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="import_mode"><?php _e('Način import-a', 'wings-b2b-customer-manager'); ?></label>
                                </th>
                                <td>
                                    <select id="import_mode" name="import_mode">
                                        <option value="create_update"><?php _e('Kreiraj nove i ažuriraj postojeće', 'wings-b2b-customer-manager'); ?></option>
                                        <option value="create_only"><?php _e('Kreiraj samo nove kupce', 'wings-b2b-customer-manager'); ?></option>
                                        <option value="update_only"><?php _e('Ažuriraj samo postojeće', 'wings-b2b-customer-manager'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="send_welcome_email"><?php _e('Pošalji welcome email', 'wings-b2b-customer-manager'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" id="send_welcome_email" name="send_welcome_email" value="1">
                                    <p class="description"><?php _e('Pošalji welcome email novim kupcima sa podacima za prijavu.', 'wings-b2b-customer-manager'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <p class="submit">
                            <button type="submit" class="button button-primary" id="import-submit">
                                <?php _e('Pokreni import', 'wings-b2b-customer-manager'); ?>
                            </button>
                        </p>
                    </form>
                </div>
                
                <div id="import-progress" style="display: none;">
                    <h3><?php _e('Import u toku...', 'wings-b2b-customer-manager'); ?></h3>
                    <div class="progress-bar" style="width: 100%; background: #f0f0f0; border-radius: 4px; overflow: hidden; margin: 10px 0;">
                        <div class="progress-fill" style="width: 0%; background: #0073aa; height: 20px; transition: width 0.3s;"></div>
                    </div>
                    <div id="import-status"></div>
                </div>
                
                <div id="import-results" style="display: none;">
                    <h3><?php _e('Rezultati import-a', 'wings-b2b-customer-manager'); ?></h3>
                    <div id="results-content"></div>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var importData = {
                filePath: '',
                totalCustomers: 0,
                totalBatches: 0,
                currentBatch: 0,
                batchSize: 50,
                totalImported: 0,
                totalUpdated: 0,
                totalSkipped: 0,
                totalErrors: []
            };

            $('#wings-import-form').on('submit', function(e) {
                e.preventDefault();

                var formData = new FormData(this);
                formData.append('action', 'wings_upload_json');

                $('#import-submit').prop('disabled', true);
                $('#import-progress').show();
                $('#import-results').hide();

                // Step 1: Upload and analyze file
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // File uploaded successfully, now analyze
                            analyzeFile(response.data.file_path);
                        } else {
                            showError(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        showError('<?php _e("Greška pri upload-u fajla:", "wings-b2b-customer-manager"); ?> ' + error);
                    }
                });
            });

            function analyzeFile(filePath) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wings_prepare_import',
                        json_file: filePath,
                        wings_import_nonce: $('[name="wings_import_nonce"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Setup import data
                            importData.filePath = filePath;
                            importData.totalCustomers = response.data.total_customers;
                            importData.totalBatches = response.data.total_batches;
                            importData.batchSize = response.data.batch_size;

                            // Show analysis results
                            showAnalysis(response.data);

                            // Start batch import
                            setTimeout(function() {
                                startBatchImport();
                            }, 2000);
                        } else {
                            showError(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        showError('<?php _e("Greška pri analizi fajla:", "wings-b2b-customer-manager"); ?> ' + error);
                    }
                });
            }

            function showAnalysis(data) {
                var html = '<div class="notice notice-info"><p>' +
                    '<?php _e("Fajl analiziran uspešno!", "wings-b2b-customer-manager"); ?>' +
                    '</p></div>' +
                    '<ul>' +
                    '<li><?php _e("Ukupno kupaca:", "wings-b2b-customer-manager"); ?> ' + data.total_customers + '</li>' +
                    '<li><?php _e("Broj batch-eva:", "wings-b2b-customer-manager"); ?> ' + data.total_batches + '</li>' +
                    '<li><?php _e("Veličina batch-a:", "wings-b2b-customer-manager"); ?> ' + data.batch_size + '</li>' +
                    '<li><?php _e("Procenjeno vreme:", "wings-b2b-customer-manager"); ?> ' + Math.round(data.estimated_time / 60) + ' <?php _e("minuta", "wings-b2b-customer-manager"); ?></li>' +
                    '</ul>' +
                    '<p><?php _e("Počinje batch import...", "wings-b2b-customer-manager"); ?></p>';

                $('#import-status').html(html);
            }

            function startBatchImport() {
                importData.currentBatch = 0;
                processBatch();
            }

            function processBatch() {
                var batchStart = importData.currentBatch * importData.batchSize;

                updateProgress();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wings_import_batch',
                        json_file: importData.filePath,
                        batch_start: batchStart,
                        batch_size: importData.batchSize,
                        wings_import_nonce: $('[name="wings_import_nonce"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update totals
                            importData.totalImported += response.data.imported;
                            importData.totalUpdated += response.data.updated;
                            importData.totalSkipped += response.data.skipped;
                            if (response.data.errors) {
                                importData.totalErrors = importData.totalErrors.concat(response.data.errors);
                            }

                            importData.currentBatch++;

                            if (response.data.is_complete) {
                                // Import complete
                                showFinalResults();
                            } else {
                                // Process next batch
                                setTimeout(function() {
                                    processBatch();
                                }, 1000); // 1 second delay between batches
                            }
                        } else {
                            showError(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        showError('<?php _e("Greška u batch-u:", "wings-b2b-customer-manager"); ?> ' + error);
                    }
                });
            }

            function updateProgress() {
                var progress = Math.round((importData.currentBatch / importData.totalBatches) * 100);
                var html = '<p><?php _e("Obrađuje se batch", "wings-b2b-customer-manager"); ?> ' + (importData.currentBatch + 1) + ' <?php _e("od", "wings-b2b-customer-manager"); ?> ' + importData.totalBatches + '</p>' +
                    '<div class="progress-bar" style="width: 100%; background: #f0f0f0; border-radius: 4px; overflow: hidden;">' +
                    '<div class="progress-fill" style="width: ' + progress + '%; background: #0073aa; height: 20px; transition: width 0.3s;"></div>' +
                    '</div>' +
                    '<p>' + progress + '% <?php _e("završeno", "wings-b2b-customer-manager"); ?></p>';

                $('#import-status').html(html);
            }

            function showFinalResults() {
                $('#import-progress').hide();
                $('#import-results').show();

                var html = '<div class="notice notice-success"><p>' +
                    '<?php _e("Import uspešno završen!", "wings-b2b-customer-manager"); ?>' +
                    '</p></div>' +
                    '<ul>' +
                    '<li><?php _e("Ukupno:", "wings-b2b-customer-manager"); ?> ' + importData.totalCustomers + '</li>' +
                    '<li><?php _e("Importovano:", "wings-b2b-customer-manager"); ?> ' + importData.totalImported + '</li>' +
                    '<li><?php _e("Ažurirano:", "wings-b2b-customer-manager"); ?> ' + importData.totalUpdated + '</li>' +
                    '<li><?php _e("Preskočeno:", "wings-b2b-customer-manager"); ?> ' + importData.totalSkipped + '</li>' +
                    '</ul>';

                if (importData.totalErrors.length > 0) {
                    html += '<h4><?php _e("Greške:", "wings-b2b-customer-manager"); ?></h4>' +
                        '<ul class="error-list">' +
                        importData.totalErrors.map(function(error) {
                            return '<li>' + error + '</li>';
                        }).join('') +
                        '</ul>';
                }

                $('#results-content').html(html);
                $('#import-submit').prop('disabled', false);
            }

            function showError(message) {
                $('#import-progress').hide();
                $('#import-results').show();
                $('#results-content').html(
                    '<div class="notice notice-error"><p>' + message + '</p></div>'
                );
                $('#import-submit').prop('disabled', false);
            }
        });
        </script>
        <?php
    }
    
    /**
     * Customers page
     */
    public function customers_page() {
        // Handle pagination and search
        $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
        $current_page = isset($_GET['paged']) ? intval($_GET['paged']) : 1;
        $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

        $customers_data = $this->get_b2b_customers_paginated($per_page, $current_page, $search);
        $customers = $customers_data['customers'];
        $total_customers = $customers_data['total'];
        $total_pages = ceil($total_customers / $per_page);

        ?>
        <div class="wrap">
            <h1><?php _e('B2B kupci', 'wings-b2b-customer-manager'); ?></h1>

            <!-- Search and filters -->
            <div class="tablenav top">
                <div class="alignleft actions">
                    <form method="get" style="display: inline-block;">
                        <input type="hidden" name="page" value="wings-b2b-customers">
                        <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Pretraži po imenu ili firmi...', 'wings-b2b-customer-manager'); ?>" style="width: 250px;">
                        <input type="submit" class="button" value="<?php _e('Pretraži', 'wings-b2b-customer-manager'); ?>">
                        <?php if ($search): ?>
                        <a href="<?php echo admin_url('admin.php?page=wings-b2b-customers'); ?>" class="button"><?php _e('Očisti', 'wings-b2b-customer-manager'); ?></a>
                        <?php endif; ?>
                    </form>
                </div>

                <div class="alignright actions">
                    <form method="get" style="display: inline-block; margin-right: 10px;">
                        <input type="hidden" name="page" value="wings-b2b-customers">
                        <?php if ($search): ?><input type="hidden" name="s" value="<?php echo esc_attr($search); ?>"><?php endif; ?>
                        <select name="per_page" onchange="this.form.submit()">
                            <option value="10" <?php selected($per_page, 10); ?>>10 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                            <option value="20" <?php selected($per_page, 20); ?>>20 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                            <option value="50" <?php selected($per_page, 50); ?>>50 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                            <option value="100" <?php selected($per_page, 100); ?>>100 <?php _e('po stranici', 'wings-b2b-customer-manager'); ?></option>
                        </select>
                    </form>

                    <a href="<?php echo admin_url('admin.php?page=wings-b2b-create-customer'); ?>" class="button button-primary">
                        <?php _e('Kreiraj kupca', 'wings-b2b-customer-manager'); ?>
                    </a>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <td class="manage-column column-cb check-column">
                            <input type="checkbox" id="cb-select-all-1">
                        </td>
                        <th><?php _e('Korisnik', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Firma', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Email', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Telefon', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Wings šifra', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Rabat', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Status', 'wings-b2b-customer-manager'); ?></th>
                        <th><?php _e('Akcije', 'wings-b2b-customer-manager'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($customers)): ?>
                        <?php foreach ($customers as $customer): ?>
                        <tr>
                            <th scope="row" class="check-column">
                                <input type="checkbox" name="customer[]" value="<?php echo $customer->ID; ?>">
                            </th>
                            <td>
                                <strong><?php echo esc_html($customer->display_name); ?></strong><br>
                                <small><?php echo esc_html($customer->user_login); ?></small>
                            </td>
                            <td><?php echo esc_html(get_user_meta($customer->ID, 'billing_company', true)); ?></td>
                            <td>
                                <?php echo esc_html($customer->user_email); ?>
                                <?php if (get_user_meta($customer->ID, 'wings_placeholder_email', true)): ?>
                                <span class="placeholder-email-badge"><?php _e('Privremeni', 'wings-b2b-customer-manager'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html(get_user_meta($customer->ID, 'billing_phone', true)); ?></td>
                            <td><?php echo esc_html(get_user_meta($customer->ID, 'wings_sifra', true)); ?></td>
                            <td><?php echo esc_html(get_user_meta($customer->ID, 'wings_rabat', true)); ?>%</td>
                            <td>
                                <span class="status-<?php echo esc_attr(get_user_meta($customer->ID, 'wings_status', true)); ?>">
                                    <?php echo esc_html(get_user_meta($customer->ID, 'wings_status', true)); ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo get_edit_user_link($customer->ID); ?>" class="button button-small">
                                    <?php _e('Uredi', 'wings-b2b-customer-manager'); ?>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="9">
                            <?php if ($search): ?>
                                <?php printf(__('Nema rezultata za "%s".', 'wings-b2b-customer-manager'), esc_html($search)); ?>
                            <?php else: ?>
                                <?php _e('Nema B2B kupaca.', 'wings-b2b-customer-manager'); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <?php if ($total_customers > $per_page): ?>
            <div class="tablenav bottom">
                <div class="alignleft actions">
                    <span class="displaying-num">
                        <?php printf(__('%s stavki', 'wings-b2b-customer-manager'), number_format_i18n($total_customers)); ?>
                    </span>
                </div>

                <div class="tablenav-pages">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => '&laquo; ' . __('Prethodna', 'wings-b2b-customer-manager'),
                        'next_text' => __('Sledeća', 'wings-b2b-customer-manager') . ' &raquo;',
                        'total' => $total_pages,
                        'current' => $current_page,
                        'show_all' => false,
                        'end_size' => 1,
                        'mid_size' => 2,
                        'type' => 'plain',
                        'add_args' => array(
                            'per_page' => $per_page,
                            's' => $search
                        )
                    );

                    echo paginate_links($pagination_args);
                    ?>
                </div>
            </div>
            <?php endif; ?>
        </div>



        <style>
        .placeholder-email-badge {
            background: #ffba00;
            color: #fff;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-left: 5px;
        }

        .tablenav-pages {
            float: right;
        }

        .tablenav-pages .page-numbers {
            display: inline-block;
            padding: 3px 5px;
            margin: 0 2px;
            text-decoration: none;
            border: 1px solid #ddd;
            background: #f7f7f7;
        }

        .tablenav-pages .page-numbers.current {
            background: #0073aa;
            color: #fff;
            border-color: #0073aa;
        }

        .tablenav-pages .page-numbers:hover {
            background: #0073aa;
            color: #fff;
            border-color: #0073aa;
        }
        </style>
        <?php
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        if (isset($_POST['submit'])) {
            update_option('wings_b2b_email_domain', sanitize_text_field($_POST['wings_b2b_email_domain']));
            update_option('wings_b2b_auto_approve', isset($_POST['wings_b2b_auto_approve']));
            update_option('wings_b2b_send_welcome_email', isset($_POST['wings_b2b_send_welcome_email']));
            update_option('wings_b2b_api_username', sanitize_text_field($_POST['wings_b2b_api_username']));
            update_option('wings_b2b_api_password', sanitize_text_field($_POST['wings_b2b_api_password']));
            update_option('wings_b2b_api_alias', sanitize_text_field($_POST['wings_b2b_api_alias']));

            echo '<div class="notice notice-success"><p>' . __('Podešavanja su sačuvana.', 'wings-b2b-customer-manager') . '</p></div>';
        }

        $email_domain = get_option('wings_b2b_email_domain', 'b2b.placeholder.local');
        $auto_approve = get_option('wings_b2b_auto_approve', true);
        $send_welcome_email = get_option('wings_b2b_send_welcome_email', false);
        $api_username = get_option('wings_b2b_api_username', 'aql');
        $api_password = get_option('wings_b2b_api_password', 'grossaql');
        $api_alias = get_option('wings_b2b_api_alias', 'grossaql');
        
        ?>
        <div class="wrap">
            <h1><?php _e('Wings B2B Podešavanja', 'wings-b2b-customer-manager'); ?></h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('wings_b2b_settings', 'wings_b2b_settings_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="wings_b2b_email_domain"><?php _e('Domen za privremene email-ove', 'wings-b2b-customer-manager'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="wings_b2b_email_domain" name="wings_b2b_email_domain" value="<?php echo esc_attr($email_domain); ?>" class="regular-text">
                            <p class="description"><?php _e('Domen koji će se koristiti za generisanje privremenih email adresa.', 'wings-b2b-customer-manager'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Automatsko odobravanje', 'wings-b2b-customer-manager'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wings_b2b_auto_approve" value="1" <?php checked($auto_approve); ?>>
                                <?php _e('Automatski odobri nove B2B kupce', 'wings-b2b-customer-manager'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Welcome email', 'wings-b2b-customer-manager'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wings_b2b_send_welcome_email" value="1" <?php checked($send_welcome_email); ?>>
                                <?php _e('Pošalji welcome email novim kupcima', 'wings-b2b-customer-manager'); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <h2><?php _e('Wings Portal API Podešavanja', 'wings-b2b-customer-manager'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="wings_b2b_api_username"><?php _e('API Username', 'wings-b2b-customer-manager'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="wings_b2b_api_username" name="wings_b2b_api_username" value="<?php echo esc_attr($api_username); ?>" class="regular-text">
                            <p class="description"><?php _e('Username za Wings Portal API.', 'wings-b2b-customer-manager'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="wings_b2b_api_password"><?php _e('API Password', 'wings-b2b-customer-manager'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="wings_b2b_api_password" name="wings_b2b_api_password" value="<?php echo esc_attr($api_password); ?>" class="regular-text">
                            <p class="description"><?php _e('Password za Wings Portal API.', 'wings-b2b-customer-manager'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="wings_b2b_api_alias"><?php _e('API Alias', 'wings-b2b-customer-manager'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="wings_b2b_api_alias" name="wings_b2b_api_alias" value="<?php echo esc_attr($api_alias); ?>" class="regular-text">
                            <p class="description"><?php _e('Alias za Wings Portal API (npr. grossaql za test).', 'wings-b2b-customer-manager'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Get B2B customers count
     */
    private function get_b2b_customers_count() {
        $users = get_users(array(
            'role' => 'b2b_customer',
            'fields' => 'ID'
        ));
        
        return count($users);
    }
    
    /**
     * Get placeholder emails count
     */
    private function get_placeholder_emails_count() {
        $users = get_users(array(
            'role' => 'b2b_customer',
            'meta_key' => 'wings_placeholder_email',
            'meta_value' => true,
            'fields' => 'ID'
        ));
        
        return count($users);
    }
    
    /**
     * Get B2B customers with pagination and search
     */
    private function get_b2b_customers_paginated($per_page = 20, $current_page = 1, $search = '') {
        $args = array(
            'role' => 'b2b_customer',
            'orderby' => 'registered',
            'order' => 'DESC',
            'number' => $per_page,
            'offset' => ($current_page - 1) * $per_page
        );

        // Add search functionality
        if (!empty($search)) {
            $args['search'] = '*' . $search . '*';
            $args['search_columns'] = array('display_name', 'user_email', 'user_login');

            // Also search in meta fields
            $args['meta_query'] = array(
                'relation' => 'OR',
                array(
                    'key' => 'billing_company',
                    'value' => $search,
                    'compare' => 'LIKE'
                ),
                array(
                    'key' => 'wings_sifra',
                    'value' => $search,
                    'compare' => 'LIKE'
                ),
                array(
                    'key' => 'wings_pib',
                    'value' => $search,
                    'compare' => 'LIKE'
                )
            );
        }

        $customers = get_users($args);

        // Get total count for pagination
        $count_args = $args;
        unset($count_args['number']);
        unset($count_args['offset']);
        $total_customers = count(get_users($count_args));

        return array(
            'customers' => $customers,
            'total' => $total_customers
        );
    }

    /**
     * Get B2B customers (legacy method)
     */
    private function get_b2b_customers() {
        return get_users(array(
            'role' => 'b2b_customer',
            'orderby' => 'registered',
            'order' => 'DESC'
        ));
    }
    
    /**
     * Get recent imports (mock data for now)
     */
    private function get_recent_imports() {
        // This would come from a custom table tracking imports
        return array();
    }
    
    /**
     * AJAX upload JSON handler
     */
    public function ajax_upload_json() {
        // Debug logging
        error_log('Wings B2B: AJAX upload started');
        error_log('Wings B2B: POST data: ' . print_r($_POST, true));
        error_log('Wings B2B: FILES data: ' . print_r($_FILES, true));

        // Check nonce
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            error_log('Wings B2B: Nonce verification failed');
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }

        if (!current_user_can('manage_options')) {
            error_log('Wings B2B: User permission check failed');
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }

        if (!isset($_FILES['json_file'])) {
            wp_send_json_error(__('Nema uploadovanog fajla.', 'wings-b2b-customer-manager'));
        }

        $uploaded_file = $_FILES['json_file'];

        // Validate file type
        $file_extension = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));
        if ($file_extension !== 'json') {
            wp_send_json_error(__('Molimo uploadujte JSON fajl.', 'wings-b2b-customer-manager'));
        }

        // Handle file upload
        $upload_dir = wp_upload_dir();
        $target_dir = $upload_dir['basedir'] . '/wings-b2b-imports/';

        if (!file_exists($target_dir)) {
            wp_mkdir_p($target_dir);
        }

        $target_file = $target_dir . sanitize_file_name($uploaded_file['name']);

        if (move_uploaded_file($uploaded_file['tmp_name'], $target_file)) {
            // Return file path for batch processing
            wp_send_json_success(array(
                'message' => __('Fajl uspešno upload-ovan.', 'wings-b2b-customer-manager'),
                'file_path' => $target_file,
                'file_name' => basename($uploaded_file['name']),
                'file_size' => filesize($target_file)
            ));
        } else {
            wp_send_json_error(__('Greška pri upload-u fajla.', 'wings-b2b-customer-manager'));
        }
    }

    /**
     * AJAX prepare import handler (proxy to importer)
     */
    public function ajax_prepare_import() {
        $importer = Wings_B2B_Customer_Importer::get_instance();
        $importer->ajax_prepare_import();
    }

    /**
     * AJAX import batch handler (proxy to importer)
     */
    public function ajax_import_batch() {
        $importer = Wings_B2B_Customer_Importer::get_instance();
        $importer->ajax_import_batch();
    }

    /**
     * AJAX debug API response handler (proxy to API)
     */
    public function ajax_debug_api_response() {
        $api = Wings_B2B_API::get_instance();
        $api->ajax_debug_api_response();
    }

    /**
     * AJAX handler for creating customer locally
     */
    public function ajax_create_customer_local() {
        if (!wp_verify_nonce($_POST['wings_import_nonce'] ?? '', 'wings_import_customers')) {
            wp_send_json_error(__('Neispravna bezbednosna provera.', 'wings-b2b-customer-manager'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemate dozvolu za ovu akciju.', 'wings-b2b-customer-manager'));
        }

        // Collect customer data from form
        $customer_data = array(
            'sifra' => sanitize_text_field($_POST['sifra'] ?? ''),
            'naziv' => sanitize_text_field($_POST['naziv'] ?? ''),
            'adresa' => sanitize_text_field($_POST['adresa'] ?? ''),
            'mesto' => sanitize_text_field($_POST['mesto'] ?? ''),
            'kontakt' => sanitize_text_field($_POST['kontakt'] ?? ''),
            'telefon' => sanitize_text_field($_POST['telefon'] ?? ''),
            'email' => sanitize_email($_POST['email'] ?? ''),
            'pib' => sanitize_text_field($_POST['pib'] ?? ''),
            'mb' => sanitize_text_field($_POST['mb'] ?? ''),
            'komercijalista' => sanitize_text_field($_POST['komercijalista'] ?? '1'),
            'klasa' => intval($_POST['klasa'] ?? 1),
            'status' => 'K',
            'rabat' => 0.0,
            'limit' => 0.0,
            'rokplacanja' => 0,
            'tolerancija' => 0,
            'fax' => '',
            'mobilni' => '',
            'radnovreme' => '',
            'racun' => ''
        );

        // Validate required fields
        if (empty($customer_data['naziv'])) {
            wp_send_json_error(__('Naziv firme je obavezan.', 'wings-b2b-customer-manager'));
        }

        // Generate sifra if not provided
        if (empty($customer_data['sifra'])) {
            $customer_data['sifra'] = $this->generate_customer_sifra();
        }

        // Create customer array in the format expected by importer
        $customers_array = array(
            array(
                'type' => 'local-kupac-svi',
                'id' => $customer_data['sifra'],
                'attributes' => $customer_data
            )
        );

        // Process using existing importer
        $importer = Wings_B2B_Customer_Importer::get_instance();
        $result = $importer->process_customers($customers_array);

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => __('Kupac uspešno kreiran lokalno!', 'wings-b2b-customer-manager'),
                'customer_id' => $result['imported'] > 0 ? 'new' : 'updated',
                'data' => $result
            ));
        } else {
            wp_send_json_error($result['message'] ?? __('Greška pri kreiranju kupca.', 'wings-b2b-customer-manager'));
        }
    }

    /**
     * Generate unique customer sifra
     */
    private function generate_customer_sifra() {
        global $wpdb;

        // Find highest existing sifra
        $max_sifra = $wpdb->get_var("
            SELECT MAX(CAST(meta_value AS UNSIGNED))
            FROM {$wpdb->usermeta}
            WHERE meta_key = 'wings_sifra'
            AND meta_value REGEXP '^[0-9]+$'
        ");

        if (!$max_sifra) {
            $max_sifra = 90000; // Start from 90000 for manually created customers
        }

        return strval($max_sifra + 1);
    }

    /**
     * Create customer page
     */
    public function create_customer_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Kreiraj novog kupca', 'wings-b2b-customer-manager'); ?></h1>

            <div class="wings-create-customer-page">
                <form id="wings-create-customer-form" method="post">
                    <?php wp_nonce_field('wings_import_customers', 'wings_import_nonce'); ?>

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="sifra"><?php _e('Šifra kupca', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="sifra" name="sifra" class="regular-text">
                                <p class="description"><?php _e('Ostavite prazno za automatsko generisanje.', 'wings-b2b-customer-manager'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="naziv"><?php _e('Naziv firme', 'wings-b2b-customer-manager'); ?> *</label>
                            </th>
                            <td>
                                <input type="text" id="naziv" name="naziv" class="regular-text" required>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="kontakt"><?php _e('Kontakt osoba', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="kontakt" name="kontakt" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="adresa"><?php _e('Adresa', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="adresa" name="adresa" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="mesto"><?php _e('Mesto', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="mesto" name="mesto" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="telefon"><?php _e('Telefon', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="tel" id="telefon" name="telefon" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="email"><?php _e('Email', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="email" id="email" name="email" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="pib"><?php _e('PIB', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="pib" name="pib" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="mb"><?php _e('Matični broj', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="mb" name="mb" class="regular-text">
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="komercijalista"><?php _e('Komercijalista', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="komercijalista" name="komercijalista" class="regular-text" value="1">
                                <p class="description"><?php _e('ID komercijaliste u Wings sistemu.', 'wings-b2b-customer-manager'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="klasa"><?php _e('Klasa kupca', 'wings-b2b-customer-manager'); ?></label>
                            </th>
                            <td>
                                <select id="klasa" name="klasa">
                                    <option value="1">1 - Standardni</option>
                                    <option value="2">2 - VIP</option>
                                    <option value="3">3 - Specijalni</option>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <div class="form-actions">
                        <button type="submit" class="button button-primary" id="create-customer-submit">
                            <?php _e('Kreiraj kupca lokalno', 'wings-b2b-customer-manager'); ?>
                        </button>

                        <button type="button" class="button" id="create-customer-wings">
                            <?php _e('Kreiraj u Wings Portal-u', 'wings-b2b-customer-manager'); ?>
                        </button>
                    </div>
                </form>

                <div id="create-customer-results" style="display: none;">
                    <h3><?php _e('Rezultat', 'wings-b2b-customer-manager'); ?></h3>
                    <div id="create-results-content"></div>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Local customer creation
            $('#wings-create-customer-form').on('submit', function(e) {
                e.preventDefault();

                var formData = $(this).serialize();
                formData += '&action=wings_create_customer_local';

                $('#create-customer-submit').prop('disabled', true);
                $('#create-customer-results').show();
                $('#create-results-content').html('<p><?php _e("Kreira se kupac...", "wings-b2b-customer-manager"); ?></p>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#create-results-content').html(
                                '<div class="notice notice-success"><p>' + response.data.message + '</p></div>' +
                                '<ul>' +
                                '<li><?php _e("Importovano:", "wings-b2b-customer-manager"); ?> ' + response.data.data.imported + '</li>' +
                                '<li><?php _e("Ažurirano:", "wings-b2b-customer-manager"); ?> ' + response.data.data.updated + '</li>' +
                                '</ul>'
                            );
                            $('#wings-create-customer-form')[0].reset();
                        } else {
                            $('#create-results-content').html(
                                '<div class="notice notice-error"><p>' + response.data + '</p></div>'
                            );
                        }
                        $('#create-customer-submit').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        $('#create-results-content').html(
                            '<div class="notice notice-error"><p><?php _e("Greška pri kreiranju kupca:", "wings-b2b-customer-manager"); ?> ' + error + '</p></div>'
                        );
                        $('#create-customer-submit').prop('disabled', false);
                        console.log('AJAX Error:', xhr.responseText);
                    }
                });
            });

            // Wings Portal customer creation
            $('#create-customer-wings').on('click', function() {
                var formData = $('#wings-create-customer-form').serialize();
                formData += '&action=wings_api_create_customer';

                $(this).prop('disabled', true);
                $('#create-customer-results').show();
                $('#create-results-content').html('<p><?php _e("Kreira se kupac u Wings Portal-u...", "wings-b2b-customer-manager"); ?></p>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#create-results-content').html(
                                '<div class="notice notice-success"><p>' + response.data.message + '</p></div>'
                            );
                            $('#wings-create-customer-form')[0].reset();
                        } else {
                            $('#create-results-content').html(
                                '<div class="notice notice-error"><p>' + response.data + '</p></div>'
                            );
                        }
                        $('#create-customer-wings').prop('disabled', false);
                    },
                    error: function() {
                        $('#create-results-content').html(
                            '<div class="notice notice-error"><p><?php _e("Greška pri kreiranju kupca u Wings Portal-u.", "wings-b2b-customer-manager"); ?></p></div>'
                        );
                        $('#create-customer-wings').prop('disabled', false);
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * API page
     */
    public function api_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Wings Portal API', 'wings-b2b-customer-manager'); ?></h1>

            <div class="wings-api-page">
                <div class="api-actions">
                    <h2><?php _e('API Akcije', 'wings-b2b-customer-manager'); ?></h2>

                    <div class="action-card">
                        <h3><?php _e('Test konekcije', 'wings-b2b-customer-manager'); ?></h3>
                        <p><?php _e('Testiraj konekciju sa Wings Portal API.', 'wings-b2b-customer-manager'); ?></p>
                        <button id="test-api-connection" class="button">
                            <?php _e('Testiraj konekciju', 'wings-b2b-customer-manager'); ?>
                        </button>
                        <button id="debug-api-response" class="button" style="margin-left: 10px;">
                            <?php _e('Debug API odgovor', 'wings-b2b-customer-manager'); ?>
                        </button>
                    </div>

                    <div class="action-card">
                        <h3><?php _e('Import kupaca iz API', 'wings-b2b-customer-manager'); ?></h3>
                        <p><?php _e('Direktno importuj kupce iz Wings Portal API-ja.', 'wings-b2b-customer-manager'); ?></p>

                        <form id="api-import-form">
                            <?php wp_nonce_field('wings_import_customers', 'wings_import_nonce'); ?>

                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="api_limit"><?php _e('Broj kupaca', 'wings-b2b-customer-manager'); ?></label>
                                    </th>
                                    <td>
                                        <input type="number" id="api_limit" name="limit" value="1000" min="1" max="5000">
                                        <p class="description"><?php _e('Maksimalno 5000 kupaca po pozivu.', 'wings-b2b-customer-manager'); ?></p>
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row">
                                        <label for="api_start"><?php _e('Početni offset', 'wings-b2b-customer-manager'); ?></label>
                                    </th>
                                    <td>
                                        <input type="number" id="api_start" name="start" value="0" min="0">
                                        <p class="description"><?php _e('Od kog kupca da počne import (0 = početak).', 'wings-b2b-customer-manager'); ?></p>
                                    </td>
                                </tr>
                            </table>

                            <button type="submit" class="button button-primary">
                                <?php _e('Importuj iz API', 'wings-b2b-customer-manager'); ?>
                            </button>
                        </form>
                    </div>
                </div>

                <div id="api-results" style="display: none;">
                    <h3><?php _e('Rezultati', 'wings-b2b-customer-manager'); ?></h3>
                    <div id="api-results-content"></div>
                </div>
            </div>
        </div>

        <style>
        .wings-api-page {
            max-width: 800px;
        }

        .action-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .action-card h3 {
            margin-top: 0;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Test API connection
            $('#test-api-connection').on('click', function() {
                $(this).prop('disabled', true).text('<?php _e("Testira se...", "wings-b2b-customer-manager"); ?>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wings_api_test_connection',
                        wings_import_nonce: $('[name="wings_import_nonce"]').val()
                    },
                    success: function(response) {
                        $('#api-results').show();
                        if (response.success) {
                            $('#api-results-content').html(
                                '<div class="notice notice-success"><p>' + response.data.message + '</p></div>'
                            );
                        } else {
                            $('#api-results-content').html(
                                '<div class="notice notice-error"><p>' + response.data + '</p></div>'
                            );
                        }
                        $('#test-api-connection').prop('disabled', false).text('<?php _e("Testiraj konekciju", "wings-b2b-customer-manager"); ?>');
                    },
                    error: function() {
                        $('#api-results').show();
                        $('#api-results-content').html(
                            '<div class="notice notice-error"><p><?php _e("Greška pri testiranju konekcije.", "wings-b2b-customer-manager"); ?></p></div>'
                        );
                        $('#test-api-connection').prop('disabled', false).text('<?php _e("Testiraj konekciju", "wings-b2b-customer-manager"); ?>');
                    }
                });
            });

            // Debug API response
            $('#debug-api-response').on('click', function() {
                $(this).prop('disabled', true).text('<?php _e("Debug-uje se...", "wings-b2b-customer-manager"); ?>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wings_api_debug_response',
                        limit: 5,
                        start: 0,
                        wings_import_nonce: $('[name="wings_import_nonce"]').val()
                    },
                    success: function(response) {
                        $('#api-results').show();
                        if (response.success) {
                            var debugInfo = response.data.debug_info;
                            var html = '<div class="notice notice-info"><p>' + response.data.message + '</p></div>';
                            html += '<h4>API Response Debug:</h4>';
                            html += '<ul>';
                            html += '<li><strong>Response Type:</strong> ' + debugInfo.response_type + '</li>';
                            html += '<li><strong>Structure Type:</strong> ' + debugInfo.structure_type + '</li>';
                            html += '<li><strong>Data Count:</strong> ' + debugInfo.data_count + '</li>';
                            html += '<li><strong>Response Keys:</strong> ' + JSON.stringify(debugInfo.response_keys) + '</li>';
                            html += '</ul>';
                            html += '<h4>Sample Data:</h4>';
                            html += '<pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">';
                            html += JSON.stringify(debugInfo.sample_data, null, 2);
                            html += '</pre>';

                            $('#api-results-content').html(html);
                        } else {
                            $('#api-results-content').html(
                                '<div class="notice notice-error"><p>' + response.data + '</p></div>'
                            );
                        }
                        $('#debug-api-response').prop('disabled', false).text('<?php _e("Debug API odgovor", "wings-b2b-customer-manager"); ?>');
                    },
                    error: function() {
                        $('#api-results').show();
                        $('#api-results-content').html(
                            '<div class="notice notice-error"><p><?php _e("Greška pri debug-ovanju API odgovora.", "wings-b2b-customer-manager"); ?></p></div>'
                        );
                        $('#debug-api-response').prop('disabled', false).text('<?php _e("Debug API odgovor", "wings-b2b-customer-manager"); ?>');
                    }
                });
            });

            // API import
            $('#api-import-form').on('submit', function(e) {
                e.preventDefault();

                var formData = $(this).serialize();
                formData += '&action=wings_api_import_customers';

                $('#api-results').show();
                $('#api-results-content').html('<p><?php _e("Importuje se iz API...", "wings-b2b-customer-manager"); ?></p>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#api-results-content').html(
                                '<div class="notice notice-success"><p>' +
                                '<?php _e("Import iz API uspešno završen!", "wings-b2b-customer-manager"); ?>' +
                                '</p></div>' +
                                '<ul>' +
                                '<li><?php _e("Ukupno:", "wings-b2b-customer-manager"); ?> ' + response.data.total + '</li>' +
                                '<li><?php _e("Importovano:", "wings-b2b-customer-manager"); ?> ' + response.data.imported + '</li>' +
                                '<li><?php _e("Ažurirano:", "wings-b2b-customer-manager"); ?> ' + response.data.updated + '</li>' +
                                '<li><?php _e("Preskočeno:", "wings-b2b-customer-manager"); ?> ' + response.data.skipped + '</li>' +
                                '</ul>'
                            );
                        } else {
                            $('#api-results-content').html(
                                '<div class="notice notice-error"><p>' + response.data + '</p></div>'
                            );
                        }
                    },
                    error: function() {
                        $('#api-results-content').html(
                            '<div class="notice notice-error"><p><?php _e("Greška pri import-u iz API.", "wings-b2b-customer-manager"); ?></p></div>'
                        );
                    }
                });
            });
        });
        </script>
        <?php
    }


}
