<?php
/**
 * Simple test to check if Wings B2B plugin is working
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo '<h1>Wings B2B Simple Test</h1>';

// Test 1: Check if plugin file exists
echo '<h2>1. Plugin File Check</h2>';
$plugin_file = __DIR__ . '/wings-b2b-customer-manager.php';
if (file_exists($plugin_file)) {
    echo '✓ Plugin file exists<br>';
} else {
    echo '✗ Plugin file missing<br>';
}

// Test 2: Check if plugin is active
echo '<h2>2. Plugin Active Check</h2>';
$active_plugins = get_option('active_plugins');
$plugin_path = 'wings-b2b-customer-manager/wings-b2b-customer-manager.php';
if (in_array($plugin_path, $active_plugins)) {
    echo '✓ Plugin is active<br>';
} else {
    echo '✗ Plugin is not active<br>';
    echo 'Active plugins: <pre>' . print_r($active_plugins, true) . '</pre>';
}

// Test 3: Check if classes exist
echo '<h2>3. Class Existence Check</h2>';
$classes = [
    'Wings_B2B_Customer_Manager',
    'Wings_B2B_Admin_Page',
    'Wings_B2B_Customer_Importer',
    'Wings_B2B_HPOS_Compatibility'
];

foreach ($classes as $class) {
    if (class_exists($class)) {
        echo "✓ $class exists<br>";
    } else {
        echo "✗ $class missing<br>";
    }
}

// Test 4: Check AJAX actions
echo '<h2>4. AJAX Actions Check</h2>';
global $wp_filter;

$ajax_actions = [
    'wp_ajax_wings_upload_json',
    'wp_ajax_wings_import_customers'
];

foreach ($ajax_actions as $action) {
    if (isset($wp_filter[$action]) && !empty($wp_filter[$action]->callbacks)) {
        echo "✓ $action is registered<br>";
    } else {
        echo "✗ $action is not registered<br>";
    }
}

// Test 5: Manual AJAX test
echo '<h2>5. Manual AJAX Test</h2>';
echo '<form method="post">';
echo '<input type="hidden" name="test_ajax" value="1">';
echo '<input type="hidden" name="wings_import_nonce" value="' . wp_create_nonce('wings_import_customers') . '">';
echo '<button type="submit">Test AJAX Handler</button>';
echo '</form>';

if (isset($_POST['test_ajax'])) {
    echo '<h3>AJAX Test Result:</h3>';
    
    // Simulate AJAX call
    $_POST['action'] = 'wings_upload_json';
    
    if (class_exists('Wings_B2B_Admin_Page')) {
        $admin_page = Wings_B2B_Admin_Page::get_instance();
        
        if (method_exists($admin_page, 'ajax_upload_json')) {
            echo '✓ AJAX method exists<br>';
            
            // Try to call it (this might cause errors, but we'll see)
            try {
                ob_start();
                $admin_page->ajax_upload_json();
                $output = ob_get_clean();
                echo 'AJAX output: <pre>' . htmlspecialchars($output) . '</pre>';
            } catch (Exception $e) {
                echo '✗ AJAX method error: ' . $e->getMessage() . '<br>';
            }
        } else {
            echo '✗ AJAX method missing<br>';
        }
    } else {
        echo '✗ Admin page class missing<br>';
    }
}

// Test 6: WordPress environment
echo '<h2>6. WordPress Environment</h2>';
echo 'WordPress Version: ' . get_bloginfo('version') . '<br>';
echo 'WooCommerce Active: ' . (class_exists('WooCommerce') ? 'Yes' : 'No') . '<br>';
echo 'Current User ID: ' . get_current_user_id() . '<br>';
echo 'Is Admin: ' . (is_admin() ? 'Yes' : 'No') . '<br>';
echo 'AJAX URL: ' . admin_url('admin-ajax.php') . '<br>';

// Test 7: File permissions
echo '<h2>7. File Permissions</h2>';
$upload_dir = wp_upload_dir();
$test_dir = $upload_dir['basedir'] . '/wings-b2b-imports/';

if (!file_exists($test_dir)) {
    if (wp_mkdir_p($test_dir)) {
        echo '✓ Upload directory created<br>';
    } else {
        echo '✗ Cannot create upload directory<br>';
    }
} else {
    echo '✓ Upload directory exists<br>';
}

if (is_writable($test_dir)) {
    echo '✓ Upload directory is writable<br>';
} else {
    echo '✗ Upload directory is not writable<br>';
}

// Test 8: Error log check
echo '<h2>8. Recent Error Log</h2>';
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $log_content = file_get_contents($error_log);
    $wings_errors = array_filter(explode("\n", $log_content), function($line) {
        return strpos($line, 'Wings B2B') !== false;
    });
    
    if (!empty($wings_errors)) {
        echo 'Recent Wings B2B errors:<br>';
        echo '<pre>' . htmlspecialchars(implode("\n", array_slice($wings_errors, -10))) . '</pre>';
    } else {
        echo 'No Wings B2B errors found in log<br>';
    }
} else {
    echo 'Error log not accessible<br>';
}

echo '<h2>9. Quick Fix Suggestions</h2>';
echo '<ul>';
echo '<li>Make sure the plugin is activated in WordPress admin</li>';
echo '<li>Check that WooCommerce is installed and active</li>';
echo '<li>Verify file permissions on the plugin directory</li>';
echo '<li>Check WordPress error log for any PHP errors</li>';
echo '<li>Try deactivating and reactivating the plugin</li>';
echo '</ul>';
?>
