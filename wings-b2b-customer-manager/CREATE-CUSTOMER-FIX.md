# Wings B2B Customer Manager - Create Customer Fix

## 🚨 **Problem: "JSON fajl ne postoji" pri kreiranju kupca**

### **Uzrok problema:**
Kada ste pokušali da kreirate kupca preko forme, plugin je pokušavao da koristi JSON import funkcionalnost umesto direktnog kreiranja kupca.

### **Šta se dešavalo:**
```javascript
// POGREŠNO - koristio je JSON import action
formData += '&action=wings_import_customers';

// Ovo je pokušavalo da pronađe JSON fajl koji ne postoji
```

## ✅ **Rešenje implementirano:**

### **1. Novi AJAX Handler**
Kreiran je novi AJAX handler specifično za kreiranje kupaca:
```php
add_action('wp_ajax_wings_create_customer_local', array($this, 'ajax_create_customer_local'));
```

### **2. Ažuriran JavaScript**
```javascript
// ISPRAVNO - koristi novi action za kreiranje kupca
formData += '&action=wings_create_customer_local';
```

### **3. Direktno kreiranje kupca**
Novi handler direktno kreira kupca bez potrebe za JSON fajlom:
```php
public function ajax_create_customer_local() {
    // Validacija podataka
    // Kreiranje customer array-a
    // Korišćenje postojećeg importer-a
    // Vraćanje rezultata
}
```

## 🔧 **Kako sada radi:**

### **Proces kreiranja kupca:**
1. **Popunite formu** sa podacima kupca
2. **Kliknite "Kreiraj kupca lokalno"**
3. **Plugin direktno kreira kupca** bez JSON fajla
4. **Prikazuje rezultate** (importovano/ažurirano)

### **Automatska šifra kupca:**
- Ako ne unesete šifru, automatski se generiše
- Počinje od 90000 za ručno kreirane kupce
- Uvek je jedinstvena

### **Validacija:**
- **Naziv firme** je obavezan
- Ostala polja su opciona
- Email se validira ako je unet

## 📋 **Nova forma struktura:**

```
┌─────────────────────────────────────┐
│ Šifra kupca: [_____________]        │ ← NOVO polje
│ (ostavite prazno za auto)           │
│                                     │
│ Naziv firme: [_____________] *      │
│ Kontakt osoba: [_____________]      │
│ Adresa: [_____________]             │
│ Mesto: [_____________]              │
│ Telefon: [_____________]            │
│ Email: [_____________]              │
│ PIB: [_____________]                │
│ Matični broj: [_____________]       │
│ Komercijalista: [1]                 │
│ Klasa kupca: [Standardni ▼]         │
│                                     │
│ [Kreiraj kupca lokalno]             │
│ [Kreiraj u Wings Portal-u]          │
└─────────────────────────────────────┘
```

## 🎯 **Rezultati kreiranja:**

### **Uspešno kreiranje:**
```
✅ Kupac uspešno kreiran lokalno!
• Importovano: 1
• Ažurirano: 0
```

### **Ažuriranje postojećeg:**
```
✅ Kupac uspešno kreiran lokalno!
• Importovano: 0
• Ažurirano: 1
```

### **Greška:**
```
❌ Naziv firme je obavezan.
```

## 🔄 **Razlika između opcija:**

### **"Kreiraj kupca lokalno"**
- ✅ Kreira kupca u WordPress/WooCommerce
- ✅ Generiše B2B nalog
- ✅ Kreira placeholder email ako je potrebno
- ✅ Dodaje u Wings B2B tabelu
- ❌ NE šalje u Wings Portal

### **"Kreiraj u Wings Portal-u"**
- ✅ Šalje podatke u Wings Portal API
- ✅ Kreira kupca u Wings sistemu
- ❌ NE kreira lokalni WordPress nalog
- ❌ Potrebna API konfiguracija

## 🛠️ **Tehnička implementacija:**

### **Novi AJAX handler:**
```php
public function ajax_create_customer_local() {
    // 1. Validacija nonce i dozvola
    // 2. Sanitizacija input podataka
    // 3. Validacija obaveznih polja
    // 4. Generisanje šifre ako je potrebno
    // 5. Kreiranje customer array-a
    // 6. Korišćenje postojećeg importer-a
    // 7. Vraćanje rezultata
}
```

### **Automatska šifra:**
```php
private function generate_customer_sifra() {
    // Pronalazi najveću postojeću šifru
    // Dodaje 1
    // Počinje od 90000 za ručno kreirane
}
```

### **Data format:**
```php
$customers_array = array(
    array(
        'type' => 'local-kupac-svi',
        'id' => $customer_data['sifra'],
        'attributes' => $customer_data
    )
);
```

## 🚀 **Testiranje:**

### **Test 1: Kreiranje novog kupca**
1. Idite na **Wings B2B → Kreiraj kupca**
2. Unesite samo **"Naziv firme"**
3. Kliknite **"Kreiraj kupca lokalno"**
4. Trebalo bi da vidite: "Kupac uspešno kreiran lokalno!"

### **Test 2: Sa kompletnim podacima**
1. Popunite sve podatke uključujući šifru
2. Kliknite **"Kreiraj kupca lokalno"**
3. Proverite da li je kupac kreiran sa vašom šifrom

### **Test 3: Ažuriranje postojećeg**
1. Unesite šifru postojećeg kupca
2. Promenite neke podatke
3. Kliknite **"Kreiraj kupca lokalno"**
4. Trebalo bi da vidite: "Ažurirano: 1"

## 📊 **Debug informacije:**

### **Ako i dalje imate probleme:**
1. **Proverite browser console** za JavaScript greške
2. **Proverite WordPress error log** za PHP greške
3. **Testirajte sa minimalnim podacima** (samo naziv firme)
4. **Proverite da li je plugin aktiviran**

### **Common issues:**
- **"Naziv firme je obavezan"** - Popunite naziv firme
- **"Neispravna bezbednosna provera"** - Refresh stranicu
- **"Nemate dozvolu"** - Prijavite se kao admin

## 🎉 **Rezultat:**

**Problem je rešen!** Sada možete da kreirate kupce direktno preko forme bez potrebe za JSON fajlovima.

### **Workflow:**
```
Forma → Validacija → Kreiranje → WordPress User → B2B Role → Rezultat
```

**Nema više "JSON fajl ne postoji" grešaka!** ✅
