<?php
/**
 * Debug Wings Portal API Response
 * Test API calls and analyze response structure
 */

// WordPress environment
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo '<h1>Wings Portal API Debug</h1>';

// Test API connection and response structure
$api = Wings_B2B_API::get_instance();

echo '<h2>1. Test Authentication</h2>';
$auth_result = $api->authenticate();
if (is_wp_error($auth_result)) {
    echo '<p style="color: red;">❌ Authentication failed: ' . $auth_result->get_error_message() . '</p>';
} else {
    echo '<p style="color: green;">✅ Authentication successful</p>';
}

echo '<h2>2. Test Customer API Call</h2>';
$customers_response = $api->get_customers(10, 0); // Get first 10 customers

if (is_wp_error($customers_response)) {
    echo '<p style="color: red;">❌ API call failed: ' . $customers_response->get_error_message() . '</p>';
} else {
    echo '<p style="color: green;">✅ API call successful</p>';
    
    echo '<h3>Response Structure Analysis:</h3>';
    echo '<pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">';
    echo 'Response Type: ' . gettype($customers_response) . "\n";
    echo 'Response Keys: ' . print_r(array_keys($customers_response), true) . "\n";
    echo 'Response Sample: ' . print_r(array_slice($customers_response, 0, 2, true), true);
    echo '</pre>';
    
    echo '<h3>Detailed Response (first 2 records):</h3>';
    echo '<pre style="background: #f0f8ff; padding: 10px; border-radius: 4px; max-height: 600px; overflow-y: auto;">';
    print_r(array_slice($customers_response, 0, 2, true));
    echo '</pre>';
    
    // Try to identify the correct data structure
    echo '<h3>Structure Detection:</h3>';
    
    if (isset($customers_response['data']) && is_array($customers_response['data'])) {
        echo '<p>✅ Found "data" key with array</p>';
        $data_sample = array_slice($customers_response['data'], 0, 1);
        echo '<pre>Sample from data: ' . print_r($data_sample, true) . '</pre>';
    }
    
    if (isset($customers_response['aaData']) && is_array($customers_response['aaData'])) {
        echo '<p>✅ Found "aaData" key with array (DataTables format)</p>';
        $data_sample = array_slice($customers_response['aaData'], 0, 1);
        echo '<pre>Sample from aaData: ' . print_r($data_sample, true) . '</pre>';
    }
    
    if (is_array($customers_response) && !isset($customers_response['data']) && !isset($customers_response['aaData'])) {
        echo '<p>✅ Direct array format detected</p>';
        $data_sample = array_slice($customers_response, 0, 1);
        echo '<pre>Sample from direct array: ' . print_r($data_sample, true) . '</pre>';
    }
    
    // Test field mapping
    echo '<h3>Field Mapping Test:</h3>';
    $first_record = null;
    
    if (isset($customers_response['data'][0])) {
        $first_record = $customers_response['data'][0];
    } elseif (isset($customers_response['aaData'][0])) {
        $first_record = $customers_response['aaData'][0];
    } elseif (isset($customers_response[0])) {
        $first_record = $customers_response[0];
    }
    
    if ($first_record) {
        echo '<p>Testing field mapping on first record:</p>';
        echo '<pre>Original record: ' . print_r($first_record, true) . '</pre>';
        
        // Test mapping function
        $reflection = new ReflectionClass($api);
        $method = $reflection->getMethod('map_array_to_attributes');
        $method->setAccessible(true);
        
        if (isset($first_record['attributes'])) {
            echo '<p>Record has attributes structure:</p>';
            echo '<pre>Attributes: ' . print_r($first_record['attributes'], true) . '</pre>';
        } else {
            $mapped = $method->invoke($api, $first_record);
            echo '<p>Mapped attributes:</p>';
            echo '<pre>' . print_r($mapped, true) . '</pre>';
        }
    }
}

echo '<h2>3. Manual API Test</h2>';
echo '<p>Test the API endpoint directly:</p>';

$api_url = 'https://portal.wings.rs/api/v1/grossaql/local.kupac.svi?dLength=5&dStart=0&output=jsonapi';
echo '<p><strong>URL:</strong> <a href="' . $api_url . '" target="_blank">' . $api_url . '</a></p>';

echo '<h2>4. Recommended Actions</h2>';
echo '<ul>';
echo '<li>Check the "Response Structure Analysis" above</li>';
echo '<li>Look for the correct data key (data, aaData, or direct array)</li>';
echo '<li>Verify field mapping in the "Field Mapping Test" section</li>';
echo '<li>If needed, update the API response handling code</li>';
echo '</ul>';

echo '<h2>5. Common API Response Formats</h2>';
echo '<h3>Format 1: JSON API</h3>';
echo '<pre>{
  "data": [
    {
      "type": "local-kupac-svi",
      "id": "12990",
      "attributes": {
        "sifra": "12990",
        "naziv": "ADAM APOTEKA",
        ...
      }
    }
  ]
}</pre>';

echo '<h3>Format 2: DataTables</h3>';
echo '<pre>{
  "draw": 1,
  "recordsTotal": 1838,
  "recordsFiltered": 1838,
  "aaData": [
    ["12990", "ADAM APOTEKA", "Miloša Velikog 110", ...]
  ]
}</pre>';

echo '<h3>Format 3: Direct Array</h3>';
echo '<pre>[
  ["12990", "ADAM APOTEKA", "Miloša Velikog 110", ...],
  ["16265", "DRUGA APOTEKA", "Neka adresa 123", ...]
]</pre>';

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
h2 { color: #0073aa; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
h3 { color: #333; }
</style>';
?>
