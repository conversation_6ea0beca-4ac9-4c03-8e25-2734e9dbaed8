<?php
/**
 * Plugin Name: Wings B2B Customer Manager
 * Plugin URI: https://yoursite.com
 * Description: Upravljanje B2B kupcima iz Wings Portal-a sa mogućnošću importa, generisanja email adresa i pregleda istorije porudž<PERSON>.
 * Version: 1.0.0
 * Author: Your Name
 * Text Domain: wings-b2b-customer-manager
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WINGS_B2B_VERSION', '1.0.0');
define('WINGS_B2B_PLUGIN_FILE', __FILE__);
define('WINGS_B2B_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WINGS_B2B_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WINGS_B2B_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Declare HPOS compatibility BEFORE any class definitions
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});

/**
 * Main Wings B2B Customer Manager Class
 */
class Wings_B2B_Customer_Manager {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        $this->includes();

        // Check HPOS compatibility
        if (!Wings_B2B_HPOS_Compatibility::is_hpos_compatible()) {
            add_action('admin_notices', array($this, 'hpos_compatibility_notice'));
            // Continue loading but show notice
        }

        $this->init_hooks();
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once WINGS_B2B_PLUGIN_DIR . 'includes/class-wings-hpos-compatibility.php';
        require_once WINGS_B2B_PLUGIN_DIR . 'includes/class-wings-api.php';
        require_once WINGS_B2B_PLUGIN_DIR . 'includes/class-email-generator.php';
        require_once WINGS_B2B_PLUGIN_DIR . 'includes/class-customer-importer.php';
        require_once WINGS_B2B_PLUGIN_DIR . 'includes/class-b2b-user-manager.php';
        require_once WINGS_B2B_PLUGIN_DIR . 'includes/class-order-history.php';

        if (is_admin()) {
            require_once WINGS_B2B_PLUGIN_DIR . 'admin/class-admin-page.php';
        }
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize classes
        Wings_B2B_Email_Generator::get_instance();
        Wings_B2B_Customer_Importer::get_instance();
        Wings_B2B_User_Manager::get_instance();
        Wings_B2B_Order_History::get_instance();
        Wings_B2B_API::get_instance();

        if (is_admin()) {
            Wings_B2B_Admin_Page::get_instance();
        }

        // Add custom user role
        add_action('init', array($this, 'add_b2b_customer_role'));

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Debug: Log that plugin is initialized
        error_log('Wings B2B Customer Manager: Plugin initialized successfully');
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('wings-b2b-customer-manager', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create custom user role
        $this->add_b2b_customer_role();
        
        // Create custom database tables if needed
        $this->create_tables();
        
        // Set default options
        add_option('wings_b2b_version', WINGS_B2B_VERSION);
        add_option('wings_b2b_email_domain', 'b2b.placeholder.local');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Add B2B customer role
     */
    public function add_b2b_customer_role() {
        if (!get_role('b2b_customer')) {
            add_role('b2b_customer', __('B2B Customer', 'wings-b2b-customer-manager'), array(
                'read' => true,
                'view_order_history' => true,
                'place_orders' => true,
            ));
        }
    }
    
    /**
     * Create custom database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Table for storing additional B2B customer data
        $table_name = $wpdb->prefix . 'wings_b2b_customers';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            wings_sifra varchar(50) NOT NULL,
            pib varchar(50) DEFAULT '',
            mb varchar(50) DEFAULT '',
            rabat decimal(5,2) DEFAULT 0.00,
            komercijalista varchar(50) DEFAULT '',
            limit_amount decimal(10,2) DEFAULT 0.00,
            payment_terms int(11) DEFAULT 0,
            tolerancija int(11) DEFAULT 0,
            klasa int(11) DEFAULT 0,
            status varchar(10) DEFAULT 'K',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id),
            UNIQUE KEY wings_sifra (wings_sifra)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style('wings-b2b-style', WINGS_B2B_PLUGIN_URL . 'assets/css/style.css', array(), WINGS_B2B_VERSION);
        wp_enqueue_script('wings-b2b-script', WINGS_B2B_PLUGIN_URL . 'assets/js/script.js', array('jquery'), WINGS_B2B_VERSION, true);
        
        // Localize script
        wp_localize_script('wings-b2b-script', 'wings_b2b_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wings_b2b_nonce'),
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'wings-b2b') !== false) {
            wp_enqueue_style('wings-b2b-admin-style', WINGS_B2B_PLUGIN_URL . 'assets/css/style.css', array(), WINGS_B2B_VERSION);
            wp_enqueue_script('wings-b2b-admin-script', WINGS_B2B_PLUGIN_URL . 'assets/js/script.js', array('jquery'), WINGS_B2B_VERSION, true);

            // Localize script for admin
            wp_localize_script('wings-b2b-admin-script', 'wings_b2b_admin_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wings_import_customers'),
                'import_nonce' => wp_create_nonce('wings_import_customers'),
            ));
        }
    }
    
    /**
     * HPOS compatibility notice
     */
    public function hpos_compatibility_notice() {
        Wings_B2B_HPOS_Compatibility::display_compatibility_notice('Wings B2B Customer Manager');
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('Wings B2B Customer Manager zahteva WooCommerce plugin da bude aktivan.', 'wings-b2b-customer-manager');
        echo '</p></div>';
    }
}

// Initialize the plugin
function wings_b2b_customer_manager() {
    return Wings_B2B_Customer_Manager::get_instance();
}

// Start the plugin
wings_b2b_customer_manager();
