# Wings B2B Customer Manager

WordPress plugin za upravljanje B2B kupcima iz Wings Portal-a sa mogućnošću importa, generisanja email adresa i pregleda istorije porudžbina.

## Funkcionalnosti

### 🔧 Osnovne funkcionalnosti
- **Import kupaca iz JSON fajla** - Automatski import kupaca iz Wings Portal-a
- **Wings Portal API integracija** - Direktan import kupaca iz Wings API-ja
- **Kreiranje kupaca** - Kreiranje novih kupaca lokalno ili u Wings Portal-u
- **Generisanje email adresa** - Kreiranje placeholder email adresa za kupce koji nemaju email
- **B2B korisničke uloge** - Specijalna uloga za B2B kupce sa odgovarajućim dozvolama
- **Istorija porudžbina** - Detaljni pregled svih porudžbina sa filterima i export opcijama
- **B2B Dashboard** - Personalizovani dashboard za B2B kupce

### 📊 Upravljanje podacima
- **Mapiranje Wings podataka** - Automatsko mapiranje svih relevantnih podataka iz Wings Portal-a
- **Custom database tabela** - Dodatna tabela za čuvanje B2B specifičnih podataka
- **Meta podaci** - Čuvanje svih Wings podataka kao WordPress user meta
- **Sinhronizacija** - Mogućnost ažuriranja postojećih kupaca
- **Batch processing** - Obrada velikih fajlova po delovima (50 kupaca po batch-u)
- **Progress tracking** - Real-time praćenje napretka import-a

### 🎨 Korisničko iskustvo
- **Responsive design** - Prilagođen svim uređajima
- **Intuitivni interface** - Lako korišćenje za krajnje korisnike
- **Admin panel** - Kompletno upravljanje kroz WordPress admin
- **Shortcode podrška** - Jednostavno uključivanje funkcionalnosti u stranice

## Instalacija

1. **Upload plugin-a**
   ```
   wp-content/plugins/wings-b2b-customer-manager/
   ```

2. **Aktivacija**
   - Idite na WordPress Admin → Plugins
   - Pronađite "Wings B2B Customer Manager"
   - Kliknite "Activate"

3. **Konfiguracija**
   - Idite na Wings B2B → Podešavanja
   - Podesite domen za privremene email adrese
   - Konfigurisite ostale opcije

## Korišćenje

### Import kupaca

1. **Priprema JSON fajla**
   - Izvezite kupce iz Wings Portal-a u JSON format
   - Struktura mora da sadrži `customers` array sa `attributes` objektima

2. **Import proces**
   - Idite na Wings B2B → Import kupaca
   - Odaberite JSON fajl
   - Izaberite način import-a (kreiraj nove, ažuriraj postojeće, ili oba)
   - Pokrenite import

3. **Batch processing** (za velike fajlove)
   - Fajl se automatski analizira
   - Prikazuje se broj batch-eva i procenjeno vreme
   - Import se izvršava po delovima sa progress bar-om
   - Real-time ažuriranje statusa

4. **Rezultati**
   - Plugin će prikazati detaljne rezultate import-a
   - Broj importovanih, ažuriranih i preskočenih kupaca
   - Lista grešaka ako ih ima
   - Ukupno vreme import-a

### Upravljanje kupcima

1. **Pregled kupaca**
   - Wings B2B → B2B kupci
   - Lista svih B2B kupaca sa osnovnim informacijama
   - Filter opcije i bulk akcije

2. **Uređivanje kupaca**
   - Kliknite "Uredi" pored kupca
   - Standardni WordPress user edit screen
   - Dodatni Wings meta podaci

### B2B Dashboard

1. **Automatski redirect**
   - B2B kupci se automatski preusmere na dashboard nakon prijave
   - URL: `/b2b-dashboard/`

2. **Funkcionalnosti dashboard-a**
   - Pregled informacija o firmi
   - Brze akcije (prodavnica, istorija, profil)
   - Poslednje porudžbine
   - B2B specifične informacije (rabat, limit, itd.)

### Istorija porudžbina

1. **Pristup**
   - URL: `/order-history/`
   - Ili preko dashboard-a

2. **Funkcionalnosti**
   - Filter po datumu, statusu
   - Detaljni pregled svake porudžbine
   - Export u CSV i PDF format
   - Mogućnost ponovne porudžbine

## Struktura podataka

### JSON format za import

```json
{
  "export_info": {
    "timestamp": "2025-08-04T22:11:10.811420",
    "total_customers": 1838,
    "source": "Wings Portal API"
  },
  "customers": [
    {
      "type": "local-kupac-svi",
      "id": "12990",
      "attributes": {
        "sifra": "12990",
        "naziv": "'ADAM' APOTEKA",
        "adresa": "Miloša Velikog 110",
        "mesto": "11320 Velika Plana",
        "kontakt": "Ilinka Ljubomirović",
        "telefon": "026-514-778",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "*********",
        "rabat": 0.0,
        "komercijalista": "9",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 60,
        "tolerancija": 0,
        "klasa": 2
      }
    }
  ]
}
```

### Mapiranje podataka

| Wings polje | WordPress polje | Opis |
|-------------|-----------------|------|
| `sifra` | `wings_sifra` | Jedinstvena šifra kupca |
| `naziv` | `billing_company` | Naziv firme |
| `adresa` | `billing_address_1` | Adresa |
| `mesto` | `billing_city` | Mesto |
| `kontakt` | `wings_kontakt` | Kontakt osoba |
| `telefon` | `billing_phone` | Telefon |
| `email` | `user_email` | Email (generiše se ako je prazan) |
| `pib` | `wings_pib` | PIB |
| `mb` | `wings_mb` | Matični broj |
| `rabat` | `wings_rabat` | Rabat u procentima |
| `komercijalista` | `wings_komercijalista` | ID komercijaliste |
| `limit` | `wings_limit` | Kreditni limit |
| `rokplacanja` | `wings_rokplacanja` | Rok plaćanja u danima |

## Email generisanje

### Strategija
- **Format**: `{sifra}@{domen}`
- **Default domen**: `b2b.placeholder.local`
- **Jedinstvost**: Automatska provera i dodavanje timestamp-a ako je potrebno

### Primer
```
<EMAIL>
<EMAIL>
```

### Ažuriranje email-a
- Kupci mogu da ažuriraju email preko profila
- Admin može bulk ažuriranje
- Automatsko označavanje placeholder email-ova

## Shortcode-ovi

### `[wings_b2b_dashboard]`
Prikazuje kompletni B2B dashboard

```php
echo do_shortcode('[wings_b2b_dashboard]');
```

### `[wings_customer_info]`
Prikazuje informacije o trenutno ulogovanom kupcu

```php
echo do_shortcode('[wings_customer_info]');
```

### `[wings_order_history]`
Prikazuje istoriju porudžbina sa filterima

```php
echo do_shortcode('[wings_order_history limit="10" show_filters="true"]');
```

## Hooks i filteri

### Actions
```php
// Pre import-a kupca
do_action('wings_b2b_before_customer_import', $customer_data);

// Posle import-a kupca
do_action('wings_b2b_after_customer_import', $user_id, $customer_data);

// Pre generisanja email-a
do_action('wings_b2b_before_email_generation', $customer_data);
```

### Filters
```php
// Modifikacija email domena
$domain = apply_filters('wings_b2b_email_domain', $domain);

// Modifikacija username-a
$username = apply_filters('wings_b2b_generated_username', $username, $customer_data);

// Modifikacija display name-a
$display_name = apply_filters('wings_b2b_display_name', $display_name, $customer_data);
```

## Baza podataka

### Custom tabela: `wp_wings_b2b_customers`

```sql
CREATE TABLE wp_wings_b2b_customers (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    wings_sifra varchar(50) NOT NULL,
    pib varchar(50) DEFAULT '',
    mb varchar(50) DEFAULT '',
    rabat decimal(5,2) DEFAULT 0.00,
    komercijalista varchar(50) DEFAULT '',
    limit_amount decimal(10,2) DEFAULT 0.00,
    payment_terms int(11) DEFAULT 0,
    tolerancija int(11) DEFAULT 0,
    klasa int(11) DEFAULT 0,
    status varchar(10) DEFAULT 'K',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_id (user_id),
    UNIQUE KEY wings_sifra (wings_sifra)
);
```

## Zavisnosti

### WordPress
- **Minimalna verzija**: 5.0
- **Testirana do**: 6.4
- **PHP**: 7.4+

### Plugin-ovi
- **WooCommerce**: Obavezno (5.0+)
- **HPOS kompatibilnost**: Da (potpuna podrška za High-Performance Order Storage)

## HPOS Kompatibilnost

### High-Performance Order Storage
Plugin je potpuno kompatibilan sa WooCommerce HPOS funkcionalnostima:

```php
// Automatska HPOS kompatibilnost deklaracija
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});
```

### HPOS Helper klasa
- `Wings_B2B_HPOS_Compatibility::get_orders()` - HPOS kompatibilno dohvatanje porudžbina
- `Wings_B2B_HPOS_Compatibility::get_customer_orders()` - Porudžbine kupca
- `Wings_B2B_HPOS_Compatibility::get_order_count()` - Brojanje porudžbina
- `Wings_B2B_HPOS_Compatibility::get_order_meta()` - Meta podaci porudžbina

### Testiranje
- Testirano sa HPOS omogućenim i onemogućenim
- Automatska detekcija HPOS statusa
- Kompatibilnost notice-ovi u admin panel-u

## Bezbednost

### Nonce zaštita
- Svi AJAX pozivi zaštićeni nonce-om
- Form submission validacija

### Capabilities
- `manage_options` za admin funkcionalnosti
- Custom capabilities za B2B kupce

### Sanitizacija
- Svi user input-i sanitizovani
- SQL injection zaštita
- XSS zaštita

## Performanse

### Optimizacije
- Lazy loading template-a
- Conditional script loading
- Database query optimizacija
- Caching friendly

### Monitoring
- Error logging
- Import statistike
- Performance metrics

## Podrška

### Dokumentacija
- Inline komentari u kodu
- PHPDoc standardni komentari
- README fajl

### Debugging
- WP_DEBUG kompatibilnost
- Error logging
- Verbose import rezultati

## Licenca

GPL v2 ili novija

## Changelog

### 1.0.0
- Početna verzija
- Import kupaca iz JSON-a
- B2B dashboard
- Istorija porudžbina
- Email generisanje
- Admin panel
