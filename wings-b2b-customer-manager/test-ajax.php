<?php
/**
 * Test AJAX functionality for Wings B2B Customer Manager
 * This file helps debug AJAX issues
 */

// WordPress environment
require_once('../../../wp-config.php');

// Check if user is logged in and has permissions
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    wp_die('Access denied');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Wings B2B AJAX Test</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
</head>
<body>
    <h1>Wings B2B AJAX Test</h1>
    
    <form id="test-form" enctype="multipart/form-data">
        <p>
            <label for="json_file">Select JSON file:</label>
            <input type="file" id="json_file" name="json_file" accept=".json" required>
        </p>
        
        <p>
            <button type="submit">Test Upload</button>
        </p>
        
        <?php wp_nonce_field('wings_import_customers', 'wings_import_nonce'); ?>
    </form>
    
    <div id="results"></div>
    
    <script>
    jQuery(document).ready(function($) {
        $('#test-form').on('submit', function(e) {
            e.preventDefault();
            
            var formData = new FormData(this);
            formData.append('action', 'wings_upload_json');
            
            $('#results').html('<p>Testing AJAX...</p>');
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#results').html('<h3>Success Response:</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $('#results').html(
                        '<h3>Error Response:</h3>' +
                        '<p><strong>Status:</strong> ' + xhr.status + '</p>' +
                        '<p><strong>Error:</strong> ' + error + '</p>' +
                        '<p><strong>Response:</strong></p>' +
                        '<pre>' + xhr.responseText + '</pre>'
                    );
                }
            });
        });
        
        // Test basic AJAX connectivity
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'heartbeat',
                _wpnonce: '<?php echo wp_create_nonce('heartbeat-nonce'); ?>'
            },
            success: function(response) {
                console.log('Basic AJAX test successful:', response);
            },
            error: function(xhr, status, error) {
                console.log('Basic AJAX test failed:', error);
            }
        });
    });
    </script>
    
    <h2>Debug Information</h2>
    <ul>
        <li><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></li>
        <li><strong>WooCommerce Version:</strong> <?php echo defined('WC_VERSION') ? WC_VERSION : 'Not installed'; ?></li>
        <li><strong>Plugin Active:</strong> <?php echo class_exists('Wings_B2B_Customer_Manager') ? 'Yes' : 'No'; ?></li>
        <li><strong>User Can Manage Options:</strong> <?php echo current_user_can('manage_options') ? 'Yes' : 'No'; ?></li>
        <li><strong>AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?></li>
        <li><strong>Nonce:</strong> <?php echo wp_create_nonce('wings_import_customers'); ?></li>
    </ul>
    
    <h2>Registered AJAX Actions</h2>
    <p>Check if these actions are registered:</p>
    <ul>
        <li>wp_ajax_wings_upload_json</li>
        <li>wp_ajax_wings_import_customers</li>
    </ul>
    
    <h2>Test Steps</h2>
    <ol>
        <li>Select a JSON file</li>
        <li>Click "Test Upload"</li>
        <li>Check the results below</li>
        <li>Check browser console for additional errors</li>
    </ol>
</body>
</html>
