#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wings Portal Customer Download Script
Skida JSON podatke o kupcima sa Wings Portal API-ja
"""

import requests
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any

class WingsCustomerDownloader:
    """Klasa za preuzimanje kupaca sa Wings Portal API-ja"""
    
    def __init__(self, base_url: str = "https://portal.wings.rs", api_alias: str = "gross"):
        """
        Inicijalizuje downloader
        
        Args:
            base_url: Osnovna URL adresa Wings Portal-a
            api_alias: API alias (gross ili grosstest)
        """
        self.base_url = base_url.rstrip('/')
        self.api_alias = api_alias
        self.api_url = f"{self.base_url}/api/v1/{self.api_alias}/"
        self.session = requests.Session()
        self.session_token = None
        
    def login(self, username: str, password: str) -> bool:
        """
        Prijavljuje se na Wings Portal
        
        Args:
            username: <PERSON><PERSON><PERSON><PERSON><PERSON> ime
            password: Lozinka
            
        Returns:
            True ako je prijava uspešna, False inače
        """
        login_url = f"{self.api_url}system.user.log"
        
        # Pokušaj sa JSON formatom
        login_data = {
            "aUn": username,
            "aUp": password
        }
        
        try:
            response = self.session.post(
                login_url,
                json=login_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                # Proveri da li je odgovor JSON
                try:
                    result = response.json()
                    if result.get('success', False):
                        print(f"✅ Uspešna prijava za korisnika: {username}")
                        return True
                except json.JSONDecodeError:
                    pass
                
                # Ako je status 200, verovatno je prijava uspešna
                # čak i ako nema JSON odgovora
                print(f"✅ Uspešna prijava za korisnika: {username}")
                return True
            else:
                print(f"❌ Neuspešna prijava. Status kod: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Greška pri prijavi: {e}")
            return False
    
    def get_customers(self, start: int = 0, length: int = 100, filters: Optional[Dict] = None) -> Optional[Dict]:
        """
        Preuzima kupce sa Wings Portal-a
        
        Args:
            start: Početni indeks (za paginaciju)
            length: Broj kupaca za preuzimanje
            filters: Dodatni filteri (komercijalista, status, search)
            
        Returns:
            Rečnik sa podacima o kupcima ili None ako je greška
        """
        url = f"{self.api_url}local.kupac.svi"
        
        params = {
            'dStart': start,
            'dLength': length,
            'output': 'jsonapi'
        }
        
        # Dodaj filtere ako postoje
        if filters:
            if 'komercijalista' in filters:
                params['komercijalista'] = filters['komercijalista']
            if 'status' in filters:
                params['status'] = filters['status']
            if 'search' in filters:
                params['search'] = filters['search']
        
        try:
            response = self.session.post(url, json=params, timeout=30)
            
            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    print(f"❌ Odgovor nije valjan JSON: {response.text[:200]}...")
                    return None
            else:
                print(f"❌ Greška pri preuzimanju kupaca. Status kod: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Greška pri API pozivu: {e}")
            return None
    
    def get_all_customers(self, batch_size: int = 200, filters: Optional[Dict] = None, max_iterations: int = 1000) -> List[Dict]:
        """
        Preuzima sve kupce koristeći paginaciju

        Args:
            batch_size: Veličina batch-a za paginaciju (povećano na 200)
            filters: Dodatni filteri
            max_iterations: Maksimalan broj iteracija (sigurnosni mehanizam)

        Returns:
            Lista svih kupaca
        """
        all_customers = []
        start = 0
        iteration = 0

        print("🔄 Pokretanje preuzimanja kupaca...")
        print(f"📊 Batch veličina: {batch_size}")

        while iteration < max_iterations:
            iteration += 1
            print(f"📥 Iteracija {iteration}: Preuzimam kupce {start + 1}-{start + batch_size}...")

            response = self.get_customers(start, batch_size, filters)

            if not response:
                print("❌ Greška pri preuzimanju batch-a kupaca - prekidam")
                break

            # Ekstraktuj kupce iz odgovora
            customers = response.get('data', [])

            # Debug informacije
            meta = response.get('meta', {})
            total_from_api = meta.get('total', 'nepoznato')
            print(f"🔍 API odgovor: {len(customers)} kupaca u batch-u, ukupno prema API: {total_from_api}")

            # Ako nema kupaca, završi
            if not customers or len(customers) == 0:
                print("✅ Nema više kupaca za preuzimanje - završavam")
                break

            all_customers.extend(customers)
            print(f"✅ Preuzeto {len(customers)} kupaca (ukupno do sada: {len(all_customers)})")

            # Ako je broj preuzenih kupaca manji od batch_size, verovatno smo stigli do kraja
            if len(customers) < batch_size:
                print(f"✅ Preuzeto {len(customers)} < {batch_size} (batch_size) - verovatno smo stigli do kraja")
                break

            start += batch_size

            # Dodaj kratku pauzu između zahteva da ne opteretimo server
            import time
            time.sleep(0.1)

        if iteration >= max_iterations:
            print(f"⚠️ Dostignut maksimalan broj iteracija ({max_iterations})")

        print(f"🎯 Završeno! Ukupno preuzeto kupaca: {len(all_customers)}")
        return all_customers
    
    def save_customers_to_json(self, customers: List[Dict], filename: str = None) -> str:
        """
        Čuva kupce u JSON fajl
        
        Args:
            customers: Lista kupaca
            filename: Ime fajla (opciono)
            
        Returns:
            Putanja do sačuvanog fajla
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wings_customers_{timestamp}.json"
        
        # Kreiraj direktorijum ako ne postoji
        os.makedirs("exports", exist_ok=True)
        filepath = os.path.join("exports", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    'export_info': {
                        'timestamp': datetime.now().isoformat(),
                        'total_customers': len(customers),
                        'source': 'Wings Portal API'
                    },
                    'customers': customers
                }, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Kupci sačuvani u: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Greška pri čuvanju fajla: {e}")
            return ""

def main():
    """Glavna funkcija"""
    print("🚀 Wings Portal Customer Downloader")
    print("=" * 50)
    
    # Konfiguracija
    # Za test okruženje koristiti: base_url="https://portal.wings.rs/grosstest"
    downloader = WingsCustomerDownloader(
        base_url="https://portal.wings.rs",
        api_alias="gross"
    )
    
    # Kredencijali - promeni ove vrednosti
    username = input("Unesite korisničko ime: ").strip()
    if not username:
        print("❌ Korisničko ime je obavezno")
        sys.exit(1)
    
    password = input("Unesite lozinku: ").strip()
    if not password:
        print("❌ Lozinka je obavezna")
        sys.exit(1)
    
    # Prijavi se
    if not downloader.login(username, password):
        print("❌ Neuspešna prijava. Proverite kredencijale.")
        sys.exit(1)
    
    # Opcioni filteri
    filters = {}
    
    # Preuzmi sve kupce - povećan batch_size za brže preuzimanje
    print("\n🔧 Konfiguracija preuzimanja:")
    batch_size = 200  # Povećano sa 50 na 200 za brže preuzimanje
    print(f"📦 Batch veličina: {batch_size}")

    customers = downloader.get_all_customers(batch_size=batch_size, filters=filters)
    
    if customers:
        print(f"\n📊 Ukupno preuzeto kupaca: {len(customers)}")
        
        # Sačuvaj u JSON
        filepath = downloader.save_customers_to_json(customers)
        
        if filepath:
            print(f"\n✅ Uspešno završeno!")
            print(f"📁 Fajl: {filepath}")
            print(f"📈 Broj kupaca: {len(customers)}")
        else:
            print("\n❌ Greška pri čuvanju fajla")
    else:
        print("\n❌ Nisu preuzeti kupci")

if __name__ == "__main__":
    main()
