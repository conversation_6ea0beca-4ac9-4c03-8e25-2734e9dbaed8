<?php
/**
 * Diagnostic script for MAX User Management plugin
 * This script checks for common issues that could prevent plugin activation
 */

echo "=== MAX User Management Plugin Diagnostic ===\n\n";

// Check if main plugin file exists
$main_file = 'max-user-management.php';
if (file_exists($main_file)) {
    echo "✓ Main plugin file exists: $main_file\n";
} else {
    echo "✗ Main plugin file missing: $main_file\n";
    exit(1);
}

// Check if required include files exist
$required_files = [
    'includes/class-max-config.php',
    'includes/class-max-wings-api.php',
    'includes/class-max-user-roles.php',
    'includes/class-max-admin-panel.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✓ Required file exists: $file\n";
    } else {
        echo "✗ Required file missing: $file\n";
    }
}

// Check if admin files exist
$admin_files = [
    'admin/css/admin.css',
    'admin/js/admin.js',
    'admin/partials/admin-display.php'
];

foreach ($admin_files as $file) {
    if (file_exists($file)) {
        echo "✓ Admin file exists: $file\n";
    } else {
        echo "✗ Admin file missing: $file\n";
    }
}

// Check if assets files exist (now created)
$asset_files = [
    'assets/css/frontend.css',
    'assets/js/frontend.js'
];

foreach ($asset_files as $file) {
    if (file_exists($file)) {
        echo "✓ Asset file exists: $file\n";
    } else {
        echo "✗ Asset file missing: $file\n";
    }
}

// Check for basic PHP syntax errors by including files
echo "\n=== Checking PHP Syntax ===\n";

// Mock WordPress functions to prevent fatal errors
function mock_wordpress_functions() {
    if (!function_exists('plugin_dir_path')) {
        function plugin_dir_path($file) { return dirname($file) . '/'; }
    }
    if (!function_exists('plugin_dir_url')) {
        function plugin_dir_url($file) { return 'http://example.com/'; }
    }
    if (!function_exists('plugin_basename')) {
        function plugin_basename($file) { return basename($file); }
    }
    if (!function_exists('add_action')) {
        function add_action($hook, $callback) { return true; }
    }
    if (!function_exists('class_exists')) {
        function class_exists($class) { return false; }
    }
    if (!function_exists('get_option')) {
        function get_option($option, $default = false) { return $default; }
    }
    if (!function_exists('update_option')) {
        function update_option($option, $value) { return true; }
    }
    if (!function_exists('add_option')) {
        function add_option($option, $value) { return true; }
    }
    if (!function_exists('flush_rewrite_rules')) {
        function flush_rewrite_rules() { return true; }
    }
    if (!function_exists('wp_clear_scheduled_hook')) {
        function wp_clear_scheduled_hook($hook) { return true; }
    }
    if (!function_exists('register_activation_hook')) {
        function register_activation_hook($file, $callback) { return true; }
    }
    if (!function_exists('register_deactivation_hook')) {
        function register_deactivation_hook($file, $callback) { return true; }
    }
    if (!function_exists('load_plugin_textdomain')) {
        function load_plugin_textdomain($domain, $deprecated, $path) { return true; }
    }
    if (!function_exists('is_admin')) {
        function is_admin() { return true; }
    }
    if (!function_exists('__')) {
        function __($text, $domain = 'default') { return $text; }
    }
    if (!function_exists('_e')) {
        function _e($text, $domain = 'default') { echo $text; }
    }
    if (!function_exists('remove_role')) {
        function remove_role($role) { return true; }
    }
    if (!function_exists('add_role')) {
        function add_role($role, $display_name, $capabilities) { return true; }
    }
    if (!function_exists('array_fill_keys')) {
        function array_fill_keys($keys, $value) { 
            $result = array();
            foreach ($keys as $key) {
                $result[$key] = $value;
            }
            return $result;
        }
    }
    if (!defined('ABSPATH')) {
        define('ABSPATH', '/');
    }
    
    // Mock global variables
    global $wpdb;
    $wpdb = new stdClass();
    $wpdb->prefix = 'wp_';
    
    // Mock WP_Error class
    if (!class_exists('WP_Error')) {
        class WP_Error {
            public function __construct($code, $message) {}
        }
    }
}

mock_wordpress_functions();

// Test syntax of each include file
foreach ($required_files as $file) {
    if (file_exists($file)) {
        try {
            include_once $file;
            echo "✓ Syntax OK: $file\n";
        } catch (ParseError $e) {
            echo "✗ Parse error in $file: " . $e->getMessage() . "\n";
        } catch (Error $e) {
            echo "✓ Syntax OK: $file (runtime error expected: " . $e->getMessage() . ")\n";
        } catch (Exception $e) {
            echo "✓ Syntax OK: $file (exception expected: " . $e->getMessage() . ")\n";
        }
    }
}

echo "\n=== Diagnostic Complete ===\n";
echo "If all files show '✓ Syntax OK', the plugin should activate without fatal errors.\n";
echo "If you see parse errors, those need to be fixed before activation.\n";
