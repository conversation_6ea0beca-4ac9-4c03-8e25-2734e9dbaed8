#!/usr/bin/env python3
"""
Wings Portal API - Customer Data Retrieval
Retrieves JSON data from local.kupac.svi endpoint
"""

import requests
import json
import sys
from typing import Dict, List, Optional

class WingsCustomerRetriever:
    def __init__(self, base_url: str = "https://portal.wings.rs", alias: str = "grosstest"):
        """
        Initialize Wings API client
        
        Args:
            base_url: Wings Portal base URL
            alias: Client alias for API access
        """
        self.base_url = base_url.rstrip('/')
        self.alias = alias
        self.api_url = f"{self.base_url}/api/v1/{self.alias}/"
        self.session = requests.Session()
        self.authenticated = False
        
    def authenticate(self, username: str, password: str) -> bool:
        """
        Authenticate with Wings Portal API
        
        Args:
            username: API username
            password: API password
            
        Returns:
            True if authentication successful, False otherwise
        """
        login_url = f"{self.api_url}system.user.log"
        
        data = {
            'aUn': username,
            'aUp': password
        }
        
        try:
            print(f"🔐 Authenticating with Wings Portal...")
            print(f"   URL: {login_url}")
            print(f"   Username: {username}")
            
            response = self.session.post(
                login_url, 
                data=data,  # Use form data, not JSON
                timeout=30,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            )
            
            print(f"   Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   Response Data: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    # Check if authentication was successful
                    if 'data' in result and result['data']:
                        self.authenticated = True
                        print("✅ Authentication successful!")
                        return True
                    else:
                        print("❌ Authentication failed - no data in response")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON response: {e}")
                    print(f"   Raw response: {response.text[:500]}...")
                    return False
            else:
                print(f"❌ Authentication failed with status code: {response.status_code}")
                print(f"   Response: {response.text[:500]}...")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error during authentication: {e}")
            return False
    
    def get_customers(self, start: int = 0, length: int = 100) -> Optional[Dict]:
        """
        Retrieve customers from Wings Portal
        
        Args:
            start: Starting index for pagination
            length: Number of customers to retrieve
            
        Returns:
            Dictionary with customer data or None if error
        """
        if not self.authenticated:
            print("❌ Not authenticated. Please authenticate first.")
            return None
            
        customers_url = f"{self.api_url}local.kupac.svi"
        
        params = {
            'dStart': start,
            'dLength': length,
            'output': 'jsonapi'
        }
        
        try:
            print(f"📥 Retrieving customers...")
            print(f"   URL: {customers_url}")
            print(f"   Parameters: {params}")
            
            response = self.session.get(customers_url, params=params, timeout=30)
            
            print(f"   Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ Successfully retrieved customer data!")
                    
                    # Print summary
                    if 'data' in result:
                        customer_count = len(result['data']) if isinstance(result['data'], list) else 1
                        print(f"   Found {customer_count} customers")
                    
                    return result
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON response: {e}")
                    print(f"   Raw response: {response.text[:500]}...")
                    return None
            else:
                print(f"❌ Failed to retrieve customers. Status code: {response.status_code}")
                print(f"   Response: {response.text[:500]}...")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error during customer retrieval: {e}")
            return None
    
    def get_all_customers(self, batch_size: int = 100, max_customers: int = 1000) -> List[Dict]:
        """
        Retrieve all customers using pagination
        
        Args:
            batch_size: Number of customers per batch
            max_customers: Maximum number of customers to retrieve
            
        Returns:
            List of all customers
        """
        all_customers = []
        start = 0
        
        while len(all_customers) < max_customers:
            current_batch_size = min(batch_size, max_customers - len(all_customers))
            
            result = self.get_customers(start, current_batch_size)
            
            if not result or 'data' not in result:
                break
                
            customers = result['data']
            if not customers:
                break
                
            all_customers.extend(customers)
            start += len(customers)
            
            print(f"   Retrieved {len(customers)} customers (total: {len(all_customers)})")
            
            # If we got fewer customers than requested, we've reached the end
            if len(customers) < current_batch_size:
                break
        
        return all_customers

def main():
    """
    Main function to retrieve and save customer data
    """
    # Try different configurations from your codebase
    configs = [
        {
            "BASE_URL": "https://portal.wings.rs",
            "ALIAS": "grosstest",  # Alternative alias from wings_portal_sync.py
            "USERNAME": "aql",
            "PASSWORD": "grossaql"
        },
        {
            "BASE_URL": "https://portal.wings.rs",
            "ALIAS": "grossaql",   # Original test alias
            "USERNAME": "aql",
            "PASSWORD": "grossaql"
        }
    ]

    for i, config in enumerate(configs, 1):
        print(f"=== Wings Portal API - Customer Data Retrieval (Config {i}) ===")
        print(f"URL: {config['BASE_URL']}")
        print(f"Alias: {config['ALIAS']}")
        print(f"Username: {config['USERNAME']}")
        print()

        # Initialize API client
        api = WingsCustomerRetriever(config['BASE_URL'], config['ALIAS'])

        # Authenticate
        if api.authenticate(config['USERNAME'], config['PASSWORD']):
            print()

            # Retrieve customers
            customers_data = api.get_customers(start=0, length=50)  # Start with 50 customers

            if customers_data:
                # Save to JSON file
                output_file = f"wings_customers_{config['ALIAS']}.json"

                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(customers_data, f, indent=2, ensure_ascii=False)

                    print(f"💾 Customer data saved to: {output_file}")

                    # Print summary
                    if 'data' in customers_data:
                        customer_count = len(customers_data['data']) if isinstance(customers_data['data'], list) else 1
                        print(f"📊 Total customers retrieved: {customer_count}")

                        # Show first customer as example
                        if customers_data['data'] and len(customers_data['data']) > 0:
                            print("\n📋 Sample customer data:")
                            first_customer = customers_data['data'][0]
                            print(json.dumps(first_customer, indent=2, ensure_ascii=False))

                    return  # Success, exit

                except Exception as e:
                    print(f"❌ Error saving file: {e}")
            else:
                print("❌ No customer data retrieved.")
        else:
            print(f"❌ Authentication failed for config {i}.")

        print("\n" + "="*60 + "\n")

    print("❌ All authentication attempts failed.")

if __name__ == "__main__":
    main()
