<?php
/**
 * Plugin Name: Wings Customer Import
 * Description: Import customers from Wings Portal API to WordPress users with real-time progress
 * Version: 1.0.0
 * Author: Your Name
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Plugin activation
register_activation_hook(__FILE__, 'wings_import_plugin_activate');

function wings_import_plugin_activate() {
    // Create necessary database tables or options if needed
    add_option('wings_import_plugin_activated', true);
}

// Add admin menu
add_action('admin_menu', 'wings_import_plugin_menu');

function wings_import_plugin_menu() {
    add_menu_page(
        'Wings Import',
        'Wings Import',
        'manage_options',
        'wings-import',
        'wings_import_plugin_page',
        'dashicons-download',
        30
    );
}

function wings_import_plugin_page() {
    ?>
    <div class="wrap">
        <h1>🚀 Wings Customer Import</h1>
        
        <div class="notice notice-info">
            <p><strong>Ready to import ~2000 customers from Wings Portal API!</strong></p>
        </div>
        
        <div class="card" style="max-width: 800px;">
            <h2>Import Dashboard</h2>
            <p>Use the real-time dashboard to monitor the import progress:</p>
            
            <p>
                <a href="<?php echo plugins_url('wings_api_import_realtime.php', __FILE__); ?>" 
                   target="_blank" 
                   class="button button-primary button-hero">
                    🚀 Launch Import Dashboard
                </a>
            </p>
            
            <h3>📋 Import Features:</h3>
            <ul>
                <li>✅ Real-time progress tracking</li>
                <li>✅ Live log console</li>
                <li>✅ Error handling and reporting</li>
                <li>✅ Batch processing for large datasets</li>
                <li>✅ Automatic email generation (<EMAIL>)</li>
                <li>✅ Address parsing (36000 Kraljevo → postcode + city)</li>
                <li>✅ Complete Wings data preservation</li>
            </ul>
            
            <h3>⚙️ Configuration Required:</h3>
            <ul>
                <li><strong>Wings Alias:</strong> Your Wings Portal alias</li>
                <li><strong>API Username:</strong> Your Wings API username</li>
                <li><strong>API Password:</strong> Your Wings API password</li>
            </ul>
            
            <div class="notice notice-warning inline">
                <p><strong>Important:</strong> Make sure to backup your database before running the import!</p>
            </div>
        </div>
    </div>
    <?php
}

// Copy the import files to plugin directory on activation
function wings_import_copy_files() {
    $plugin_dir = plugin_dir_path(__FILE__);
    $root_dir = ABSPATH;
    
    // Copy main import file
    if (file_exists($root_dir . 'wings_api_import_realtime.php')) {
        copy($root_dir . 'wings_api_import_realtime.php', $plugin_dir . 'wings_api_import_realtime.php');
    }
}

add_action('admin_init', 'wings_import_copy_files');
?>
